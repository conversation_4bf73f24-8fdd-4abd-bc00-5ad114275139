{"ast": null, "code": "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\nexport default function thresholdFreedmanDiaconis(values, min, max) {\n  const c = count(values),\n    d = quantile(values, 0.75) - quantile(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}", "map": {"version": 3, "names": ["count", "quantile", "thresholdFreedman<PERSON><PERSON><PERSON><PERSON>", "values", "min", "max", "c", "d", "Math", "ceil", "pow"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-array/src/threshold/freedmanDiaconis.js"], "sourcesContent": ["import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function thresholdFreedmanDiaconis(values, min, max) {\n  const c = count(values), d = quantile(values, 0.75) - quantile(values, 0.25);\n  return c && d ? Math.ceil((max - min) / (2 * d * Math.pow(c, -1 / 3))) : 1;\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,QAAQ,MAAM,gBAAgB;AAErC,eAAe,SAASC,yBAAyBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAClE,MAAMC,CAAC,GAAGN,KAAK,CAACG,MAAM,CAAC;IAAEI,CAAC,GAAGN,QAAQ,CAACE,MAAM,EAAE,IAAI,CAAC,GAAGF,QAAQ,CAACE,MAAM,EAAE,IAAI,CAAC;EAC5E,OAAOG,CAAC,IAAIC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,GAAG,GAAGD,GAAG,KAAK,CAAC,GAAGG,CAAC,GAAGC,IAAI,CAACE,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}