#!/usr/bin/env node

const axios = require('axios');
const BotClient = require('./sdk/bot-client');

const API_BASE = 'http://localhost:3000/api';
let authToken = '';
let botToken = '';

async function testChatSystem() {
    console.log('🚀 Testing Discord-like Chat System with Bot Integration\n');

    try {
        // Step 1: Register user
        console.log('=== Step 1: User Registration ===');
        const registerResult = await axios.post(`${API_BASE}/auth/register`, {
            username: 'chatuser',
            email: '<EMAIL>',
            password: 'password123'
        });

        if (registerResult.status === 201) {
            authToken = registerResult.data.token;
            console.log('✅ User registered successfully');
        }

        // Step 2: Create a server
        console.log('\n=== Step 2: Create Server ===');
        const serverResult = await axios.post(`${API_BASE}/servers`, {
            name: 'My Test Server',
            description: 'A test server for bot integration'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const server = serverResult.data.server;
        console.log(`✅ Server created: ${server.name} (ID: ${server.id})`);

        // Step 3: Get channels (should have default general channel)
        console.log('\n=== Step 3: Get Channels ===');
        const channelsResult = await axios.get(`${API_BASE}/servers/${server.id}/channels`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const channels = channelsResult.data.channels;
        console.log(`✅ Found ${channels.length} channels:`);
        channels.forEach(channel => {
            console.log(`   - #${channel.name} (${channel.type})`);
        });

        const generalChannel = channels.find(c => c.name === 'general');

        // Step 4: Create additional channel
        console.log('\n=== Step 4: Create Additional Channel ===');
        const newChannelResult = await axios.post(`${API_BASE}/servers/${server.id}/channels`, {
            name: 'bot-testing',
            type: 'text'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const botChannel = newChannelResult.data.channel;
        console.log(`✅ Created channel: #${botChannel.name}`);

        // Step 5: Create a bot application
        console.log('\n=== Step 5: Create Bot Application ===');
        const botResult = await axios.post(`${API_BASE}/applications`, {
            name: 'ChatBot',
            description: 'A test bot for chat integration'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const bot = botResult.data.application;
        botToken = bot.bot_token;
        console.log(`✅ Bot created: ${bot.name}`);
        console.log(`   Token: ${botToken.substring(0, 8)}...`);

        // Step 6: Create bot code
        console.log('\n=== Step 6: Create Bot Code ===');
        const botCode = `
// Discord-like Chat Bot
const { BotClient } = require('bot-discord-sdk');

const bot = new BotClient({
    token: process.env.BOT_TOKEN,
    intents: ['GUILD_MESSAGES', 'MESSAGE_CONTENT']
});

bot.on('ready', () => {
    console.log(\`Bot \${bot.user.username} is ready for chat!\`);
});

bot.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    
    console.log(\`📨 Message in chat: "\${message.content}"\`);
    
    // Ping command
    if (message.content.toLowerCase() === '!ping') {
        await bot.sendMessage(message.channel_id, 'Pong! 🏓');
    }
    
    // Hello command
    if (message.content.toLowerCase().includes('hello bot')) {
        await bot.sendMessage(message.channel_id, \`Hello \${message.author.username}! 👋 I'm your chat bot!\`);
    }
    
    // Help command
    if (message.content.toLowerCase() === '!help') {
        const helpMessage = \`
**Available Commands:**
• \`!ping\` - Test bot response
• \`!help\` - Show this help message
• \`hello bot\` - Get a greeting
• \`!time\` - Get current time
        \`;
        await bot.sendMessage(message.channel_id, helpMessage);
    }
    
    // Time command
    if (message.content.toLowerCase() === '!time') {
        const now = new Date().toLocaleString();
        await bot.sendMessage(message.channel_id, \`Current time: \${now} ⏰\`);
    }
});

bot.connect();
        `;

        const codeResult = await axios.post(`${API_BASE}/applications/${bot.id}/code`, {
            code: botCode,
            entry_point: 'index.js'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('✅ Bot code created successfully');

        // Step 7: Invite bot to channel
        console.log('\n=== Step 7: Invite Bot to Channel ===');
        const inviteResult = await axios.post(`${API_BASE}/servers/${server.id}/channels/${botChannel.id}/bots`, {
            bot_id: bot.id
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log(`✅ Bot invited to #${botChannel.name}`);

        // Step 8: Send test messages
        console.log('\n=== Step 8: Send Test Messages ===');
        
        const testMessages = [
            'Hello everyone! 👋',
            'Testing the chat system',
            '!ping',
            'hello bot',
            '!help',
            '!time'
        ];

        for (const content of testMessages) {
            const messageResult = await axios.post(`${API_BASE}/servers/${server.id}/channels/${botChannel.id}/messages`, {
                content
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });

            console.log(`📤 Sent: "${content}"`);
            await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
        }

        // Step 9: Get messages
        console.log('\n=== Step 9: Retrieve Messages ===');
        const messagesResult = await axios.get(`${API_BASE}/servers/${server.id}/channels/${botChannel.id}/messages`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const messages = messagesResult.data.messages;
        console.log(`✅ Retrieved ${messages.length} messages:`);
        messages.forEach(msg => {
            const authorType = msg.author.bot ? '[BOT]' : '[USER]';
            console.log(`   ${authorType} ${msg.author.username}: ${msg.content}`);
        });

        // Step 10: Test WebSocket connection with bot
        console.log('\n=== Step 10: Test Bot WebSocket Connection ===');
        console.log('🔌 Connecting bot to WebSocket Gateway...');

        const botClient = new BotClient({
            token: botToken,
            gatewayUrl: 'ws://localhost:3001/gateway',
            apiBaseUrl: API_BASE
        });

        botClient.on('ready', (data) => {
            console.log(`✅ Bot connected: ${data.bot.name}`);
            console.log('🎯 Bot is now listening for real-time messages!');
        });

        botClient.on('messageCreate', async (message) => {
            if (message.author.bot) return;
            
            console.log(`📨 Real-time message: "${message.content}" from ${message.author.username}`);
            
            // Auto-respond to specific messages
            if (message.content.toLowerCase().includes('test bot')) {
                await botClient.sendMessage(message.channel_id, '✅ Bot is working perfectly in real-time chat!');
            }
        });

        botClient.on('error', (error) => {
            console.log('❌ Bot WebSocket error:', error.message);
        });

        await botClient.connect();

        // Step 11: Send a test message to trigger bot response
        console.log('\n=== Step 11: Test Real-time Bot Response ===');
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for bot to be ready

        const realtimeTestResult = await axios.post(`${API_BASE}/servers/${server.id}/channels/${botChannel.id}/messages`, {
            content: 'test bot please respond'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        console.log('📤 Sent real-time test message');

        // Wait a bit for bot response
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Step 12: Final message check
        console.log('\n=== Step 12: Final Message Check ===');
        const finalMessagesResult = await axios.get(`${API_BASE}/servers/${server.id}/channels/${botChannel.id}/messages`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        const finalMessages = finalMessagesResult.data.messages;
        console.log(`✅ Final message count: ${finalMessages.length}`);
        
        const botMessages = finalMessages.filter(msg => msg.author.bot);
        console.log(`🤖 Bot responses: ${botMessages.length}`);

        // Disconnect bot
        botClient.disconnect();

        console.log('\n🎉 Chat System Test Completed Successfully!');
        console.log('\n📊 Test Summary:');
        console.log('- ✅ User Registration');
        console.log('- ✅ Server Creation');
        console.log('- ✅ Channel Management');
        console.log('- ✅ Bot Creation & Code');
        console.log('- ✅ Bot Invitation to Channel');
        console.log('- ✅ Message Sending/Receiving');
        console.log('- ✅ WebSocket Real-time Connection');
        console.log('- ✅ Bot Auto-responses');
        
        console.log('\n🌐 Access your chat system at:');
        console.log('- Dashboard: http://localhost:3001');
        console.log('- Chat Interface: http://localhost:3001/chat');
        console.log('- Code Editor: http://localhost:3001/applications');

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    testChatSystem();
}

module.exports = testChatSystem;
