# 🚀 Discord-like Chat System với Bot Integration

Hệ thống chat hoàn chỉnh giống Discord với khả năng tạo bot, viết code tùy ý, và test real-time!

## ✨ Tính Năng Chính

### 🎯 **Discord-like Chat Interface**
- **Servers & Channels**: Tạo server và channel như Discord
- **Real-time Messaging**: Chat real-time với WebSocket
- **User Management**: Quản lý thành viên server
- **Bot Integration**: Mời bot vào channel và chat

### 🤖 **Bot Management System**
- **Bot Creation**: Tạo bot với token tự động
- **Code Editor**: Monaco Editor với IntelliSense cho Bot SDK
- **Live Testing**: Test bot trong môi trường chat thật
- **One-click Deploy**: Deploy bot với 1 click

### 💻 **Advanced Code Editor**
- **Monaco Editor**: VS Code engine với syntax highlighting
- **Bot SDK IntelliSense**: Auto-completion cho Bot API
- **Templates**: 4 template bot sẵn có (Basic, Advanced, Slash Commands, Event-Driven)
- **Live Console**: Console output real-time
- **Version Control**: Lưu và restore code versions

### 🔄 **Real-time Integration**
- **WebSocket Gateway**: Kết nối real-time cho bot
- **Live Bot Responses**: Bot phản hồi ngay lập tức trong chat
- **Event Handling**: Xử lý events Discord-like
- **Message Broadcasting**: Broadcast tin nhắn real-time

## 🏗️ Kiến Trúc Hệ Thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Chat UI       │    │   Code Editor   │    │   Bot Runtime   │
│   (React)       │    │   (Monaco)      │    │   (WebSocket)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │              Backend API                        │
         │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌────────┐│
         │  │  Chat   │ │   Bot   │ │  Code  │ │Gateway ││
         │  │   API   │ │   API   │ │   API  │ │   WS   ││
         │  └─────────┘ └─────────┘ └─────────┘ └────────┘│
         └─────────────────────────────────────────────────┘
                                 │
         ┌─────────────────────────────────────────────────┐
         │                SQLite Database                  │
         │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌────────┐│
         │  │ Users   │ │Servers  │ │Messages │ │  Bots  ││
         │  │Channels │ │ Code    │ │ Tokens  │ │ Logs   ││
         │  └─────────┘ └─────────┘ └─────────┘ └────────┘│
         └─────────────────────────────────────────────────┘
```

## 🚀 Cách Chạy Hệ Thống

### **Bước 1: Cài Đặt Dependencies**
```bash
# Backend dependencies
npm install

# Frontend dependencies
cd dashboard
npm install
```

### **Bước 2: Chạy Backend Server**
```bash
# Từ thư mục root
npm start
```
Server sẽ chạy trên: http://localhost:3000

### **Bước 3: Chạy Dashboard**
```bash
# Terminal mới
npm run dashboard:start
```
Dashboard sẽ chạy trên: http://localhost:3001

### **Bước 4: Truy Cập Hệ Thống**
- **Dashboard**: http://localhost:3001
- **Chat Interface**: http://localhost:3001/chat
- **Code Editor**: http://localhost:3001/applications

## 🎮 Hướng Dẫn Sử Dụng

### **1. Tạo Account và Server**
1. Đăng ký tài khoản tại http://localhost:3001/register
2. Đăng nhập và vào **Chat** tab
3. Tạo server mới và channel

### **2. Tạo Bot**
1. Vào **Applications** → **Create New Bot**
2. Nhập tên và mô tả bot
3. Copy **Bot Token** (sẽ cần dùng)

### **3. Viết Code Bot**
1. Click **Edit Code** trên bot vừa tạo
2. Chọn template hoặc viết code từ đầu
3. Sử dụng IntelliSense cho Bot SDK:

```javascript
// Bot SDK có sẵn
bot.sendMessage(channelId, message)
bot.on('messageCreate', handler)
bot.storage.set(key, value)
bot.storage.get(key)
bot.registerCommand(name, handler)
```

### **4. Test Bot trong Chat**
1. Vào **Chat** → Chọn server/channel
2. Click **Invite Bot** → Chọn bot của bạn
3. Bot sẽ xuất hiện trong channel
4. Test các lệnh bot trong chat real-time!

### **5. Deploy Bot**
1. Trong Code Editor, click **Deploy**
2. Bot sẽ được deploy và chạy 24/7
3. Monitor logs và performance

## 🤖 Bot SDK Reference

### **Basic Bot Template**
```javascript
const { BotClient } = require('bot-discord-sdk');

const bot = new BotClient({
    token: process.env.BOT_TOKEN,
    intents: ['GUILD_MESSAGES', 'MESSAGE_CONTENT']
});

bot.on('ready', () => {
    console.log('Bot is ready!');
});

bot.on('messageCreate', async (message) => {
    if (message.author.bot) return;
    
    if (message.content === '!ping') {
        await bot.sendMessage(message.channel_id, 'Pong!');
    }
});

bot.connect();
```

### **Advanced Features**
```javascript
// Storage
await bot.storage.set('user_count', 100);
const count = await bot.storage.get('user_count');

// Commands
bot.registerCommand('hello', async (context) => {
    await context.reply(`Hello ${context.user.username}!`);
});

// Events
bot.on('memberJoin', async (member) => {
    const channel = await bot.getDefaultChannel(member.guild_id);
    await bot.sendMessage(channel.id, `Welcome ${member.username}!`);
});
```

## 🧪 Test Hệ Thống

Chạy script test tự động:
```bash
node test-chat-system.js
```

Script sẽ test:
- ✅ Tạo user, server, channel
- ✅ Tạo bot và code
- ✅ Invite bot vào channel
- ✅ Gửi tin nhắn và nhận phản hồi
- ✅ WebSocket real-time connection
- ✅ Bot auto-responses

## 📁 Cấu Trúc Project

```
bot_fake_discord/
├── src/                     # Backend source
│   ├── api/                 # API routes
│   │   ├── chat.js         # Chat API (servers, channels, messages)
│   │   ├── applications.js  # Bot management
│   │   └── bot-code.js     # Code editor API
│   ├── gateway/            # WebSocket gateway
│   └── database/           # Database schema & connection
├── dashboard/              # Frontend React app
│   └── src/
│       ├── components/
│       │   ├── Chat/       # Discord-like chat interface
│       │   └── CodeEditor/ # Monaco editor with IntelliSense
│       └── services/       # API services
├── sdk/                    # Bot SDK
│   └── bot-client.js      # Bot client library
└── examples/              # Bot examples
```

## 🔧 API Endpoints

### **Chat API**
- `GET /api/servers` - Lấy danh sách server
- `POST /api/servers` - Tạo server mới
- `GET /api/servers/:id/channels` - Lấy channels
- `POST /api/servers/:id/channels` - Tạo channel
- `GET /api/servers/:id/channels/:id/messages` - Lấy tin nhắn
- `POST /api/servers/:id/channels/:id/messages` - Gửi tin nhắn
- `POST /api/servers/:id/channels/:id/bots` - Mời bot

### **Bot API**
- `GET /api/applications` - Lấy danh sách bot
- `POST /api/applications` - Tạo bot mới
- `GET /api/applications/:id/code` - Lấy code bot
- `POST /api/applications/:id/code` - Lưu code bot
- `POST /api/applications/:id/code/:id/deploy` - Deploy bot

## 🌟 Tính Năng Nâng Cao

### **Real-time Features**
- Live chat với WebSocket
- Bot responses real-time
- Online user status
- Typing indicators (planned)

### **Code Editor Features**
- Monaco Editor (VS Code engine)
- IntelliSense cho Bot SDK
- Auto-save mỗi 2 giây
- Version control và history
- Live testing environment

### **Bot Features**
- Multiple bot templates
- Custom command system
- Persistent storage
- Event handling
- Scheduled tasks (planned)

## 🎯 Roadmap

- [ ] Voice channels
- [ ] File uploads
- [ ] Bot permissions system
- [ ] Slash commands
- [ ] Bot marketplace
- [ ] Real-time collaboration
- [ ] Mobile app

## 🤝 Đóng Góp

1. Fork project
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## 📞 Hỗ Trợ

Nếu gặp vấn đề:
1. Check console logs
2. Restart servers
3. Clear browser cache
4. Check database connection

**Happy Coding! 🚀**
