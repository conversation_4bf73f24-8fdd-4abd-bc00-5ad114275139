{"ast": null, "code": "import appearance from \"./appearance.js\";\nimport { sum } from \"./ascending.js\";\nexport default function (series) {\n  var n = series.length,\n    i,\n    j,\n    sums = series.map(sum),\n    order = appearance(series),\n    top = 0,\n    bottom = 0,\n    tops = [],\n    bottoms = [];\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n  return bottoms.reverse().concat(tops);\n}", "map": {"version": 3, "names": ["appearance", "sum", "series", "n", "length", "i", "j", "sums", "map", "order", "top", "bottom", "tops", "bottoms", "push", "reverse", "concat"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-shape/src/order/insideOut.js"], "sourcesContent": ["import appearance from \"./appearance.js\";\nimport {sum} from \"./ascending.js\";\n\nexport default function(series) {\n  var n = series.length,\n      i,\n      j,\n      sums = series.map(sum),\n      order = appearance(series),\n      top = 0,\n      bottom = 0,\n      tops = [],\n      bottoms = [];\n\n  for (i = 0; i < n; ++i) {\n    j = order[i];\n    if (top < bottom) {\n      top += sums[j];\n      tops.push(j);\n    } else {\n      bottom += sums[j];\n      bottoms.push(j);\n    }\n  }\n\n  return bottoms.reverse().concat(tops);\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,iBAAiB;AACxC,SAAQC,GAAG,QAAO,gBAAgB;AAElC,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;IACjBC,CAAC;IACDC,CAAC;IACDC,IAAI,GAAGL,MAAM,CAACM,GAAG,CAACP,GAAG,CAAC;IACtBQ,KAAK,GAAGT,UAAU,CAACE,MAAM,CAAC;IAC1BQ,GAAG,GAAG,CAAC;IACPC,MAAM,GAAG,CAAC;IACVC,IAAI,GAAG,EAAE;IACTC,OAAO,GAAG,EAAE;EAEhB,KAAKR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;IACtBC,CAAC,GAAGG,KAAK,CAACJ,CAAC,CAAC;IACZ,IAAIK,GAAG,GAAGC,MAAM,EAAE;MAChBD,GAAG,IAAIH,IAAI,CAACD,CAAC,CAAC;MACdM,IAAI,CAACE,IAAI,CAACR,CAAC,CAAC;IACd,CAAC,MAAM;MACLK,MAAM,IAAIJ,IAAI,CAACD,CAAC,CAAC;MACjBO,OAAO,CAACC,IAAI,CAACR,CAAC,CAAC;IACjB;EACF;EAEA,OAAOO,OAAO,CAACE,OAAO,CAAC,CAAC,CAACC,MAAM,CAACJ,IAAI,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}