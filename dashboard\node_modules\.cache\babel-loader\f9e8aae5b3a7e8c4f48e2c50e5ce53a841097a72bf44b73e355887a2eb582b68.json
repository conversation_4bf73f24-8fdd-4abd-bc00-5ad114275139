{"ast": null, "code": "export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0:\n      break;\n    case 1:\n      this.range(domain);\n      break;\n    default:\n      this.range(range).domain(domain);\n      break;\n  }\n  return this;\n}\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0:\n      break;\n    case 1:\n      {\n        if (typeof domain === \"function\") this.interpolator(domain);else this.range(domain);\n        break;\n      }\n    default:\n      {\n        this.domain(domain);\n        if (typeof interpolator === \"function\") this.interpolator(interpolator);else this.range(interpolator);\n        break;\n      }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["initRange", "domain", "range", "arguments", "length", "initInterpolator", "interpolator"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-scale/src/init.js"], "sourcesContent": ["export function initRange(domain, range) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: this.range(domain); break;\n    default: this.range(range).domain(domain); break;\n  }\n  return this;\n}\n\nexport function initInterpolator(domain, interpolator) {\n  switch (arguments.length) {\n    case 0: break;\n    case 1: {\n      if (typeof domain === \"function\") this.interpolator(domain);\n      else this.range(domain);\n      break;\n    }\n    default: {\n      this.domain(domain);\n      if (typeof interpolator === \"function\") this.interpolator(interpolator);\n      else this.range(interpolator);\n      break;\n    }\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACvC,QAAQC,SAAS,CAACC,MAAM;IACtB,KAAK,CAAC;MAAE;IACR,KAAK,CAAC;MAAE,IAAI,CAACF,KAAK,CAACD,MAAM,CAAC;MAAE;IAC5B;MAAS,IAAI,CAACC,KAAK,CAACA,KAAK,CAAC,CAACD,MAAM,CAACA,MAAM,CAAC;MAAE;EAC7C;EACA,OAAO,IAAI;AACb;AAEA,OAAO,SAASI,gBAAgBA,CAACJ,MAAM,EAAEK,YAAY,EAAE;EACrD,QAAQH,SAAS,CAACC,MAAM;IACtB,KAAK,CAAC;MAAE;IACR,KAAK,CAAC;MAAE;QACN,IAAI,OAAOH,MAAM,KAAK,UAAU,EAAE,IAAI,CAACK,YAAY,CAACL,MAAM,CAAC,CAAC,KACvD,IAAI,CAACC,KAAK,CAACD,MAAM,CAAC;QACvB;MACF;IACA;MAAS;QACP,IAAI,CAACA,MAAM,CAACA,MAAM,CAAC;QACnB,IAAI,OAAOK,YAAY,KAAK,UAAU,EAAE,IAAI,CAACA,YAAY,CAACA,YAAY,CAAC,CAAC,KACnE,IAAI,CAACJ,KAAK,CAACI,YAAY,CAAC;QAC7B;MACF;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}