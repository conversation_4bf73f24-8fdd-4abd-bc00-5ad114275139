{"ast": null, "code": "import { epsilon } from \"../math.js\";\nimport { <PERSON> } from \"./cardinal.js\";\nexport function point(that, x, y) {\n  var x1 = that._x1,\n    y1 = that._y1,\n    x2 = that._x2,\n    y2 = that._y2;\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n      n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n      m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\nCatmullRom.prototype = {\n  areaStart: function () {\n    this._line = 0;\n  },\n  areaEnd: function () {\n    this._line = NaN;\n  },\n  lineStart: function () {\n    this._x0 = this._x1 = this._x2 = this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a = this._l01_2a = this._l12_2a = this._l23_2a = this._point = 0;\n  },\n  lineEnd: function () {\n    switch (this._point) {\n      case 2:\n        this._context.lineTo(this._x2, this._y2);\n        break;\n      case 3:\n        this.point(this._x2, this._y2);\n        break;\n    }\n    if (this._line || this._line !== 0 && this._point === 1) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function (x, y) {\n    x = +x, y = +y;\n    if (this._point) {\n      var x23 = this._x2 - x,\n        y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n    switch (this._point) {\n      case 0:\n        this._point = 1;\n        this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y);\n        break;\n      case 1:\n        this._point = 2;\n        break;\n      case 2:\n        this._point = 3;\n      // falls through\n      default:\n        point(this, x, y);\n        break;\n    }\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\nexport default (function custom(alpha) {\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n  catmullRom.alpha = function (alpha) {\n    return custom(+alpha);\n  };\n  return catmullRom;\n})(0.5);", "map": {"version": 3, "names": ["epsilon", "<PERSON>", "point", "that", "x", "y", "x1", "_x1", "y1", "_y1", "x2", "_x2", "y2", "_y2", "_l01_a", "a", "_l01_2a", "_l12_a", "_l12_2a", "n", "_x0", "_y0", "_l23_a", "b", "_l23_2a", "m", "_context", "bezierCurveTo", "CatmullRom", "context", "alpha", "_alpha", "prototype", "areaStart", "_line", "areaEnd", "NaN", "lineStart", "_point", "lineEnd", "lineTo", "closePath", "x23", "y23", "Math", "sqrt", "pow", "moveTo", "custom", "catmullRom"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-shape/src/curve/catmullRom.js"], "sourcesContent": ["import {epsilon} from \"../math.js\";\nimport {<PERSON>} from \"./cardinal.js\";\n\nexport function point(that, x, y) {\n  var x1 = that._x1,\n      y1 = that._y1,\n      x2 = that._x2,\n      y2 = that._y2;\n\n  if (that._l01_a > epsilon) {\n    var a = 2 * that._l01_2a + 3 * that._l01_a * that._l12_a + that._l12_2a,\n        n = 3 * that._l01_a * (that._l01_a + that._l12_a);\n    x1 = (x1 * a - that._x0 * that._l12_2a + that._x2 * that._l01_2a) / n;\n    y1 = (y1 * a - that._y0 * that._l12_2a + that._y2 * that._l01_2a) / n;\n  }\n\n  if (that._l23_a > epsilon) {\n    var b = 2 * that._l23_2a + 3 * that._l23_a * that._l12_a + that._l12_2a,\n        m = 3 * that._l23_a * (that._l23_a + that._l12_a);\n    x2 = (x2 * b + that._x1 * that._l23_2a - x * that._l12_2a) / m;\n    y2 = (y2 * b + that._y1 * that._l23_2a - y * that._l12_2a) / m;\n  }\n\n  that._context.bezierCurveTo(x1, y1, x2, y2, that._x2, that._y2);\n}\n\nfunction CatmullRom(context, alpha) {\n  this._context = context;\n  this._alpha = alpha;\n}\n\nCatmullRom.prototype = {\n  areaStart: function() {\n    this._line = 0;\n  },\n  areaEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._x0 = this._x1 = this._x2 =\n    this._y0 = this._y1 = this._y2 = NaN;\n    this._l01_a = this._l12_a = this._l23_a =\n    this._l01_2a = this._l12_2a = this._l23_2a =\n    this._point = 0;\n  },\n  lineEnd: function() {\n    switch (this._point) {\n      case 2: this._context.lineTo(this._x2, this._y2); break;\n      case 3: this.point(this._x2, this._y2); break;\n    }\n    if (this._line || (this._line !== 0 && this._point === 1)) this._context.closePath();\n    this._line = 1 - this._line;\n  },\n  point: function(x, y) {\n    x = +x, y = +y;\n\n    if (this._point) {\n      var x23 = this._x2 - x,\n          y23 = this._y2 - y;\n      this._l23_a = Math.sqrt(this._l23_2a = Math.pow(x23 * x23 + y23 * y23, this._alpha));\n    }\n\n    switch (this._point) {\n      case 0: this._point = 1; this._line ? this._context.lineTo(x, y) : this._context.moveTo(x, y); break;\n      case 1: this._point = 2; break;\n      case 2: this._point = 3; // falls through\n      default: point(this, x, y); break;\n    }\n\n    this._l01_a = this._l12_a, this._l12_a = this._l23_a;\n    this._l01_2a = this._l12_2a, this._l12_2a = this._l23_2a;\n    this._x0 = this._x1, this._x1 = this._x2, this._x2 = x;\n    this._y0 = this._y1, this._y1 = this._y2, this._y2 = y;\n  }\n};\n\nexport default (function custom(alpha) {\n\n  function catmullRom(context) {\n    return alpha ? new CatmullRom(context, alpha) : new Cardinal(context, 0);\n  }\n\n  catmullRom.alpha = function(alpha) {\n    return custom(+alpha);\n  };\n\n  return catmullRom;\n})(0.5);\n"], "mappings": "AAAA,SAAQA,OAAO,QAAO,YAAY;AAClC,SAAQC,QAAQ,QAAO,eAAe;AAEtC,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAChC,IAAIC,EAAE,GAAGH,IAAI,CAACI,GAAG;IACbC,EAAE,GAAGL,IAAI,CAACM,GAAG;IACbC,EAAE,GAAGP,IAAI,CAACQ,GAAG;IACbC,EAAE,GAAGT,IAAI,CAACU,GAAG;EAEjB,IAAIV,IAAI,CAACW,MAAM,GAAGd,OAAO,EAAE;IACzB,IAAIe,CAAC,GAAG,CAAC,GAAGZ,IAAI,CAACa,OAAO,GAAG,CAAC,GAAGb,IAAI,CAACW,MAAM,GAAGX,IAAI,CAACc,MAAM,GAAGd,IAAI,CAACe,OAAO;MACnEC,CAAC,GAAG,CAAC,GAAGhB,IAAI,CAACW,MAAM,IAAIX,IAAI,CAACW,MAAM,GAAGX,IAAI,CAACc,MAAM,CAAC;IACrDX,EAAE,GAAG,CAACA,EAAE,GAAGS,CAAC,GAAGZ,IAAI,CAACiB,GAAG,GAAGjB,IAAI,CAACe,OAAO,GAAGf,IAAI,CAACQ,GAAG,GAAGR,IAAI,CAACa,OAAO,IAAIG,CAAC;IACrEX,EAAE,GAAG,CAACA,EAAE,GAAGO,CAAC,GAAGZ,IAAI,CAACkB,GAAG,GAAGlB,IAAI,CAACe,OAAO,GAAGf,IAAI,CAACU,GAAG,GAAGV,IAAI,CAACa,OAAO,IAAIG,CAAC;EACvE;EAEA,IAAIhB,IAAI,CAACmB,MAAM,GAAGtB,OAAO,EAAE;IACzB,IAAIuB,CAAC,GAAG,CAAC,GAAGpB,IAAI,CAACqB,OAAO,GAAG,CAAC,GAAGrB,IAAI,CAACmB,MAAM,GAAGnB,IAAI,CAACc,MAAM,GAAGd,IAAI,CAACe,OAAO;MACnEO,CAAC,GAAG,CAAC,GAAGtB,IAAI,CAACmB,MAAM,IAAInB,IAAI,CAACmB,MAAM,GAAGnB,IAAI,CAACc,MAAM,CAAC;IACrDP,EAAE,GAAG,CAACA,EAAE,GAAGa,CAAC,GAAGpB,IAAI,CAACI,GAAG,GAAGJ,IAAI,CAACqB,OAAO,GAAGpB,CAAC,GAAGD,IAAI,CAACe,OAAO,IAAIO,CAAC;IAC9Db,EAAE,GAAG,CAACA,EAAE,GAAGW,CAAC,GAAGpB,IAAI,CAACM,GAAG,GAAGN,IAAI,CAACqB,OAAO,GAAGnB,CAAC,GAAGF,IAAI,CAACe,OAAO,IAAIO,CAAC;EAChE;EAEAtB,IAAI,CAACuB,QAAQ,CAACC,aAAa,CAACrB,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAEE,EAAE,EAAET,IAAI,CAACQ,GAAG,EAAER,IAAI,CAACU,GAAG,CAAC;AACjE;AAEA,SAASe,UAAUA,CAACC,OAAO,EAAEC,KAAK,EAAE;EAClC,IAAI,CAACJ,QAAQ,GAAGG,OAAO;EACvB,IAAI,CAACE,MAAM,GAAGD,KAAK;AACrB;AAEAF,UAAU,CAACI,SAAS,GAAG;EACrBC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB,CAAC;EACDC,SAAS,EAAE,SAAAA,CAAA,EAAW;IACpB,IAAI,CAACjB,GAAG,GAAG,IAAI,CAACb,GAAG,GAAG,IAAI,CAACI,GAAG,GAC9B,IAAI,CAACU,GAAG,GAAG,IAAI,CAACZ,GAAG,GAAG,IAAI,CAACI,GAAG,GAAGuB,GAAG;IACpC,IAAI,CAACtB,MAAM,GAAG,IAAI,CAACG,MAAM,GAAG,IAAI,CAACK,MAAM,GACvC,IAAI,CAACN,OAAO,GAAG,IAAI,CAACE,OAAO,GAAG,IAAI,CAACM,OAAO,GAC1C,IAAI,CAACc,MAAM,GAAG,CAAC;EACjB,CAAC;EACDC,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,QAAQ,IAAI,CAACD,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACZ,QAAQ,CAACc,MAAM,CAAC,IAAI,CAAC7B,GAAG,EAAE,IAAI,CAACE,GAAG,CAAC;QAAE;MAClD,KAAK,CAAC;QAAE,IAAI,CAACX,KAAK,CAAC,IAAI,CAACS,GAAG,EAAE,IAAI,CAACE,GAAG,CAAC;QAAE;IAC1C;IACA,IAAI,IAAI,CAACqB,KAAK,IAAK,IAAI,CAACA,KAAK,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,KAAK,CAAE,EAAE,IAAI,CAACZ,QAAQ,CAACe,SAAS,CAAC,CAAC;IACpF,IAAI,CAACP,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK;EAC7B,CAAC;EACDhC,KAAK,EAAE,SAAAA,CAASE,CAAC,EAAEC,CAAC,EAAE;IACpBD,CAAC,GAAG,CAACA,CAAC,EAAEC,CAAC,GAAG,CAACA,CAAC;IAEd,IAAI,IAAI,CAACiC,MAAM,EAAE;MACf,IAAII,GAAG,GAAG,IAAI,CAAC/B,GAAG,GAAGP,CAAC;QAClBuC,GAAG,GAAG,IAAI,CAAC9B,GAAG,GAAGR,CAAC;MACtB,IAAI,CAACiB,MAAM,GAAGsB,IAAI,CAACC,IAAI,CAAC,IAAI,CAACrB,OAAO,GAAGoB,IAAI,CAACE,GAAG,CAACJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,EAAE,IAAI,CAACZ,MAAM,CAAC,CAAC;IACtF;IAEA,QAAQ,IAAI,CAACO,MAAM;MACjB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;QAAE,IAAI,CAACJ,KAAK,GAAG,IAAI,CAACR,QAAQ,CAACc,MAAM,CAACpC,CAAC,EAAEC,CAAC,CAAC,GAAG,IAAI,CAACqB,QAAQ,CAACqB,MAAM,CAAC3C,CAAC,EAAEC,CAAC,CAAC;QAAE;MAC/F,KAAK,CAAC;QAAE,IAAI,CAACiC,MAAM,GAAG,CAAC;QAAE;MACzB,KAAK,CAAC;QAAE,IAAI,CAACA,MAAM,GAAG,CAAC;MAAE;MACzB;QAASpC,KAAK,CAAC,IAAI,EAAEE,CAAC,EAAEC,CAAC,CAAC;QAAE;IAC9B;IAEA,IAAI,CAACS,MAAM,GAAG,IAAI,CAACG,MAAM,EAAE,IAAI,CAACA,MAAM,GAAG,IAAI,CAACK,MAAM;IACpD,IAAI,CAACN,OAAO,GAAG,IAAI,CAACE,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,IAAI,CAACM,OAAO;IACxD,IAAI,CAACJ,GAAG,GAAG,IAAI,CAACb,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACI,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGP,CAAC;IACtD,IAAI,CAACiB,GAAG,GAAG,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG,IAAI,CAACI,GAAG,EAAE,IAAI,CAACA,GAAG,GAAGR,CAAC;EACxD;AACF,CAAC;AAED,eAAe,CAAC,SAAS2C,MAAMA,CAAClB,KAAK,EAAE;EAErC,SAASmB,UAAUA,CAACpB,OAAO,EAAE;IAC3B,OAAOC,KAAK,GAAG,IAAIF,UAAU,CAACC,OAAO,EAAEC,KAAK,CAAC,GAAG,IAAI7B,QAAQ,CAAC4B,OAAO,EAAE,CAAC,CAAC;EAC1E;EAEAoB,UAAU,CAACnB,KAAK,GAAG,UAASA,KAAK,EAAE;IACjC,OAAOkB,MAAM,CAAC,CAAClB,KAAK,CAAC;EACvB,CAAC;EAED,OAAOmB,UAAU;AACnB,CAAC,EAAE,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}