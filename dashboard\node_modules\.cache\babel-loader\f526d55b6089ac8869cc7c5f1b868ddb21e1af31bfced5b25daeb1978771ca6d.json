{"ast": null, "code": "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (max < value || max === undefined && value >= value)) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}", "map": {"version": 3, "names": ["max", "values", "valueof", "undefined", "value", "index"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-array/src/max.js"], "sourcesContent": ["export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n"], "mappings": "AAAA,eAAe,SAASA,GAAGA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC3C,IAAIF,GAAG;EACP,IAAIE,OAAO,KAAKC,SAAS,EAAE;IACzB,KAAK,MAAMC,KAAK,IAAIH,MAAM,EAAE;MAC1B,IAAIG,KAAK,IAAI,IAAI,KACTJ,GAAG,GAAGI,KAAK,IAAKJ,GAAG,KAAKG,SAAS,IAAIC,KAAK,IAAIA,KAAM,CAAC,EAAE;QAC7DJ,GAAG,GAAGI,KAAK;MACb;IACF;EACF,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAID,KAAK,IAAIH,MAAM,EAAE;MACxB,IAAI,CAACG,KAAK,GAAGF,OAAO,CAACE,KAAK,EAAE,EAAEC,KAAK,EAAEJ,MAAM,CAAC,KAAK,IAAI,KAC7CD,GAAG,GAAGI,KAAK,IAAKJ,GAAG,KAAKG,SAAS,IAAIC,KAAK,IAAIA,KAAM,CAAC,EAAE;QAC7DJ,GAAG,GAAGI,KAAK;MACb;IACF;EACF;EACA,OAAOJ,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}