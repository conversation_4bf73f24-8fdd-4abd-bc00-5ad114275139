{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bot_fake_discord\\\\dashboard\\\\src\\\\components\\\\CodeEditor\\\\CodeEditor.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Box, Typography, Card, CardContent, Button, Grid, Paper, Tabs, Tab, IconButton, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Chip, Alert, CircularProgress, Tooltip, Divider } from '@mui/material';\nimport { Save as SaveIcon, PlayArrow as TestIcon, Publish as DeployIcon, Settings as SettingsIcon, History as HistoryIcon, FolderOpen as TemplateIcon, BugReport as DebugIcon, Refresh as RefreshIcon, MoreVert as MoreIcon, Add as AddIcon, Close as CloseIcon } from '@mui/icons-material';\nimport Editor from '@monaco-editor/react';\nimport toast from 'react-hot-toast';\n\n// Import our custom components\nimport CodeTemplateSelector from './CodeTemplateSelector';\nimport LiveConsole from './LiveConsole';\nimport FileExplorer from './FileExplorer';\nimport CodeHistory from './CodeHistory';\nimport DeploymentPanel from './DeploymentPanel';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst CodeEditor = () => {\n  _s();\n  const {\n    id: applicationId,\n    codeId\n  } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Get bot context from navigation state\n  const botContext = location.state || {};\n\n  // State management\n  const [code, setCode] = useState('');\n  const [originalCode, setOriginalCode] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [isTesting, setIsTesting] = useState(false);\n  const [isDeploying, setIsDeploying] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // UI State\n  const [activeTab, setActiveTab] = useState(0);\n  const [showTemplateDialog, setShowTemplateDialog] = useState(false);\n  const [showHistoryDialog, setShowHistoryDialog] = useState(false);\n  const [showDeployDialog, setShowDeployDialog] = useState(false);\n  const [settingsMenuAnchor, setSettingsMenuAnchor] = useState(null);\n\n  // Editor state\n  const [editorTheme, setEditorTheme] = useState('vs-dark');\n  const [fontSize, setFontSize] = useState(14);\n  const [wordWrap, setWordWrap] = useState('on');\n  const [minimap, setMinimap] = useState(false);\n\n  // Console and testing\n  const [consoleLogs, setConsoleLogs] = useState([]);\n  const [testResults, setTestResults] = useState(null);\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  // File management\n  const [files, setFiles] = useState([]);\n  const [currentFile, setCurrentFile] = useState(null);\n\n  // Code versions and history\n  const [codeVersions, setCodeVersions] = useState([]);\n  const [currentVersion, setCurrentVersion] = useState(null);\n\n  // Refs\n  const editorRef = useRef(null);\n  const monacoRef = useRef(null);\n\n  // Auto-save timer\n  const autoSaveTimerRef = useRef(null);\n\n  // Load initial data\n  useEffect(() => {\n    loadCodeData();\n    loadCodeVersions();\n  }, [applicationId, codeId]);\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges && code !== originalCode) {\n      if (autoSaveTimerRef.current) {\n        clearTimeout(autoSaveTimerRef.current);\n      }\n      autoSaveTimerRef.current = setTimeout(() => {\n        handleAutoSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n    }\n    return () => {\n      if (autoSaveTimerRef.current) {\n        clearTimeout(autoSaveTimerRef.current);\n      }\n    };\n  }, [code, hasUnsavedChanges, originalCode]);\n  const loadCodeData = async () => {\n    try {\n      setIsLoading(true);\n      let response;\n      if (codeId) {\n        // Load specific code version\n        response = await fetch(`/api/applications/${applicationId}/code/${codeId}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n      } else {\n        // Load latest code or create new\n        const codesResponse = await fetch(`/api/applications/${applicationId}/code`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n        if (codesResponse.ok) {\n          const codesData = await codesResponse.json();\n          if (codesData.codes && codesData.codes.length > 0) {\n            // Use the latest draft or deployed version\n            const latestCode = codesData.codes.find(c => c.status === 'draft') || codesData.codes[0];\n            response = await fetch(`/api/applications/${applicationId}/code/${latestCode.id}`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              }\n            });\n          } else {\n            // No code exists, start with template\n            setShowTemplateDialog(true);\n            setIsLoading(false);\n            return;\n          }\n        }\n      }\n      if (response && response.ok) {\n        const data = await response.json();\n        const codeData = data.code;\n        setCode(codeData.code || '');\n        setOriginalCode(codeData.code || '');\n        setCurrentVersion(codeData);\n        setHasUnsavedChanges(false);\n\n        // Set validation errors if any\n        if (codeData.validation_errors) {\n          setValidationErrors(codeData.validation_errors);\n        }\n      } else {\n        toast.error('Failed to load code');\n      }\n    } catch (error) {\n      console.error('Error loading code:', error);\n      toast.error('Failed to load code');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const loadCodeVersions = async () => {\n    try {\n      const response = await fetch(`/api/applications/${applicationId}/code`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setCodeVersions(data.codes || []);\n      }\n    } catch (error) {\n      console.error('Error loading code versions:', error);\n    }\n  };\n  const handleAutoSave = async () => {\n    if (!hasUnsavedChanges || !currentVersion) return;\n    try {\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          code: code\n        })\n      });\n      if (response.ok) {\n        setOriginalCode(code);\n        setHasUnsavedChanges(false);\n        toast.success('Auto-saved', {\n          duration: 1000\n        });\n      }\n    } catch (error) {\n      console.error('Auto-save failed:', error);\n    }\n  };\n  const handleSave = async () => {\n    try {\n      setIsSaving(true);\n      let response;\n      if (currentVersion && currentVersion.status === 'draft') {\n        // Update existing draft\n        response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify({\n            code: code\n          })\n        });\n      } else {\n        // Create new version\n        response = await fetch(`/api/applications/${applicationId}/code`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify({\n            code: code,\n            entry_point: 'index.js'\n          })\n        });\n      }\n      if (response.ok) {\n        const data = await response.json();\n        setCurrentVersion(data.code);\n        setOriginalCode(code);\n        setHasUnsavedChanges(false);\n        if (data.validation && !data.validation.isValid) {\n          setValidationErrors(data.validation.errors || []);\n          toast.error('Code saved with validation errors');\n        } else {\n          setValidationErrors([]);\n          toast.success('Code saved successfully');\n        }\n\n        // Reload versions\n        loadCodeVersions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to save code');\n        if (errorData.validation_errors) {\n          setValidationErrors(errorData.validation_errors);\n        }\n      }\n    } catch (error) {\n      console.error('Error saving code:', error);\n      toast.error('Failed to save code');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n  const handleTest = async () => {\n    try {\n      setIsTesting(true);\n      setConsoleLogs([]);\n      setTestResults(null);\n\n      // First save the code if there are unsaved changes\n      if (hasUnsavedChanges) {\n        await handleSave();\n      }\n      if (!currentVersion) {\n        toast.error('Please save the code first');\n        return;\n      }\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/test`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          test_input: {\n            message: {\n              content: '!ping',\n              channel_id: 'test-channel-1',\n              author: {\n                id: 'test-user',\n                username: 'TestUser'\n              }\n            }\n          }\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setTestResults(data.result);\n\n        // Add test results to console\n        addToConsole('=== Test Results ===', 'info');\n        addToConsole(JSON.stringify(data.result, null, 2), 'log');\n        toast.success('Code test completed');\n      } else {\n        const errorData = await response.json();\n        setTestResults({\n          error: errorData.message\n        });\n        addToConsole(`Test Error: ${errorData.message}`, 'error');\n        toast.error('Code test failed');\n      }\n    } catch (error) {\n      console.error('Error testing code:', error);\n      addToConsole(`Test Error: ${error.message}`, 'error');\n      toast.error('Failed to test code');\n    } finally {\n      setIsTesting(false);\n    }\n  };\n  const handleDeploy = async () => {\n    try {\n      setIsDeploying(true);\n\n      // First save the code if there are unsaved changes\n      if (hasUnsavedChanges) {\n        await handleSave();\n      }\n      if (!currentVersion) {\n        toast.error('Please save the code first');\n        return;\n      }\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/deploy`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setCurrentVersion(data.code);\n        toast.success('Code deployed successfully');\n\n        // Reload versions to show the new deployed version\n        loadCodeVersions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to deploy code');\n        if (errorData.validation_errors) {\n          setValidationErrors(errorData.validation_errors);\n        }\n      }\n    } catch (error) {\n      console.error('Error deploying code:', error);\n      toast.error('Failed to deploy code');\n    } finally {\n      setIsDeploying(false);\n    }\n  };\n  const handleEditorDidMount = (editor, monaco) => {\n    editorRef.current = editor;\n    monacoRef.current = monaco;\n\n    // Configure Monaco Editor for Bot SDK\n    setupBotSDKIntelliSense(monaco);\n\n    // Add keyboard shortcuts\n    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {\n      handleSave();\n    });\n    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {\n      handleTest();\n    });\n  };\n  const setupBotSDKIntelliSense = monaco => {\n    // Register completion item provider for Bot SDK\n    monaco.languages.registerCompletionItemProvider('javascript', {\n      provideCompletionItems: (model, position) => {\n        const suggestions = [{\n          label: 'bot.sendMessage',\n          kind: monaco.languages.CompletionItemKind.Method,\n          insertText: 'bot.sendMessage(${1:channelId}, ${2:message})',\n          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n          documentation: 'Send a message to a channel'\n        }, {\n          label: 'bot.on',\n          kind: monaco.languages.CompletionItemKind.Method,\n          insertText: 'bot.on(\\'${1:event}\\', async (${2:data}) => {\\n\\t${3:// Handle event}\\n});',\n          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n          documentation: 'Register an event handler'\n        }, {\n          label: 'bot.storage.set',\n          kind: monaco.languages.CompletionItemKind.Method,\n          insertText: 'await bot.storage.set(${1:key}, ${2:value})',\n          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n          documentation: 'Store a value in bot storage'\n        }, {\n          label: 'bot.storage.get',\n          kind: monaco.languages.CompletionItemKind.Method,\n          insertText: 'await bot.storage.get(${1:key})',\n          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n          documentation: 'Retrieve a value from bot storage'\n        }, {\n          label: 'bot.registerCommand',\n          kind: monaco.languages.CompletionItemKind.Method,\n          insertText: 'bot.registerCommand(\\'${1:commandName}\\', async (${2:context}) => {\\n\\t${3:// Command logic}\\n\\tawait context.reply(\\'${4:response}\\');\\n});',\n          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n          documentation: 'Register a bot command'\n        }];\n        return {\n          suggestions\n        };\n      }\n    });\n\n    // Add hover provider for Bot SDK methods\n    monaco.languages.registerHoverProvider('javascript', {\n      provideHover: (model, position) => {\n        const word = model.getWordAtPosition(position);\n        if (!word) return;\n        const botSDKDocs = {\n          'sendMessage': 'Send a message to a specific channel\\n\\nParameters:\\n- channelId: string - The ID of the channel\\n- message: string - The message content',\n          'storage': 'Bot persistent storage system for saving data between sessions',\n          'registerCommand': 'Register a new bot command that users can invoke'\n        };\n        if (botSDKDocs[word.word]) {\n          return {\n            range: new monaco.Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn),\n            contents: [{\n              value: `**${word.word}**`\n            }, {\n              value: botSDKDocs[word.word]\n            }]\n          };\n        }\n      }\n    });\n  };\n  const addToConsole = useCallback((message, type = 'log') => {\n    setConsoleLogs(prev => [...prev, {\n      message,\n      type,\n      timestamp: new Date().toLocaleTimeString()\n    }]);\n  }, []);\n  const handleCodeChange = value => {\n    setCode(value || '');\n    setHasUnsavedChanges(value !== originalCode);\n  };\n  const handleTemplateSelect = templateCode => {\n    setCode(templateCode);\n    setHasUnsavedChanges(true);\n    setShowTemplateDialog(false);\n  };\n  const clearConsole = () => {\n    setConsoleLogs([]);\n    setTestResults(null);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"60vh\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      px: 2,\n      py: 1,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: \"bold\",\n          children: \"Code Editor\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 11\n        }, this), hasUnsavedChanges && /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Unsaved Changes\",\n          color: \"warning\",\n          size: \"small\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 13\n        }, this), currentVersion && /*#__PURE__*/_jsxDEV(Chip, {\n          label: `v${currentVersion.version} (${currentVersion.status})`,\n          color: currentVersion.status === 'deployed' ? 'success' : 'default',\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 517,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Templates\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setShowTemplateDialog(true),\n            children: /*#__PURE__*/_jsxDEV(TemplateIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"History\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => setShowHistoryDialog(true),\n            children: /*#__PURE__*/_jsxDEV(HistoryIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Settings\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: e => setSettingsMenuAnchor(e.currentTarget),\n            children: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          orientation: \"vertical\",\n          flexItem: true,\n          sx: {\n            mx: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 24\n          }, this),\n          onClick: handleSave,\n          disabled: isSaving || !hasUnsavedChanges,\n          children: isSaving ? 'Saving...' : 'Save'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(TestIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 24\n          }, this),\n          onClick: handleTest,\n          disabled: isTesting,\n          children: isTesting ? 'Testing...' : 'Test'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(DeployIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 24\n          }, this),\n          onClick: handleDeploy,\n          disabled: isDeploying || hasUnsavedChanges,\n          children: isDeploying ? 'Deploying...' : 'Deploy'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 516,\n      columnNumber: 7\n    }, this), validationErrors.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      px: 2,\n      mb: 2,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle2\",\n          gutterBottom: true,\n          children: \"Code Validation Errors:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this), validationErrors.map((error, index) => /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: [\"\\u2022 \", error]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        display: 'flex',\n        overflow: 'hidden'\n      },\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        sx: {\n          height: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 8,\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              flex: 1,\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                p: 0,\n                '&:last-child': {\n                  pb: 0\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Editor, {\n                height: \"100%\",\n                defaultLanguage: \"javascript\",\n                value: code,\n                onChange: handleCodeChange,\n                onMount: handleEditorDidMount,\n                theme: editorTheme,\n                options: {\n                  fontSize: fontSize,\n                  wordWrap: wordWrap,\n                  minimap: {\n                    enabled: minimap\n                  },\n                  automaticLayout: true,\n                  scrollBeyondLastLine: false,\n                  renderLineHighlight: 'all',\n                  selectOnLineNumbers: true,\n                  matchBrackets: 'always',\n                  autoClosingBrackets: 'always',\n                  autoClosingQuotes: 'always',\n                  folding: true,\n                  foldingHighlight: true,\n                  showFoldingControls: 'always',\n                  smoothScrolling: true,\n                  cursorBlinking: 'smooth',\n                  cursorSmoothCaretAnimation: true,\n                  suggest: {\n                    showKeywords: true,\n                    showSnippets: true,\n                    showFunctions: true,\n                    showConstructors: true,\n                    showFields: true,\n                    showVariables: true,\n                    showClasses: true,\n                    showStructs: true,\n                    showInterfaces: true,\n                    showModules: true,\n                    showProperties: true,\n                    showEvents: true,\n                    showOperators: true,\n                    showUnits: true,\n                    showValues: true,\n                    showConstants: true,\n                    showEnums: true,\n                    showEnumMembers: true,\n                    showColors: true,\n                    showFiles: true,\n                    showReferences: true,\n                    showFolders: true,\n                    showTypeParameters: true,\n                    showIssues: true,\n                    showUsers: true\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          lg: 4,\n          sx: {\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            sx: {\n              flex: 1,\n              display: 'flex',\n              flexDirection: 'column'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                borderBottom: 1,\n                borderColor: 'divider'\n              },\n              children: /*#__PURE__*/_jsxDEV(Tabs, {\n                value: activeTab,\n                onChange: (e, newValue) => setActiveTab(newValue),\n                variant: \"fullWidth\",\n                children: [/*#__PURE__*/_jsxDEV(Tab, {\n                  label: \"Console\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Tab, {\n                  label: \"Info\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 678,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n              sx: {\n                flex: 1,\n                p: 0,\n                overflow: 'hidden'\n              },\n              children: [activeTab === 0 && /*#__PURE__*/_jsxDEV(LiveConsole, {\n                logs: consoleLogs,\n                onClear: clearConsole,\n                testResults: testResults\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 19\n              }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Box, {\n                p: 2,\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  gutterBottom: true,\n                  children: \"Code Information\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: [\"Application ID: \", applicationId]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 21\n                }, this), currentVersion && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: [\"Version: \", currentVersion.version]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 703,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: [\"Status: \", currentVersion.status]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 706,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: [\"Created: \", new Date(currentVersion.created_at).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 709,\n                    columnNumber: 25\n                  }, this), currentVersion.deployed_at && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"text.secondary\",\n                    gutterBottom: true,\n                    children: [\"Deployed: \", new Date(currentVersion.deployed_at).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 713,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {\n                  sx: {\n                    my: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Keyboard Shortcuts:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 722,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"\\u2022 Ctrl/Cmd + S: Save\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 725,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"\\u2022 Ctrl/Cmd + Enter: Test\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  gutterBottom: true,\n                  children: \"\\u2022 Ctrl/Cmd + /: Toggle Comment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 669,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 606,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 605,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: settingsMenuAnchor,\n      open: Boolean(settingsMenuAnchor),\n      onClose: () => setSettingsMenuAnchor(null),\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          setEditorTheme(editorTheme === 'vs-dark' ? 'light' : 'vs-dark');\n          setSettingsMenuAnchor(null);\n        },\n        children: \"Toggle Theme\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          setFontSize(fontSize === 14 ? 16 : 14);\n          setSettingsMenuAnchor(null);\n        },\n        children: [\"Font Size: \", fontSize, \"px\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 753,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          setWordWrap(wordWrap === 'on' ? 'off' : 'on');\n          setSettingsMenuAnchor(null);\n        },\n        children: [\"Word Wrap: \", wordWrap]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 759,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          setMinimap(!minimap);\n          setSettingsMenuAnchor(null);\n        },\n        children: [\"Minimap: \", minimap ? 'On' : 'Off']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 765,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 742,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showTemplateDialog,\n      onClose: () => setShowTemplateDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Choose a Bot Template\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 780,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CodeTemplateSelector, {\n          onSelect: handleTemplateSelect\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 784,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 783,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowTemplateDialog(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 787,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 786,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 774,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showHistoryDialog,\n      onClose: () => setShowHistoryDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Code History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 800,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(CodeHistory, {\n          versions: codeVersions,\n          currentVersion: currentVersion,\n          onRestore: version => {\n            setCode(version.code);\n            setHasUnsavedChanges(true);\n            setShowHistoryDialog(false);\n            toast.success('Code restored from history');\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 803,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowHistoryDialog(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 794,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 514,\n    columnNumber: 5\n  }, this);\n};\n_s(CodeEditor, \"PnFWwjRV/PvrE8doTUERwPk9xks=\", true, function () {\n  return [useParams, useNavigate];\n});\n_c = CodeEditor;\nexport default CodeEditor;\nvar _c;\n$RefreshReg$(_c, \"CodeEditor\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useParams", "useNavigate", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Paper", "Tabs", "Tab", "IconButton", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Chip", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON><PERSON>", "Divider", "Save", "SaveIcon", "PlayArrow", "TestIcon", "Publish", "DeployIcon", "Settings", "SettingsIcon", "History", "HistoryIcon", "FolderOpen", "TemplateIcon", "BugReport", "DebugIcon", "Refresh", "RefreshIcon", "<PERSON><PERSON><PERSON>", "MoreIcon", "Add", "AddIcon", "Close", "CloseIcon", "Editor", "toast", "CodeTemplateSelector", "LiveConsole", "FileExplorer", "CodeHistory", "DeploymentPanel", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CodeEditor", "_s", "id", "applicationId", "codeId", "navigate", "location", "useLocation", "botContext", "state", "code", "setCode", "originalCode", "setOriginalCode", "isLoading", "setIsLoading", "isSaving", "setIsSaving", "isTesting", "setIsTesting", "isDeploying", "setIsDeploying", "hasUnsavedChanges", "setHasUnsavedChanges", "activeTab", "setActiveTab", "showTemplateDialog", "setShowTemplateDialog", "showHistoryDialog", "setShowHistoryDialog", "showDeployDialog", "setShowDeployDialog", "settingsMenuAnchor", "setSettingsMenuAnchor", "editor<PERSON><PERSON><PERSON>", "setEditorTheme", "fontSize", "setFontSize", "wordWrap", "setWordWrap", "minimap", "setMinimap", "consoleLogs", "setConsoleLogs", "testResults", "setTestResults", "validationErrors", "setValidationErrors", "files", "setFiles", "currentFile", "setCurrentFile", "codeVersions", "setCodeVersions", "currentVersion", "setCurrentVersion", "editor<PERSON><PERSON>", "monacoRef", "autoSaveTimerRef", "loadCodeData", "loadCodeVersions", "current", "clearTimeout", "setTimeout", "handleAutoSave", "response", "fetch", "headers", "localStorage", "getItem", "codesResponse", "ok", "codesData", "json", "codes", "length", "latestCode", "find", "c", "status", "data", "codeData", "validation_errors", "error", "console", "method", "body", "JSON", "stringify", "success", "duration", "handleSave", "entry_point", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "errorData", "handleTest", "test_input", "message", "content", "channel_id", "author", "username", "result", "addToConsole", "handleDeploy", "handleEditorDidMount", "editor", "monaco", "setupBotSDKIntelliSense", "addCommand", "KeyMod", "CtrlCmd", "KeyCode", "KeyS", "Enter", "languages", "registerCompletionItemProvider", "provideCompletionItems", "model", "position", "suggestions", "label", "kind", "CompletionItemKind", "Method", "insertText", "insertTextRules", "CompletionItemInsertTextRule", "InsertAsSnippet", "documentation", "registerHoverProvider", "provideHover", "word", "getWordAtPosition", "botSDKDocs", "range", "Range", "lineNumber", "startColumn", "endColumn", "contents", "value", "type", "prev", "timestamp", "Date", "toLocaleTimeString", "handleCodeChange", "handleTemplateSelect", "templateCode", "clearConsole", "display", "justifyContent", "alignItems", "minHeight", "children", "size", "fileName", "_jsxFileName", "columnNumber", "className", "sx", "height", "flexDirection", "mb", "px", "py", "gap", "variant", "fontWeight", "color", "version", "title", "onClick", "e", "currentTarget", "orientation", "flexItem", "mx", "startIcon", "disabled", "severity", "gutterBottom", "map", "index", "flex", "overflow", "container", "item", "xs", "lg", "p", "pb", "defaultLanguage", "onChange", "onMount", "theme", "options", "enabled", "automaticLayout", "scrollBeyondLastLine", "renderLineHighlight", "selectOnLineNumbers", "matchBrackets", "autoClosingBrackets", "autoClosingQuotes", "folding", "foldingHighlight", "showFoldingControls", "smoothScrolling", "cursorBlinking", "cursorSmoothCaretAnimation", "suggest", "showKeywords", "showSnippets", "showFunctions", "showConstructors", "showFields", "showVariables", "showClasses", "showStructs", "showInterfaces", "showModules", "showProperties", "showEvents", "showOperators", "showUnits", "showValues", "showConstants", "showEnums", "showEnumMembers", "showColors", "showFiles", "showReferences", "showFolders", "showTypeParameters", "showIssues", "showUsers", "borderBottom", "borderColor", "newValue", "logs", "onClear", "created_at", "toLocaleString", "deployed_at", "my", "anchorEl", "open", "Boolean", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "onSelect", "versions", "onRestore", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/components/CodeEditor/CodeEditor.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  Grid,\n  Paper,\n  Tabs,\n  Tab,\n  IconButton,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Chip,\n  Alert,\n  CircularProgress,\n  Tooltip,\n  Divider,\n} from '@mui/material';\nimport {\n  Save as SaveIcon,\n  PlayArrow as TestIcon,\n  Publish as DeployIcon,\n  Settings as SettingsIcon,\n  History as HistoryIcon,\n  FolderOpen as TemplateIcon,\n  BugReport as DebugIcon,\n  Refresh as RefreshIcon,\n  MoreVert as MoreIcon,\n  Add as AddIcon,\n  Close as CloseIcon,\n} from '@mui/icons-material';\nimport Editor from '@monaco-editor/react';\nimport toast from 'react-hot-toast';\n\n// Import our custom components\nimport CodeTemplateSelector from './CodeTemplateSelector';\nimport LiveConsole from './LiveConsole';\nimport FileExplorer from './FileExplorer';\nimport CodeHistory from './CodeHistory';\nimport DeploymentPanel from './DeploymentPanel';\n\nconst CodeEditor = () => {\n  const { id: applicationId, codeId } = useParams();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Get bot context from navigation state\n  const botContext = location.state || {};\n\n  // State management\n  const [code, setCode] = useState('');\n  const [originalCode, setOriginalCode] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [isTesting, setIsTesting] = useState(false);\n  const [isDeploying, setIsDeploying] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // UI State\n  const [activeTab, setActiveTab] = useState(0);\n  const [showTemplateDialog, setShowTemplateDialog] = useState(false);\n  const [showHistoryDialog, setShowHistoryDialog] = useState(false);\n  const [showDeployDialog, setShowDeployDialog] = useState(false);\n  const [settingsMenuAnchor, setSettingsMenuAnchor] = useState(null);\n\n  // Editor state\n  const [editorTheme, setEditorTheme] = useState('vs-dark');\n  const [fontSize, setFontSize] = useState(14);\n  const [wordWrap, setWordWrap] = useState('on');\n  const [minimap, setMinimap] = useState(false);\n\n  // Console and testing\n  const [consoleLogs, setConsoleLogs] = useState([]);\n  const [testResults, setTestResults] = useState(null);\n  const [validationErrors, setValidationErrors] = useState([]);\n\n  // File management\n  const [files, setFiles] = useState([]);\n  const [currentFile, setCurrentFile] = useState(null);\n\n  // Code versions and history\n  const [codeVersions, setCodeVersions] = useState([]);\n  const [currentVersion, setCurrentVersion] = useState(null);\n\n  // Refs\n  const editorRef = useRef(null);\n  const monacoRef = useRef(null);\n\n  // Auto-save timer\n  const autoSaveTimerRef = useRef(null);\n\n  // Load initial data\n  useEffect(() => {\n    loadCodeData();\n    loadCodeVersions();\n  }, [applicationId, codeId]);\n\n  // Auto-save functionality\n  useEffect(() => {\n    if (hasUnsavedChanges && code !== originalCode) {\n      if (autoSaveTimerRef.current) {\n        clearTimeout(autoSaveTimerRef.current);\n      }\n\n      autoSaveTimerRef.current = setTimeout(() => {\n        handleAutoSave();\n      }, 2000); // Auto-save after 2 seconds of inactivity\n    }\n\n    return () => {\n      if (autoSaveTimerRef.current) {\n        clearTimeout(autoSaveTimerRef.current);\n      }\n    };\n  }, [code, hasUnsavedChanges, originalCode]);\n\n  const loadCodeData = async () => {\n    try {\n      setIsLoading(true);\n\n      let response;\n      if (codeId) {\n        // Load specific code version\n        response = await fetch(`/api/applications/${applicationId}/code/${codeId}`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n      } else {\n        // Load latest code or create new\n        const codesResponse = await fetch(`/api/applications/${applicationId}/code`, {\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n\n        if (codesResponse.ok) {\n          const codesData = await codesResponse.json();\n          if (codesData.codes && codesData.codes.length > 0) {\n            // Use the latest draft or deployed version\n            const latestCode = codesData.codes.find(c => c.status === 'draft') || codesData.codes[0];\n            response = await fetch(`/api/applications/${applicationId}/code/${latestCode.id}`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              }\n            });\n          } else {\n            // No code exists, start with template\n            setShowTemplateDialog(true);\n            setIsLoading(false);\n            return;\n          }\n        }\n      }\n\n      if (response && response.ok) {\n        const data = await response.json();\n        const codeData = data.code;\n\n        setCode(codeData.code || '');\n        setOriginalCode(codeData.code || '');\n        setCurrentVersion(codeData);\n        setHasUnsavedChanges(false);\n\n        // Set validation errors if any\n        if (codeData.validation_errors) {\n          setValidationErrors(codeData.validation_errors);\n        }\n      } else {\n        toast.error('Failed to load code');\n      }\n    } catch (error) {\n      console.error('Error loading code:', error);\n      toast.error('Failed to load code');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loadCodeVersions = async () => {\n    try {\n      const response = await fetch(`/api/applications/${applicationId}/code`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setCodeVersions(data.codes || []);\n      }\n    } catch (error) {\n      console.error('Error loading code versions:', error);\n    }\n  };\n  const handleAutoSave = async () => {\n    if (!hasUnsavedChanges || !currentVersion) return;\n\n    try {\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          code: code\n        })\n      });\n\n      if (response.ok) {\n        setOriginalCode(code);\n        setHasUnsavedChanges(false);\n        toast.success('Auto-saved', { duration: 1000 });\n      }\n    } catch (error) {\n      console.error('Auto-save failed:', error);\n    }\n  };\n\n  const handleSave = async () => {\n    try {\n      setIsSaving(true);\n\n      let response;\n      if (currentVersion && currentVersion.status === 'draft') {\n        // Update existing draft\n        response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {\n          method: 'PUT',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify({\n            code: code\n          })\n        });\n      } else {\n        // Create new version\n        response = await fetch(`/api/applications/${applicationId}/code`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify({\n            code: code,\n            entry_point: 'index.js'\n          })\n        });\n      }\n\n      if (response.ok) {\n        const data = await response.json();\n        setCurrentVersion(data.code);\n        setOriginalCode(code);\n        setHasUnsavedChanges(false);\n\n        if (data.validation && !data.validation.isValid) {\n          setValidationErrors(data.validation.errors || []);\n          toast.error('Code saved with validation errors');\n        } else {\n          setValidationErrors([]);\n          toast.success('Code saved successfully');\n        }\n\n        // Reload versions\n        loadCodeVersions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to save code');\n\n        if (errorData.validation_errors) {\n          setValidationErrors(errorData.validation_errors);\n        }\n      }\n    } catch (error) {\n      console.error('Error saving code:', error);\n      toast.error('Failed to save code');\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleTest = async () => {\n    try {\n      setIsTesting(true);\n      setConsoleLogs([]);\n      setTestResults(null);\n\n      // First save the code if there are unsaved changes\n      if (hasUnsavedChanges) {\n        await handleSave();\n      }\n\n      if (!currentVersion) {\n        toast.error('Please save the code first');\n        return;\n      }\n\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/test`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          test_input: {\n            message: {\n              content: '!ping',\n              channel_id: 'test-channel-1',\n              author: { id: 'test-user', username: 'TestUser' }\n            }\n          }\n        })\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setTestResults(data.result);\n\n        // Add test results to console\n        addToConsole('=== Test Results ===', 'info');\n        addToConsole(JSON.stringify(data.result, null, 2), 'log');\n\n        toast.success('Code test completed');\n      } else {\n        const errorData = await response.json();\n        setTestResults({ error: errorData.message });\n        addToConsole(`Test Error: ${errorData.message}`, 'error');\n        toast.error('Code test failed');\n      }\n    } catch (error) {\n      console.error('Error testing code:', error);\n      addToConsole(`Test Error: ${error.message}`, 'error');\n      toast.error('Failed to test code');\n    } finally {\n      setIsTesting(false);\n    }\n  };\n\n  const handleDeploy = async () => {\n    try {\n      setIsDeploying(true);\n\n      // First save the code if there are unsaved changes\n      if (hasUnsavedChanges) {\n        await handleSave();\n      }\n\n      if (!currentVersion) {\n        toast.error('Please save the code first');\n        return;\n      }\n\n      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/deploy`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setCurrentVersion(data.code);\n        toast.success('Code deployed successfully');\n\n        // Reload versions to show the new deployed version\n        loadCodeVersions();\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to deploy code');\n\n        if (errorData.validation_errors) {\n          setValidationErrors(errorData.validation_errors);\n        }\n      }\n    } catch (error) {\n      console.error('Error deploying code:', error);\n      toast.error('Failed to deploy code');\n    } finally {\n      setIsDeploying(false);\n    }\n  };\n\n  const handleEditorDidMount = (editor, monaco) => {\n    editorRef.current = editor;\n    monacoRef.current = monaco;\n\n    // Configure Monaco Editor for Bot SDK\n    setupBotSDKIntelliSense(monaco);\n\n    // Add keyboard shortcuts\n    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {\n      handleSave();\n    });\n\n    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {\n      handleTest();\n    });\n  };\n\n  const setupBotSDKIntelliSense = (monaco) => {\n    // Register completion item provider for Bot SDK\n    monaco.languages.registerCompletionItemProvider('javascript', {\n      provideCompletionItems: (model, position) => {\n        const suggestions = [\n          {\n            label: 'bot.sendMessage',\n            kind: monaco.languages.CompletionItemKind.Method,\n            insertText: 'bot.sendMessage(${1:channelId}, ${2:message})',\n            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n            documentation: 'Send a message to a channel'\n          },\n          {\n            label: 'bot.on',\n            kind: monaco.languages.CompletionItemKind.Method,\n            insertText: 'bot.on(\\'${1:event}\\', async (${2:data}) => {\\n\\t${3:// Handle event}\\n});',\n            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n            documentation: 'Register an event handler'\n          },\n          {\n            label: 'bot.storage.set',\n            kind: monaco.languages.CompletionItemKind.Method,\n            insertText: 'await bot.storage.set(${1:key}, ${2:value})',\n            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n            documentation: 'Store a value in bot storage'\n          },\n          {\n            label: 'bot.storage.get',\n            kind: monaco.languages.CompletionItemKind.Method,\n            insertText: 'await bot.storage.get(${1:key})',\n            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n            documentation: 'Retrieve a value from bot storage'\n          },\n          {\n            label: 'bot.registerCommand',\n            kind: monaco.languages.CompletionItemKind.Method,\n            insertText: 'bot.registerCommand(\\'${1:commandName}\\', async (${2:context}) => {\\n\\t${3:// Command logic}\\n\\tawait context.reply(\\'${4:response}\\');\\n});',\n            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,\n            documentation: 'Register a bot command'\n          }\n        ];\n\n        return { suggestions };\n      }\n    });\n\n    // Add hover provider for Bot SDK methods\n    monaco.languages.registerHoverProvider('javascript', {\n      provideHover: (model, position) => {\n        const word = model.getWordAtPosition(position);\n        if (!word) return;\n\n        const botSDKDocs = {\n          'sendMessage': 'Send a message to a specific channel\\n\\nParameters:\\n- channelId: string - The ID of the channel\\n- message: string - The message content',\n          'storage': 'Bot persistent storage system for saving data between sessions',\n          'registerCommand': 'Register a new bot command that users can invoke'\n        };\n\n        if (botSDKDocs[word.word]) {\n          return {\n            range: new monaco.Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn),\n            contents: [\n              { value: `**${word.word}**` },\n              { value: botSDKDocs[word.word] }\n            ]\n          };\n        }\n      }\n    });\n  };\n\n  const addToConsole = useCallback((message, type = 'log') => {\n    setConsoleLogs(prev => [...prev, {\n      message,\n      type,\n      timestamp: new Date().toLocaleTimeString()\n    }]);\n  }, []);\n\n  const handleCodeChange = (value) => {\n    setCode(value || '');\n    setHasUnsavedChanges(value !== originalCode);\n  };\n\n  const handleTemplateSelect = (templateCode) => {\n    setCode(templateCode);\n    setHasUnsavedChanges(true);\n    setShowTemplateDialog(false);\n  };\n\n  const clearConsole = () => {\n    setConsoleLogs([]);\n    setTestResults(null);\n  };\n  if (isLoading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"60vh\">\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\" sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2} px={2} py={1}>\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <Typography variant=\"h4\" fontWeight=\"bold\">\n            Code Editor\n          </Typography>\n          {hasUnsavedChanges && (\n            <Chip\n              label=\"Unsaved Changes\"\n              color=\"warning\"\n              size=\"small\"\n              variant=\"outlined\"\n            />\n          )}\n          {currentVersion && (\n            <Chip\n              label={`v${currentVersion.version} (${currentVersion.status})`}\n              color={currentVersion.status === 'deployed' ? 'success' : 'default'}\n              size=\"small\"\n            />\n          )}\n        </Box>\n\n        <Box display=\"flex\" gap={1}>\n          <Tooltip title=\"Templates\">\n            <IconButton onClick={() => setShowTemplateDialog(true)}>\n              <TemplateIcon />\n            </IconButton>\n          </Tooltip>\n\n          <Tooltip title=\"History\">\n            <IconButton onClick={() => setShowHistoryDialog(true)}>\n              <HistoryIcon />\n            </IconButton>\n          </Tooltip>\n\n          <Tooltip title=\"Settings\">\n            <IconButton onClick={(e) => setSettingsMenuAnchor(e.currentTarget)}>\n              <SettingsIcon />\n            </IconButton>\n          </Tooltip>\n\n          <Divider orientation=\"vertical\" flexItem sx={{ mx: 1 }} />\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<SaveIcon />}\n            onClick={handleSave}\n            disabled={isSaving || !hasUnsavedChanges}\n          >\n            {isSaving ? 'Saving...' : 'Save'}\n          </Button>\n\n          <Button\n            variant=\"outlined\"\n            startIcon={<TestIcon />}\n            onClick={handleTest}\n            disabled={isTesting}\n          >\n            {isTesting ? 'Testing...' : 'Test'}\n          </Button>\n\n          <Button\n            variant=\"contained\"\n            startIcon={<DeployIcon />}\n            onClick={handleDeploy}\n            disabled={isDeploying || hasUnsavedChanges}\n          >\n            {isDeploying ? 'Deploying...' : 'Deploy'}\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Validation Errors */}\n      {validationErrors.length > 0 && (\n        <Box px={2} mb={2}>\n          <Alert severity=\"error\" sx={{ mb: 1 }}>\n            <Typography variant=\"subtitle2\" gutterBottom>\n              Code Validation Errors:\n            </Typography>\n            {validationErrors.map((error, index) => (\n              <Typography key={index} variant=\"body2\">\n                • {error}\n              </Typography>\n            ))}\n          </Alert>\n        </Box>\n      )}\n\n      {/* Main Content */}\n      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>\n        <Grid container sx={{ height: '100%' }}>\n          {/* Code Editor */}\n          <Grid item xs={12} lg={8} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n              <CardContent sx={{ flex: 1, p: 0, '&:last-child': { pb: 0 } }}>\n                <Editor\n                  height=\"100%\"\n                  defaultLanguage=\"javascript\"\n                  value={code}\n                  onChange={handleCodeChange}\n                  onMount={handleEditorDidMount}\n                  theme={editorTheme}\n                  options={{\n                    fontSize: fontSize,\n                    wordWrap: wordWrap,\n                    minimap: { enabled: minimap },\n                    automaticLayout: true,\n                    scrollBeyondLastLine: false,\n                    renderLineHighlight: 'all',\n                    selectOnLineNumbers: true,\n                    matchBrackets: 'always',\n                    autoClosingBrackets: 'always',\n                    autoClosingQuotes: 'always',\n                    folding: true,\n                    foldingHighlight: true,\n                    showFoldingControls: 'always',\n                    smoothScrolling: true,\n                    cursorBlinking: 'smooth',\n                    cursorSmoothCaretAnimation: true,\n                    suggest: {\n                      showKeywords: true,\n                      showSnippets: true,\n                      showFunctions: true,\n                      showConstructors: true,\n                      showFields: true,\n                      showVariables: true,\n                      showClasses: true,\n                      showStructs: true,\n                      showInterfaces: true,\n                      showModules: true,\n                      showProperties: true,\n                      showEvents: true,\n                      showOperators: true,\n                      showUnits: true,\n                      showValues: true,\n                      showConstants: true,\n                      showEnums: true,\n                      showEnumMembers: true,\n                      showColors: true,\n                      showFiles: true,\n                      showReferences: true,\n                      showFolders: true,\n                      showTypeParameters: true,\n                      showIssues: true,\n                      showUsers: true,\n                    }\n                  }}\n                />\n              </CardContent>\n            </Card>\n          </Grid>\n\n          {/* Side Panel */}\n          <Grid item xs={12} lg={4} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n            <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>\n                <Tabs\n                  value={activeTab}\n                  onChange={(e, newValue) => setActiveTab(newValue)}\n                  variant=\"fullWidth\"\n                >\n                  <Tab label=\"Console\" />\n                  <Tab label=\"Info\" />\n                </Tabs>\n              </Box>\n\n              <CardContent sx={{ flex: 1, p: 0, overflow: 'hidden' }}>\n                {activeTab === 0 && (\n                  <LiveConsole\n                    logs={consoleLogs}\n                    onClear={clearConsole}\n                    testResults={testResults}\n                  />\n                )}\n\n                {activeTab === 1 && (\n                  <Box p={2}>\n                    <Typography variant=\"h6\" gutterBottom>\n                      Code Information\n                    </Typography>\n\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      Application ID: {applicationId}\n                    </Typography>\n\n                    {currentVersion && (\n                      <>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          Version: {currentVersion.version}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          Status: {currentVersion.status}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                          Created: {new Date(currentVersion.created_at).toLocaleString()}\n                        </Typography>\n                        {currentVersion.deployed_at && (\n                          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                            Deployed: {new Date(currentVersion.deployed_at).toLocaleString()}\n                          </Typography>\n                        )}\n                      </>\n                    )}\n\n                    <Divider sx={{ my: 2 }} />\n\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Keyboard Shortcuts:\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      • Ctrl/Cmd + S: Save\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      • Ctrl/Cmd + Enter: Test\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                      • Ctrl/Cmd + /: Toggle Comment\n                    </Typography>\n                  </Box>\n                )}\n              </CardContent>\n            </Card>\n          </Grid>\n        </Grid>\n      </Box>\n      {/* Settings Menu */}\n      <Menu\n        anchorEl={settingsMenuAnchor}\n        open={Boolean(settingsMenuAnchor)}\n        onClose={() => setSettingsMenuAnchor(null)}\n      >\n        <MenuItem onClick={() => {\n          setEditorTheme(editorTheme === 'vs-dark' ? 'light' : 'vs-dark');\n          setSettingsMenuAnchor(null);\n        }}>\n          Toggle Theme\n        </MenuItem>\n        <MenuItem onClick={() => {\n          setFontSize(fontSize === 14 ? 16 : 14);\n          setSettingsMenuAnchor(null);\n        }}>\n          Font Size: {fontSize}px\n        </MenuItem>\n        <MenuItem onClick={() => {\n          setWordWrap(wordWrap === 'on' ? 'off' : 'on');\n          setSettingsMenuAnchor(null);\n        }}>\n          Word Wrap: {wordWrap}\n        </MenuItem>\n        <MenuItem onClick={() => {\n          setMinimap(!minimap);\n          setSettingsMenuAnchor(null);\n        }}>\n          Minimap: {minimap ? 'On' : 'Off'}\n        </MenuItem>\n      </Menu>\n\n      {/* Template Selection Dialog */}\n      <Dialog\n        open={showTemplateDialog}\n        onClose={() => setShowTemplateDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          Choose a Bot Template\n        </DialogTitle>\n        <DialogContent>\n          <CodeTemplateSelector onSelect={handleTemplateSelect} />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowTemplateDialog(false)}>\n            Cancel\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* History Dialog */}\n      <Dialog\n        open={showHistoryDialog}\n        onClose={() => setShowHistoryDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          Code History\n        </DialogTitle>\n        <DialogContent>\n          <CodeHistory\n            versions={codeVersions}\n            currentVersion={currentVersion}\n            onRestore={(version) => {\n              setCode(version.code);\n              setHasUnsavedChanges(true);\n              setShowHistoryDialog(false);\n              toast.success('Code restored from history');\n            }}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowHistoryDialog(false)}>\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CodeEditor;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,IAAI,EACJC,KAAK,EACLC,gBAAgB,EAChBC,OAAO,EACPC,OAAO,QACF,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,SAAS,IAAIC,QAAQ,EACrBC,OAAO,IAAIC,UAAU,EACrBC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,UAAU,IAAIC,YAAY,EAC1BC,SAAS,IAAIC,SAAS,EACtBC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,QAAQ,EACpBC,GAAG,IAAIC,OAAO,EACdC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,KAAK,MAAM,iBAAiB;;AAEnC;AACA,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhD,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,EAAE,EAAEC,aAAa;IAAEC;EAAO,CAAC,GAAG7D,SAAS,CAAC,CAAC;EACjD,MAAM8D,QAAQ,GAAG7D,WAAW,CAAC,CAAC;EAC9B,MAAM8D,QAAQ,GAAGC,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMC,UAAU,GAAGF,QAAQ,CAACG,KAAK,IAAI,CAAC,CAAC;;EAEvC;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyE,YAAY,EAAEC,eAAe,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6E,QAAQ,EAAEC,WAAW,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;;EAEjE;EACA,MAAM,CAACqF,SAAS,EAAEC,YAAY,CAAC,GAAGtF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACuF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACyF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1F,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6F,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;;EAElE;EACA,MAAM,CAAC+F,WAAW,EAAEC,cAAc,CAAC,GAAGhG,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAACiG,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmG,QAAQ,EAAEC,WAAW,CAAC,GAAGpG,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqG,OAAO,EAAEC,UAAU,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACyG,WAAW,EAAEC,cAAc,CAAC,GAAG1G,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC2G,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5G,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA,MAAM,CAAC6G,KAAK,EAAEC,QAAQ,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+G,WAAW,EAAEC,cAAc,CAAC,GAAGhH,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM,CAACiH,YAAY,EAAEC,eAAe,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACmH,cAAc,EAAEC,iBAAiB,CAAC,GAAGpH,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAMqH,SAAS,GAAGnH,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMoH,SAAS,GAAGpH,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMqH,gBAAgB,GAAGrH,MAAM,CAAC,IAAI,CAAC;;EAErC;EACAD,SAAS,CAAC,MAAM;IACduH,YAAY,CAAC,CAAC;IACdC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACzD,aAAa,EAAEC,MAAM,CAAC,CAAC;;EAE3B;EACAhE,SAAS,CAAC,MAAM;IACd,IAAIkF,iBAAiB,IAAIZ,IAAI,KAAKE,YAAY,EAAE;MAC9C,IAAI8C,gBAAgB,CAACG,OAAO,EAAE;QAC5BC,YAAY,CAACJ,gBAAgB,CAACG,OAAO,CAAC;MACxC;MAEAH,gBAAgB,CAACG,OAAO,GAAGE,UAAU,CAAC,MAAM;QAC1CC,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IACZ;IAEA,OAAO,MAAM;MACX,IAAIN,gBAAgB,CAACG,OAAO,EAAE;QAC5BC,YAAY,CAACJ,gBAAgB,CAACG,OAAO,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAACnD,IAAI,EAAEY,iBAAiB,EAAEV,YAAY,CAAC,CAAC;EAE3C,MAAM+C,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF5C,YAAY,CAAC,IAAI,CAAC;MAElB,IAAIkD,QAAQ;MACZ,IAAI7D,MAAM,EAAE;QACV;QACA6D,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASC,MAAM,EAAE,EAAE;UAC1E+D,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,MAAMC,aAAa,GAAG,MAAMJ,KAAK,CAAC,qBAAqB/D,aAAa,OAAO,EAAE;UAC3EgE,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D;QACF,CAAC,CAAC;QAEF,IAAIC,aAAa,CAACC,EAAE,EAAE;UACpB,MAAMC,SAAS,GAAG,MAAMF,aAAa,CAACG,IAAI,CAAC,CAAC;UAC5C,IAAID,SAAS,CAACE,KAAK,IAAIF,SAAS,CAACE,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;YACjD;YACA,MAAMC,UAAU,GAAGJ,SAAS,CAACE,KAAK,CAACG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK,OAAO,CAAC,IAAIP,SAAS,CAACE,KAAK,CAAC,CAAC,CAAC;YACxFT,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASyE,UAAU,CAAC1E,EAAE,EAAE,EAAE;cACjFiE,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC1D;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL;YACA1C,qBAAqB,CAAC,IAAI,CAAC;YAC3BZ,YAAY,CAAC,KAAK,CAAC;YACnB;UACF;QACF;MACF;MAEA,IAAIkD,QAAQ,IAAIA,QAAQ,CAACM,EAAE,EAAE;QAC3B,MAAMS,IAAI,GAAG,MAAMf,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClC,MAAMQ,QAAQ,GAAGD,IAAI,CAACtE,IAAI;QAE1BC,OAAO,CAACsE,QAAQ,CAACvE,IAAI,IAAI,EAAE,CAAC;QAC5BG,eAAe,CAACoE,QAAQ,CAACvE,IAAI,IAAI,EAAE,CAAC;QACpC6C,iBAAiB,CAAC0B,QAAQ,CAAC;QAC3B1D,oBAAoB,CAAC,KAAK,CAAC;;QAE3B;QACA,IAAI0D,QAAQ,CAACC,iBAAiB,EAAE;UAC9BnC,mBAAmB,CAACkC,QAAQ,CAACC,iBAAiB,CAAC;QACjD;MACF,CAAC,MAAM;QACL5F,KAAK,CAAC6F,KAAK,CAAC,qBAAqB,CAAC;MACpC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C7F,KAAK,CAAC6F,KAAK,CAAC,qBAAqB,CAAC;IACpC,CAAC,SAAS;MACRpE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,OAAO,EAAE;QACtEgE,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMS,IAAI,GAAG,MAAMf,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClCpB,eAAe,CAAC2B,IAAI,CAACN,KAAK,IAAI,EAAE,CAAC;MACnC;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EACD,MAAMnB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAC1C,iBAAiB,IAAI,CAACgC,cAAc,EAAE;IAE3C,IAAI;MACF,MAAMW,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASmD,cAAc,CAACpD,EAAE,EAAE,EAAE;QAC3FmF,MAAM,EAAE,KAAK;QACblB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnB9E,IAAI,EAAEA;QACR,CAAC;MACH,CAAC,CAAC;MAEF,IAAIuD,QAAQ,CAACM,EAAE,EAAE;QACf1D,eAAe,CAACH,IAAI,CAAC;QACrBa,oBAAoB,CAAC,KAAK,CAAC;QAC3BjC,KAAK,CAACmG,OAAO,CAAC,YAAY,EAAE;UAAEC,QAAQ,EAAE;QAAK,CAAC,CAAC;MACjD;IACF,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;IAC3C;EACF,CAAC;EAED,MAAMQ,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF1E,WAAW,CAAC,IAAI,CAAC;MAEjB,IAAIgD,QAAQ;MACZ,IAAIX,cAAc,IAAIA,cAAc,CAACyB,MAAM,KAAK,OAAO,EAAE;QACvD;QACAd,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASmD,cAAc,CAACpD,EAAE,EAAE,EAAE;UACrFmF,MAAM,EAAE,KAAK;UACblB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB9E,IAAI,EAAEA;UACR,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAuD,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,OAAO,EAAE;UAChEkF,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB9E,IAAI,EAAEA,IAAI;YACVkF,WAAW,EAAE;UACf,CAAC;QACH,CAAC,CAAC;MACJ;MAEA,IAAI3B,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMS,IAAI,GAAG,MAAMf,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClClB,iBAAiB,CAACyB,IAAI,CAACtE,IAAI,CAAC;QAC5BG,eAAe,CAACH,IAAI,CAAC;QACrBa,oBAAoB,CAAC,KAAK,CAAC;QAE3B,IAAIyD,IAAI,CAACa,UAAU,IAAI,CAACb,IAAI,CAACa,UAAU,CAACC,OAAO,EAAE;UAC/C/C,mBAAmB,CAACiC,IAAI,CAACa,UAAU,CAACE,MAAM,IAAI,EAAE,CAAC;UACjDzG,KAAK,CAAC6F,KAAK,CAAC,mCAAmC,CAAC;QAClD,CAAC,MAAM;UACLpC,mBAAmB,CAAC,EAAE,CAAC;UACvBzD,KAAK,CAACmG,OAAO,CAAC,yBAAyB,CAAC;QAC1C;;QAEA;QACA7B,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACL,MAAMoC,SAAS,GAAG,MAAM/B,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvCnF,KAAK,CAAC6F,KAAK,CAACa,SAAS,CAACb,KAAK,IAAI,qBAAqB,CAAC;QAErD,IAAIa,SAAS,CAACd,iBAAiB,EAAE;UAC/BnC,mBAAmB,CAACiD,SAAS,CAACd,iBAAiB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C7F,KAAK,CAAC6F,KAAK,CAAC,qBAAqB,CAAC;IACpC,CAAC,SAAS;MACRlE,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC;EAED,MAAMgF,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF9E,YAAY,CAAC,IAAI,CAAC;MAClBwB,cAAc,CAAC,EAAE,CAAC;MAClBE,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAIvB,iBAAiB,EAAE;QACrB,MAAMqE,UAAU,CAAC,CAAC;MACpB;MAEA,IAAI,CAACrC,cAAc,EAAE;QACnBhE,KAAK,CAAC6F,KAAK,CAAC,4BAA4B,CAAC;QACzC;MACF;MAEA,MAAMlB,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASmD,cAAc,CAACpD,EAAE,OAAO,EAAE;QAChGmF,MAAM,EAAE,MAAM;QACdlB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDiB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBU,UAAU,EAAE;YACVC,OAAO,EAAE;cACPC,OAAO,EAAE,OAAO;cAChBC,UAAU,EAAE,gBAAgB;cAC5BC,MAAM,EAAE;gBAAEpG,EAAE,EAAE,WAAW;gBAAEqG,QAAQ,EAAE;cAAW;YAClD;UACF;QACF,CAAC;MACH,CAAC,CAAC;MAEF,IAAItC,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMS,IAAI,GAAG,MAAMf,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClC5B,cAAc,CAACmC,IAAI,CAACwB,MAAM,CAAC;;QAE3B;QACAC,YAAY,CAAC,sBAAsB,EAAE,MAAM,CAAC;QAC5CA,YAAY,CAAClB,IAAI,CAACC,SAAS,CAACR,IAAI,CAACwB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;QAEzDlH,KAAK,CAACmG,OAAO,CAAC,qBAAqB,CAAC;MACtC,CAAC,MAAM;QACL,MAAMO,SAAS,GAAG,MAAM/B,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvC5B,cAAc,CAAC;UAAEsC,KAAK,EAAEa,SAAS,CAACG;QAAQ,CAAC,CAAC;QAC5CM,YAAY,CAAC,eAAeT,SAAS,CAACG,OAAO,EAAE,EAAE,OAAO,CAAC;QACzD7G,KAAK,CAAC6F,KAAK,CAAC,kBAAkB,CAAC;MACjC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CsB,YAAY,CAAC,eAAetB,KAAK,CAACgB,OAAO,EAAE,EAAE,OAAO,CAAC;MACrD7G,KAAK,CAAC6F,KAAK,CAAC,qBAAqB,CAAC;IACpC,CAAC,SAAS;MACRhE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMuF,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFrF,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,IAAIC,iBAAiB,EAAE;QACrB,MAAMqE,UAAU,CAAC,CAAC;MACpB;MAEA,IAAI,CAACrC,cAAc,EAAE;QACnBhE,KAAK,CAAC6F,KAAK,CAAC,4BAA4B,CAAC;QACzC;MACF;MAEA,MAAMlB,QAAQ,GAAG,MAAMC,KAAK,CAAC,qBAAqB/D,aAAa,SAASmD,cAAc,CAACpD,EAAE,SAAS,EAAE;QAClGmF,MAAM,EAAE,MAAM;QACdlB,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMS,IAAI,GAAG,MAAMf,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClClB,iBAAiB,CAACyB,IAAI,CAACtE,IAAI,CAAC;QAC5BpB,KAAK,CAACmG,OAAO,CAAC,4BAA4B,CAAC;;QAE3C;QACA7B,gBAAgB,CAAC,CAAC;MACpB,CAAC,MAAM;QACL,MAAMoC,SAAS,GAAG,MAAM/B,QAAQ,CAACQ,IAAI,CAAC,CAAC;QACvCnF,KAAK,CAAC6F,KAAK,CAACa,SAAS,CAACb,KAAK,IAAI,uBAAuB,CAAC;QAEvD,IAAIa,SAAS,CAACd,iBAAiB,EAAE;UAC/BnC,mBAAmB,CAACiD,SAAS,CAACd,iBAAiB,CAAC;QAClD;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C7F,KAAK,CAAC6F,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,SAAS;MACR9D,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMsF,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;IAC/CrD,SAAS,CAACK,OAAO,GAAG+C,MAAM;IAC1BnD,SAAS,CAACI,OAAO,GAAGgD,MAAM;;IAE1B;IACAC,uBAAuB,CAACD,MAAM,CAAC;;IAE/B;IACAD,MAAM,CAACG,UAAU,CAACF,MAAM,CAACG,MAAM,CAACC,OAAO,GAAGJ,MAAM,CAACK,OAAO,CAACC,IAAI,EAAE,MAAM;MACnExB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;IAEFiB,MAAM,CAACG,UAAU,CAACF,MAAM,CAACG,MAAM,CAACC,OAAO,GAAGJ,MAAM,CAACK,OAAO,CAACE,KAAK,EAAE,MAAM;MACpEnB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;EACJ,CAAC;EAED,MAAMa,uBAAuB,GAAID,MAAM,IAAK;IAC1C;IACAA,MAAM,CAACQ,SAAS,CAACC,8BAA8B,CAAC,YAAY,EAAE;MAC5DC,sBAAsB,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAK;QAC3C,MAAMC,WAAW,GAAG,CAClB;UACEC,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAEf,MAAM,CAACQ,SAAS,CAACQ,kBAAkB,CAACC,MAAM;UAChDC,UAAU,EAAE,+CAA+C;UAC3DC,eAAe,EAAEnB,MAAM,CAACQ,SAAS,CAACY,4BAA4B,CAACC,eAAe;UAC9EC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,KAAK,EAAE,QAAQ;UACfC,IAAI,EAAEf,MAAM,CAACQ,SAAS,CAACQ,kBAAkB,CAACC,MAAM;UAChDC,UAAU,EAAE,4EAA4E;UACxFC,eAAe,EAAEnB,MAAM,CAACQ,SAAS,CAACY,4BAA4B,CAACC,eAAe;UAC9EC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAEf,MAAM,CAACQ,SAAS,CAACQ,kBAAkB,CAACC,MAAM;UAChDC,UAAU,EAAE,6CAA6C;UACzDC,eAAe,EAAEnB,MAAM,CAACQ,SAAS,CAACY,4BAA4B,CAACC,eAAe;UAC9EC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,KAAK,EAAE,iBAAiB;UACxBC,IAAI,EAAEf,MAAM,CAACQ,SAAS,CAACQ,kBAAkB,CAACC,MAAM;UAChDC,UAAU,EAAE,iCAAiC;UAC7CC,eAAe,EAAEnB,MAAM,CAACQ,SAAS,CAACY,4BAA4B,CAACC,eAAe;UAC9EC,aAAa,EAAE;QACjB,CAAC,EACD;UACER,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAEf,MAAM,CAACQ,SAAS,CAACQ,kBAAkB,CAACC,MAAM;UAChDC,UAAU,EAAE,8IAA8I;UAC1JC,eAAe,EAAEnB,MAAM,CAACQ,SAAS,CAACY,4BAA4B,CAACC,eAAe;UAC9EC,aAAa,EAAE;QACjB,CAAC,CACF;QAED,OAAO;UAAET;QAAY,CAAC;MACxB;IACF,CAAC,CAAC;;IAEF;IACAb,MAAM,CAACQ,SAAS,CAACe,qBAAqB,CAAC,YAAY,EAAE;MACnDC,YAAY,EAAEA,CAACb,KAAK,EAAEC,QAAQ,KAAK;QACjC,MAAMa,IAAI,GAAGd,KAAK,CAACe,iBAAiB,CAACd,QAAQ,CAAC;QAC9C,IAAI,CAACa,IAAI,EAAE;QAEX,MAAME,UAAU,GAAG;UACjB,aAAa,EAAE,2IAA2I;UAC1J,SAAS,EAAE,gEAAgE;UAC3E,iBAAiB,EAAE;QACrB,CAAC;QAED,IAAIA,UAAU,CAACF,IAAI,CAACA,IAAI,CAAC,EAAE;UACzB,OAAO;YACLG,KAAK,EAAE,IAAI5B,MAAM,CAAC6B,KAAK,CAACjB,QAAQ,CAACkB,UAAU,EAAEL,IAAI,CAACM,WAAW,EAAEnB,QAAQ,CAACkB,UAAU,EAAEL,IAAI,CAACO,SAAS,CAAC;YACnGC,QAAQ,EAAE,CACR;cAAEC,KAAK,EAAE,KAAKT,IAAI,CAACA,IAAI;YAAK,CAAC,EAC7B;cAAES,KAAK,EAAEP,UAAU,CAACF,IAAI,CAACA,IAAI;YAAE,CAAC;UAEpC,CAAC;QACH;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAM7B,YAAY,GAAGnK,WAAW,CAAC,CAAC6J,OAAO,EAAE6C,IAAI,GAAG,KAAK,KAAK;IAC1DrG,cAAc,CAACsG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;MAC/B9C,OAAO;MACP6C,IAAI;MACJE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;IAC3C,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAIN,KAAK,IAAK;IAClCpI,OAAO,CAACoI,KAAK,IAAI,EAAE,CAAC;IACpBxH,oBAAoB,CAACwH,KAAK,KAAKnI,YAAY,CAAC;EAC9C,CAAC;EAED,MAAM0I,oBAAoB,GAAIC,YAAY,IAAK;IAC7C5I,OAAO,CAAC4I,YAAY,CAAC;IACrBhI,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,qBAAqB,CAAC,KAAK,CAAC;EAC9B,CAAC;EAED,MAAM6H,YAAY,GAAGA,CAAA,KAAM;IACzB7G,cAAc,CAAC,EAAE,CAAC;IAClBE,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,IAAI/B,SAAS,EAAE;IACb,oBACEjB,OAAA,CAACpD,GAAG;MAACgN,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACC,SAAS,EAAC,MAAM;MAAAC,QAAA,eAC9EhK,OAAA,CAACjC,gBAAgB;QAACkM,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAAE;IAAC;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OAC3B,CAAC;EAEV;EAEA,oBACEpK,OAAA,CAACpD,GAAG;IAACyN,SAAS,EAAC,SAAS;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE,OAAO;MAAEX,OAAO,EAAE,MAAM;MAAEY,aAAa,EAAE;IAAS,CAAE;IAAAR,QAAA,gBAEzFhK,OAAA,CAACpD,GAAG;MAACgN,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACW,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAACC,EAAE,EAAE,CAAE;MAAAX,QAAA,gBACzFhK,OAAA,CAACpD,GAAG;QAACgN,OAAO,EAAC,MAAM;QAACE,UAAU,EAAC,QAAQ;QAACc,GAAG,EAAE,CAAE;QAAAZ,QAAA,gBAC7ChK,OAAA,CAACnD,UAAU;UAACgO,OAAO,EAAC,IAAI;UAACC,UAAU,EAAC,MAAM;UAAAd,QAAA,EAAC;QAE3C;UAAAE,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAY,CAAC,EACZ3I,iBAAiB,iBAChBzB,OAAA,CAACnC,IAAI;UACHiK,KAAK,EAAC,iBAAiB;UACvBiD,KAAK,EAAC,SAAS;UACfd,IAAI,EAAC,OAAO;UACZY,OAAO,EAAC;QAAU;UAAAX,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACnB,CACF,EACA3G,cAAc,iBACbzD,OAAA,CAACnC,IAAI;UACHiK,KAAK,EAAE,IAAIrE,cAAc,CAACuH,OAAO,KAAKvH,cAAc,CAACyB,MAAM,GAAI;UAC/D6F,KAAK,EAAEtH,cAAc,CAACyB,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG,SAAU;UACpE+E,IAAI,EAAC;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACb,CACF;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACE,CAAC,eAENpK,OAAA,CAACpD,GAAG;QAACgN,OAAO,EAAC,MAAM;QAACgB,GAAG,EAAE,CAAE;QAAAZ,QAAA,gBACzBhK,OAAA,CAAChC,OAAO;UAACiN,KAAK,EAAC,WAAW;UAAAjB,QAAA,eACxBhK,OAAA,CAAC3C,UAAU;YAAC6N,OAAO,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,IAAI,CAAE;YAAAkI,QAAA,eACrDhK,OAAA,CAACnB,YAAY;cAAAqL,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OAAE;UAAC;YAAAF,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OACN;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACN,CAAC,eAEVpK,OAAA,CAAChC,OAAO;UAACiN,KAAK,EAAC,SAAS;UAAAjB,QAAA,eACtBhK,OAAA,CAAC3C,UAAU;YAAC6N,OAAO,EAAEA,CAAA,KAAMlJ,oBAAoB,CAAC,IAAI,CAAE;YAAAgI,QAAA,eACpDhK,OAAA,CAACrB,WAAW;cAAAuL,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OAAE;UAAC;YAAAF,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OACL;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACN,CAAC,eAEVpK,OAAA,CAAChC,OAAO;UAACiN,KAAK,EAAC,UAAU;UAAAjB,QAAA,eACvBhK,OAAA,CAAC3C,UAAU;YAAC6N,OAAO,EAAGC,CAAC,IAAK/I,qBAAqB,CAAC+I,CAAC,CAACC,aAAa,CAAE;YAAApB,QAAA,eACjEhK,OAAA,CAACvB,YAAY;cAAAyL,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OAAE;UAAC;YAAAF,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OACN;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACN,CAAC,eAEVpK,OAAA,CAAC/B,OAAO;UAACoN,WAAW,EAAC,UAAU;UAACC,QAAQ;UAAChB,EAAE,EAAE;YAAEiB,EAAE,EAAE;UAAE;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAE,CAAC,eAE1DpK,OAAA,CAAChD,MAAM;UACL6N,OAAO,EAAC,UAAU;UAClBW,SAAS,eAAExL,OAAA,CAAC7B,QAAQ;YAAA+L,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAEpF,UAAW;UACpB2F,QAAQ,EAAEtK,QAAQ,IAAI,CAACM,iBAAkB;UAAAuI,QAAA,EAExC7I,QAAQ,GAAG,WAAW,GAAG;QAAM;UAAA+I,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAC1B,CAAC,eAETpK,OAAA,CAAChD,MAAM;UACL6N,OAAO,EAAC,UAAU;UAClBW,SAAS,eAAExL,OAAA,CAAC3B,QAAQ;YAAA6L,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OAAE,CAAE;UACxBc,OAAO,EAAE9E,UAAW;UACpBqF,QAAQ,EAAEpK,SAAU;UAAA2I,QAAA,EAEnB3I,SAAS,GAAG,YAAY,GAAG;QAAM;UAAA6I,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAC5B,CAAC,eAETpK,OAAA,CAAChD,MAAM;UACL6N,OAAO,EAAC,WAAW;UACnBW,SAAS,eAAExL,OAAA,CAACzB,UAAU;YAAA2L,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OAAE,CAAE;UAC1Bc,OAAO,EAAErE,YAAa;UACtB4E,QAAQ,EAAElK,WAAW,IAAIE,iBAAkB;UAAAuI,QAAA,EAE1CzI,WAAW,GAAG,cAAc,GAAG;QAAQ;UAAA2I,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACN,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACH,CAAC,EAGLnH,gBAAgB,CAAC6B,MAAM,GAAG,CAAC,iBAC1B9E,OAAA,CAACpD,GAAG;MAAC8N,EAAE,EAAE,CAAE;MAACD,EAAE,EAAE,CAAE;MAAAT,QAAA,eAChBhK,OAAA,CAAClC,KAAK;QAAC4N,QAAQ,EAAC,OAAO;QAACpB,EAAE,EAAE;UAAEG,EAAE,EAAE;QAAE,CAAE;QAAAT,QAAA,gBACpChK,OAAA,CAACnD,UAAU;UAACgO,OAAO,EAAC,WAAW;UAACc,YAAY;UAAA3B,QAAA,EAAC;QAE7C;UAAAE,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAY,CAAC,EACZnH,gBAAgB,CAAC2I,GAAG,CAAC,CAACtG,KAAK,EAAEuG,KAAK,kBACjC7L,OAAA,CAACnD,UAAU;UAAagO,OAAO,EAAC,OAAO;UAAAb,QAAA,GAAC,SACpC,EAAC1E,KAAK;QAAA,GADOuG,KAAK;UAAA3B,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAEV,CACb,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACG;IAAC;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACL,CACN,eAGDpK,OAAA,CAACpD,GAAG;MAAC0N,EAAE,EAAE;QAAEwB,IAAI,EAAE,CAAC;QAAElC,OAAO,EAAE,MAAM;QAAEmC,QAAQ,EAAE;MAAS,CAAE;MAAA/B,QAAA,eACxDhK,OAAA,CAAC/C,IAAI;QAAC+O,SAAS;QAAC1B,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAO,CAAE;QAAAP,QAAA,gBAErChK,OAAA,CAAC/C,IAAI;UAACgP,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAC7B,EAAE,EAAE;YAAEC,MAAM,EAAE,MAAM;YAAEX,OAAO,EAAE,MAAM;YAAEY,aAAa,EAAE;UAAS,CAAE;UAAAR,QAAA,eACzFhK,OAAA,CAAClD,IAAI;YAACwN,EAAE,EAAE;cAAEwB,IAAI,EAAE,CAAC;cAAElC,OAAO,EAAE,MAAM;cAAEY,aAAa,EAAE;YAAS,CAAE;YAAAR,QAAA,eAC9DhK,OAAA,CAACjD,WAAW;cAACuN,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAEM,CAAC,EAAE,CAAC;gBAAE,cAAc,EAAE;kBAAEC,EAAE,EAAE;gBAAE;cAAE,CAAE;cAAArC,QAAA,eAC5DhK,OAAA,CAACR,MAAM;gBACL+K,MAAM,EAAC,MAAM;gBACb+B,eAAe,EAAC,YAAY;gBAC5BpD,KAAK,EAAErI,IAAK;gBACZ0L,QAAQ,EAAE/C,gBAAiB;gBAC3BgD,OAAO,EAAE1F,oBAAqB;gBAC9B2F,KAAK,EAAEpK,WAAY;gBACnBqK,OAAO,EAAE;kBACPnK,QAAQ,EAAEA,QAAQ;kBAClBE,QAAQ,EAAEA,QAAQ;kBAClBE,OAAO,EAAE;oBAAEgK,OAAO,EAAEhK;kBAAQ,CAAC;kBAC7BiK,eAAe,EAAE,IAAI;kBACrBC,oBAAoB,EAAE,KAAK;kBAC3BC,mBAAmB,EAAE,KAAK;kBAC1BC,mBAAmB,EAAE,IAAI;kBACzBC,aAAa,EAAE,QAAQ;kBACvBC,mBAAmB,EAAE,QAAQ;kBAC7BC,iBAAiB,EAAE,QAAQ;kBAC3BC,OAAO,EAAE,IAAI;kBACbC,gBAAgB,EAAE,IAAI;kBACtBC,mBAAmB,EAAE,QAAQ;kBAC7BC,eAAe,EAAE,IAAI;kBACrBC,cAAc,EAAE,QAAQ;kBACxBC,0BAA0B,EAAE,IAAI;kBAChCC,OAAO,EAAE;oBACPC,YAAY,EAAE,IAAI;oBAClBC,YAAY,EAAE,IAAI;oBAClBC,aAAa,EAAE,IAAI;oBACnBC,gBAAgB,EAAE,IAAI;oBACtBC,UAAU,EAAE,IAAI;oBAChBC,aAAa,EAAE,IAAI;oBACnBC,WAAW,EAAE,IAAI;oBACjBC,WAAW,EAAE,IAAI;oBACjBC,cAAc,EAAE,IAAI;oBACpBC,WAAW,EAAE,IAAI;oBACjBC,cAAc,EAAE,IAAI;oBACpBC,UAAU,EAAE,IAAI;oBAChBC,aAAa,EAAE,IAAI;oBACnBC,SAAS,EAAE,IAAI;oBACfC,UAAU,EAAE,IAAI;oBAChBC,aAAa,EAAE,IAAI;oBACnBC,SAAS,EAAE,IAAI;oBACfC,eAAe,EAAE,IAAI;oBACrBC,UAAU,EAAE,IAAI;oBAChBC,SAAS,EAAE,IAAI;oBACfC,cAAc,EAAE,IAAI;oBACpBC,WAAW,EAAE,IAAI;oBACjBC,kBAAkB,EAAE,IAAI;oBACxBC,UAAU,EAAE,IAAI;oBAChBC,SAAS,EAAE;kBACb;gBACF;cAAE;gBAAAhF,QAAA,EAAAC,YAAA;gBAAArB,UAAA;gBAAAsB,YAAA;cAAA,OACH;YAAC;cAAAF,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OACS;UAAC;YAAAF,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OACV;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACH,CAAC,eAGPpK,OAAA,CAAC/C,IAAI;UAACgP,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAC7B,EAAE,EAAE;YAAEC,MAAM,EAAE,MAAM;YAAEX,OAAO,EAAE,MAAM;YAAEY,aAAa,EAAE;UAAS,CAAE;UAAAR,QAAA,eACzFhK,OAAA,CAAClD,IAAI;YAACwN,EAAE,EAAE;cAAEwB,IAAI,EAAE,CAAC;cAAElC,OAAO,EAAE,MAAM;cAAEY,aAAa,EAAE;YAAS,CAAE;YAAAR,QAAA,gBAC9DhK,OAAA,CAACpD,GAAG;cAAC0N,EAAE,EAAE;gBAAE6E,YAAY,EAAE,CAAC;gBAAEC,WAAW,EAAE;cAAU,CAAE;cAAApF,QAAA,eACnDhK,OAAA,CAAC7C,IAAI;gBACH+L,KAAK,EAAEvH,SAAU;gBACjB4K,QAAQ,EAAEA,CAACpB,CAAC,EAAEkE,QAAQ,KAAKzN,YAAY,CAACyN,QAAQ,CAAE;gBAClDxE,OAAO,EAAC,WAAW;gBAAAb,QAAA,gBAEnBhK,OAAA,CAAC5C,GAAG;kBAAC0K,KAAK,EAAC;gBAAS;kBAAAoC,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAE,CAAC,eACvBpK,OAAA,CAAC5C,GAAG;kBAAC0K,KAAK,EAAC;gBAAM;kBAAAoC,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAF,QAAA,EAAAC,YAAA;gBAAArB,UAAA;gBAAAsB,YAAA;cAAA,OAChB;YAAC;cAAAF,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OACJ,CAAC,eAENpK,OAAA,CAACjD,WAAW;cAACuN,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAEM,CAAC,EAAE,CAAC;gBAAEL,QAAQ,EAAE;cAAS,CAAE;cAAA/B,QAAA,GACpDrI,SAAS,KAAK,CAAC,iBACd3B,OAAA,CAACL,WAAW;gBACV2P,IAAI,EAAEzM,WAAY;gBAClB0M,OAAO,EAAE5F,YAAa;gBACtB5G,WAAW,EAAEA;cAAY;gBAAAmH,QAAA,EAAAC,YAAA;gBAAArB,UAAA;gBAAAsB,YAAA;cAAA,OAC1B,CACF,EAEAzI,SAAS,KAAK,CAAC,iBACd3B,OAAA,CAACpD,GAAG;gBAACwP,CAAC,EAAE,CAAE;gBAAApC,QAAA,gBACRhK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,IAAI;kBAACc,YAAY;kBAAA3B,QAAA,EAAC;gBAEtC;kBAAAE,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAY,CAAC,eAEbpK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAACY,YAAY;kBAAA3B,QAAA,GAAC,kBAC9C,EAAC1J,aAAa;gBAAA;kBAAA4J,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OACpB,CAAC,EAEZ3G,cAAc,iBACbzD,OAAA,CAAAE,SAAA;kBAAA8J,QAAA,gBACEhK,OAAA,CAACnD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACE,KAAK,EAAC,gBAAgB;oBAACY,YAAY;oBAAA3B,QAAA,GAAC,WACrD,EAACvG,cAAc,CAACuH,OAAO;kBAAA;oBAAAd,QAAA,EAAAC,YAAA;oBAAArB,UAAA;oBAAAsB,YAAA;kBAAA,OACtB,CAAC,eACbpK,OAAA,CAACnD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACE,KAAK,EAAC,gBAAgB;oBAACY,YAAY;oBAAA3B,QAAA,GAAC,UACtD,EAACvG,cAAc,CAACyB,MAAM;kBAAA;oBAAAgF,QAAA,EAAAC,YAAA;oBAAArB,UAAA;oBAAAsB,YAAA;kBAAA,OACpB,CAAC,eACbpK,OAAA,CAACnD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACE,KAAK,EAAC,gBAAgB;oBAACY,YAAY;oBAAA3B,QAAA,GAAC,WACrD,EAAC,IAAIV,IAAI,CAAC7F,cAAc,CAAC+L,UAAU,CAAC,CAACC,cAAc,CAAC,CAAC;kBAAA;oBAAAvF,QAAA,EAAAC,YAAA;oBAAArB,UAAA;oBAAAsB,YAAA;kBAAA,OACpD,CAAC,EACZ3G,cAAc,CAACiM,WAAW,iBACzB1P,OAAA,CAACnD,UAAU;oBAACgO,OAAO,EAAC,OAAO;oBAACE,KAAK,EAAC,gBAAgB;oBAACY,YAAY;oBAAA3B,QAAA,GAAC,YACpD,EAAC,IAAIV,IAAI,CAAC7F,cAAc,CAACiM,WAAW,CAAC,CAACD,cAAc,CAAC,CAAC;kBAAA;oBAAAvF,QAAA,EAAAC,YAAA;oBAAArB,UAAA;oBAAAsB,YAAA;kBAAA,OACtD,CACb;gBAAA,eACD,CACH,eAEDpK,OAAA,CAAC/B,OAAO;kBAACqM,EAAE,EAAE;oBAAEqF,EAAE,EAAE;kBAAE;gBAAE;kBAAAzF,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAE,CAAC,eAE1BpK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,WAAW;kBAACc,YAAY;kBAAA3B,QAAA,EAAC;gBAE7C;kBAAAE,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAY,CAAC,eACbpK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAACY,YAAY;kBAAA3B,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAY,CAAC,eACbpK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAACY,YAAY;kBAAA3B,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAY,CAAC,eACbpK,OAAA,CAACnD,UAAU;kBAACgO,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,gBAAgB;kBAACY,YAAY;kBAAA3B,QAAA,EAAC;gBAEhE;kBAAAE,QAAA,EAAAC,YAAA;kBAAArB,UAAA;kBAAAsB,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAF,QAAA,EAAAC,YAAA;gBAAArB,UAAA;gBAAAsB,YAAA;cAAA,OACV,CACN;YAAA;cAAAF,QAAA,EAAAC,YAAA;cAAArB,UAAA;cAAAsB,YAAA;YAAA,OACU,CAAC;UAAA;YAAAF,QAAA,EAAAC,YAAA;YAAArB,UAAA;YAAAsB,YAAA;UAAA,OACV;QAAC;UAAAF,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACH,CAAC;MAAA;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACH;IAAC;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACJ,CAAC,eAENpK,OAAA,CAAC1C,IAAI;MACHsS,QAAQ,EAAEzN,kBAAmB;MAC7B0N,IAAI,EAAEC,OAAO,CAAC3N,kBAAkB,CAAE;MAClC4N,OAAO,EAAEA,CAAA,KAAM3N,qBAAqB,CAAC,IAAI,CAAE;MAAA4H,QAAA,gBAE3ChK,OAAA,CAACzC,QAAQ;QAAC2N,OAAO,EAAEA,CAAA,KAAM;UACvB5I,cAAc,CAACD,WAAW,KAAK,SAAS,GAAG,OAAO,GAAG,SAAS,CAAC;UAC/DD,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAE;QAAA4H,QAAA,EAAC;MAEH;QAAAE,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAAU,CAAC,eACXpK,OAAA,CAACzC,QAAQ;QAAC2N,OAAO,EAAEA,CAAA,KAAM;UACvB1I,WAAW,CAACD,QAAQ,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;UACtCH,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAE;QAAA4H,QAAA,GAAC,aACU,EAACzH,QAAQ,EAAC,IACvB;MAAA;QAAA2H,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAAU,CAAC,eACXpK,OAAA,CAACzC,QAAQ;QAAC2N,OAAO,EAAEA,CAAA,KAAM;UACvBxI,WAAW,CAACD,QAAQ,KAAK,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC;UAC7CL,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAE;QAAA4H,QAAA,GAAC,aACU,EAACvH,QAAQ;MAAA;QAAAyH,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACZ,CAAC,eACXpK,OAAA,CAACzC,QAAQ;QAAC2N,OAAO,EAAEA,CAAA,KAAM;UACvBtI,UAAU,CAAC,CAACD,OAAO,CAAC;UACpBP,qBAAqB,CAAC,IAAI,CAAC;QAC7B,CAAE;QAAA4H,QAAA,GAAC,WACQ,EAACrH,OAAO,GAAG,IAAI,GAAG,KAAK;MAAA;QAAAuH,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACP,CAAC,eAGPpK,OAAA,CAACxC,MAAM;MACLqS,IAAI,EAAEhO,kBAAmB;MACzBkO,OAAO,EAAEA,CAAA,KAAMjO,qBAAqB,CAAC,KAAK,CAAE;MAC5CkO,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAjG,QAAA,gBAEThK,OAAA,CAACvC,WAAW;QAAAuM,QAAA,EAAC;MAEb;QAAAE,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAAa,CAAC,eACdpK,OAAA,CAACtC,aAAa;QAAAsM,QAAA,eACZhK,OAAA,CAACN,oBAAoB;UAACwQ,QAAQ,EAAEzG;QAAqB;UAAAS,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAE;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAC3C,CAAC,eAChBpK,OAAA,CAACrC,aAAa;QAAAqM,QAAA,eACZhK,OAAA,CAAChD,MAAM;UAACkO,OAAO,EAAEA,CAAA,KAAMpJ,qBAAqB,CAAC,KAAK,CAAE;UAAAkI,QAAA,EAAC;QAErD;UAAAE,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAQ;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACI,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACV,CAAC,eAGTpK,OAAA,CAACxC,MAAM;MACLqS,IAAI,EAAE9N,iBAAkB;MACxBgO,OAAO,EAAEA,CAAA,KAAM/N,oBAAoB,CAAC,KAAK,CAAE;MAC3CgO,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAAjG,QAAA,gBAEThK,OAAA,CAACvC,WAAW;QAAAuM,QAAA,EAAC;MAEb;QAAAE,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OAAa,CAAC,eACdpK,OAAA,CAACtC,aAAa;QAAAsM,QAAA,eACZhK,OAAA,CAACH,WAAW;UACVsQ,QAAQ,EAAE5M,YAAa;UACvBE,cAAc,EAAEA,cAAe;UAC/B2M,SAAS,EAAGpF,OAAO,IAAK;YACtBlK,OAAO,CAACkK,OAAO,CAACnK,IAAI,CAAC;YACrBa,oBAAoB,CAAC,IAAI,CAAC;YAC1BM,oBAAoB,CAAC,KAAK,CAAC;YAC3BvC,KAAK,CAACmG,OAAO,CAAC,4BAA4B,CAAC;UAC7C;QAAE;UAAAsE,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OACH;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACW,CAAC,eAChBpK,OAAA,CAACrC,aAAa;QAAAqM,QAAA,eACZhK,OAAA,CAAChD,MAAM;UAACkO,OAAO,EAAEA,CAAA,KAAMlJ,oBAAoB,CAAC,KAAK,CAAE;UAAAgI,QAAA,EAAC;QAEpD;UAAAE,QAAA,EAAAC,YAAA;UAAArB,UAAA;UAAAsB,YAAA;QAAA,OAAQ;MAAC;QAAAF,QAAA,EAAAC,YAAA;QAAArB,UAAA;QAAAsB,YAAA;MAAA,OACI,CAAC;IAAA;MAAAF,QAAA,EAAAC,YAAA;MAAArB,UAAA;MAAAsB,YAAA;IAAA,OACV,CAAC;EAAA;IAAAF,QAAA,EAAAC,YAAA;IAAArB,UAAA;IAAAsB,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChK,EAAA,CArwBID,UAAU;EAAA,QACwBzD,SAAS,EAC9BC,WAAW;AAAA;AAAA0T,EAAA,GAFxBlQ,UAAU;AAuwBhB,eAAeA,UAAU;AAAC,IAAAkQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}