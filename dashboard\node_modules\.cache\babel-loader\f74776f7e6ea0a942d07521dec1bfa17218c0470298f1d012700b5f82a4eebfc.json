{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n  isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]';\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\nmodule.exports = baseIsArguments;", "map": {"version": 3, "names": ["baseGetTag", "require", "isObjectLike", "argsTag", "baseIsArguments", "value", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_baseIsArguments.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]';\n\n/**\n * The base implementation of `_.isArguments`.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n */\nfunction baseIsArguments(value) {\n  return isObjectLike(value) && baseGetTag(value) == argsTag;\n}\n\nmodule.exports = baseIsArguments;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIE,OAAO,GAAG,oBAAoB;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAOH,YAAY,CAACG,KAAK,CAAC,IAAIL,UAAU,CAACK,KAAK,CAAC,IAAIF,OAAO;AAC5D;AAEAG,MAAM,CAACC,OAAO,GAAGH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}