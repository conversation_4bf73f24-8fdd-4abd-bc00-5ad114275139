{"ast": null, "code": "/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */\n;\n(function (globalScope) {\n  'use strict';\n\n  /*\r\n   *  decimal.js-light v2.5.1\r\n   *  An arbitrary-precision Decimal type for JavaScript.\r\n   *  https://github.com/MikeMcl/decimal.js-light\r\n   *  Copyright (c) 2020 <PERSON> <<EMAIL>>\r\n   *  MIT Expat Licence\r\n   */\n\n  // -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\n\n  // The limit on the value of `precision`, and on the value of the first argument to\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\n  var MAX_DIGITS = 1e9,\n    // 0 to 1e9\n\n    // The initial configuration properties of the Decimal constructor.\n    Decimal = {\n      // These values must be integers within the stated ranges (inclusive).\n      // Most of these values can be changed during run-time using `Decimal.config`.\n\n      // The maximum number of significant digits of the result of a calculation or base conversion.\n      // E.g. `Decimal.config({ precision: 20 });`\n      precision: 20,\n      // 1 to MAX_DIGITS\n\n      // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\n      // `toFixed`, `toPrecision` and `toSignificantDigits`.\n      //\n      // ROUND_UP         0 Away from zero.\n      // ROUND_DOWN       1 Towards zero.\n      // ROUND_CEIL       2 Towards +Infinity.\n      // ROUND_FLOOR      3 Towards -Infinity.\n      // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n      // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n      // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n      // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n      // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n      //\n      // E.g.\n      // `Decimal.rounding = 4;`\n      // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n      rounding: 4,\n      // 0 to 8\n\n      // The exponent value at and beneath which `toString` returns exponential notation.\n      // JavaScript numbers: -7\n      toExpNeg: -7,\n      // 0 to -MAX_E\n\n      // The exponent value at and above which `toString` returns exponential notation.\n      // JavaScript numbers: 21\n      toExpPos: 21,\n      // 0 to MAX_E\n\n      // The natural logarithm of 10.\n      // 115 digits\n      LN10: '2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286'\n    },\n    // ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\n\n    external = true,\n    decimalError = '[DecimalError] ',\n    invalidArgument = decimalError + 'Invalid argument: ',\n    exponentOutOfRange = decimalError + 'Exponent out of range: ',\n    mathfloor = Math.floor,\n    mathpow = Math.pow,\n    isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\n    ONE,\n    BASE = 1e7,\n    LOG_BASE = 7,\n    MAX_SAFE_INTEGER = 9007199254740991,\n    MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE),\n    // 1286742750677284\n\n    // Decimal.prototype object\n    P = {};\n\n  // Decimal prototype methods\n\n  /*\r\n   *  absoluteValue                       abs\r\n   *  comparedTo                          cmp\r\n   *  decimalPlaces                       dp\r\n   *  dividedBy                           div\r\n   *  dividedToIntegerBy                  idiv\r\n   *  equals                              eq\r\n   *  exponent\r\n   *  greaterThan                         gt\r\n   *  greaterThanOrEqualTo                gte\r\n   *  isInteger                           isint\r\n   *  isNegative                          isneg\r\n   *  isPositive                          ispos\r\n   *  isZero\r\n   *  lessThan                            lt\r\n   *  lessThanOrEqualTo                   lte\r\n   *  logarithm                           log\r\n   *  minus                               sub\r\n   *  modulo                              mod\r\n   *  naturalExponential                  exp\r\n   *  naturalLogarithm                    ln\r\n   *  negated                             neg\r\n   *  plus                                add\r\n   *  precision                           sd\r\n   *  squareRoot                          sqrt\r\n   *  times                               mul\r\n   *  toDecimalPlaces                     todp\r\n   *  toExponential\r\n   *  toFixed\r\n   *  toInteger                           toint\r\n   *  toNumber\r\n   *  toPower                             pow\r\n   *  toPrecision\r\n   *  toSignificantDigits                 tosd\r\n   *  toString\r\n   *  valueOf                             val\r\n   */\n\n  /*\r\n   * Return a new Decimal whose value is the absolute value of this Decimal.\r\n   *\r\n   */\n  P.absoluteValue = P.abs = function () {\n    var x = new this.constructor(this);\n    if (x.s) x.s = 1;\n    return x;\n  };\n\n  /*\r\n   * Return\r\n   *   1    if the value of this Decimal is greater than the value of `y`,\r\n   *  -1    if the value of this Decimal is less than the value of `y`,\r\n   *   0    if they have the same value\r\n   *\r\n   */\n  P.comparedTo = P.cmp = function (y) {\n    var i,\n      j,\n      xdL,\n      ydL,\n      x = this;\n    y = new x.constructor(y);\n\n    // Signs differ?\n    if (x.s !== y.s) return x.s || -y.s;\n\n    // Compare exponents.\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\n    xdL = x.d.length;\n    ydL = y.d.length;\n\n    // Compare digit by digit.\n    for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\n      if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\n    }\n\n    // Compare lengths.\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\n  };\n\n  /*\r\n   * Return the number of decimal places of the value of this Decimal.\r\n   *\r\n   */\n  P.decimalPlaces = P.dp = function () {\n    var x = this,\n      w = x.d.length - 1,\n      dp = (w - x.e) * LOG_BASE;\n\n    // Subtract the number of trailing zeros of the last word.\n    w = x.d[w];\n    if (w) for (; w % 10 == 0; w /= 10) dp--;\n    return dp < 0 ? 0 : dp;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\n  P.dividedBy = P.div = function (y) {\n    return divide(this, new this.constructor(y));\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n   * by the value of `y`, truncated to `precision` significant digits.\r\n   *\r\n   */\n  P.dividedToIntegerBy = P.idiv = function (y) {\n    var x = this,\n      Ctor = x.constructor;\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n   *\r\n   */\n  P.equals = P.eq = function (y) {\n    return !this.cmp(y);\n  };\n\n  /*\r\n   * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n   *\r\n   */\n  P.exponent = function () {\n    return getBase10Exponent(this);\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n   * false.\r\n   *\r\n   */\n  P.greaterThan = P.gt = function (y) {\n    return this.cmp(y) > 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n   * otherwise return false.\r\n   *\r\n   */\n  P.greaterThanOrEqualTo = P.gte = function (y) {\n    return this.cmp(y) >= 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is an integer, otherwise return false.\r\n   *\r\n   */\n  P.isInteger = P.isint = function () {\n    return this.e > this.d.length - 2;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is negative, otherwise return false.\r\n   *\r\n   */\n  P.isNegative = P.isneg = function () {\n    return this.s < 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is positive, otherwise return false.\r\n   *\r\n   */\n  P.isPositive = P.ispos = function () {\n    return this.s > 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is 0, otherwise return false.\r\n   *\r\n   */\n  P.isZero = function () {\n    return this.s === 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n   *\r\n   */\n  P.lessThan = P.lt = function (y) {\n    return this.cmp(y) < 0;\n  };\n\n  /*\r\n   * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n   *\r\n   */\n  P.lessThanOrEqualTo = P.lte = function (y) {\n    return this.cmp(y) < 1;\n  };\n\n  /*\r\n   * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   * If no base is specified, return log[10](x).\r\n   *\r\n   * log[base](x) = ln(x) / ln(base)\r\n   *\r\n   * The maximum error of the result is 1 ulp (unit in the last place).\r\n   *\r\n   * [base] {number|string|Decimal} The base of the logarithm.\r\n   *\r\n   */\n  P.logarithm = P.log = function (base) {\n    var r,\n      x = this,\n      Ctor = x.constructor,\n      pr = Ctor.precision,\n      wpr = pr + 5;\n\n    // Default base is 10.\n    if (base === void 0) {\n      base = new Ctor(10);\n    } else {\n      base = new Ctor(base);\n\n      // log[-b](x) = NaN\n      // log[0](x)  = NaN\n      // log[1](x)  = NaN\n      if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + 'NaN');\n    }\n\n    // log[b](-x) = NaN\n    // log[b](0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\n\n    // log[b](1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    external = false;\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\n    external = true;\n    return round(r, pr);\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\n  P.minus = P.sub = function (y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\n  P.modulo = P.mod = function (y) {\n    var q,\n      x = this,\n      Ctor = x.constructor,\n      pr = Ctor.precision;\n    y = new Ctor(y);\n\n    // x % 0 = NaN\n    if (!y.s) throw Error(decimalError + 'NaN');\n\n    // Return x if x is 0.\n    if (!x.s) return round(new Ctor(x), pr);\n\n    // Prevent rounding of intermediate calculations.\n    external = false;\n    q = divide(x, y, 0, 1).times(y);\n    external = true;\n    return x.minus(q);\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n   * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\n  P.naturalExponential = P.exp = function () {\n    return exp(this);\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   */\n  P.naturalLogarithm = P.ln = function () {\n    return ln(this);\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n   * -1.\r\n   *\r\n   */\n  P.negated = P.neg = function () {\n    var x = new this.constructor(this);\n    x.s = -x.s || 0;\n    return x;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\n  P.plus = P.add = function (y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\n  };\n\n  /*\r\n   * Return the number of significant digits of the value of this Decimal.\r\n   *\r\n   * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n   *\r\n   */\n  P.precision = P.sd = function (z) {\n    var e,\n      sd,\n      w,\n      x = this;\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n    e = getBase10Exponent(x) + 1;\n    w = x.d.length - 1;\n    sd = w * LOG_BASE + 1;\n    w = x.d[w];\n\n    // If non-zero...\n    if (w) {\n      // Subtract the number of trailing zeros of the last word.\n      for (; w % 10 == 0; w /= 10) sd--;\n\n      // Add the number of digits of the first word.\n      for (w = x.d[0]; w >= 10; w /= 10) sd++;\n    }\n    return z && e > sd ? e : sd;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\n  P.squareRoot = P.sqrt = function () {\n    var e,\n      n,\n      pr,\n      r,\n      s,\n      t,\n      wpr,\n      x = this,\n      Ctor = x.constructor;\n\n    // Negative or zero?\n    if (x.s < 1) {\n      if (!x.s) return new Ctor(0);\n\n      // sqrt(-x) = NaN\n      throw Error(decimalError + 'NaN');\n    }\n    e = getBase10Exponent(x);\n    external = false;\n\n    // Initial estimate.\n    s = Math.sqrt(+x);\n\n    // Math.sqrt underflow/overflow?\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n    if (s == 0 || s == 1 / 0) {\n      n = digitsToString(x.d);\n      if ((n.length + e) % 2 == 0) n += '0';\n      s = Math.sqrt(n);\n      e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n      if (s == 1 / 0) {\n        n = '5e' + e;\n      } else {\n        n = s.toExponential();\n        n = n.slice(0, n.indexOf('e') + 1) + e;\n      }\n      r = new Ctor(n);\n    } else {\n      r = new Ctor(s.toString());\n    }\n    pr = Ctor.precision;\n    s = wpr = pr + 3;\n\n    // Newton-Raphson iteration.\n    for (;;) {\n      t = r;\n      r = t.plus(divide(x, t, wpr + 2)).times(0.5);\n      if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\n        n = n.slice(wpr - 3, wpr + 1);\n\n        // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n        // 4999, i.e. approaching a rounding boundary, continue the iteration.\n        if (s == wpr && n == '4999') {\n          // On the first iteration only, check to see if rounding up gives the exact result as the\n          // nines may infinitely repeat.\n          round(t, pr + 1, 0);\n          if (t.times(t).eq(x)) {\n            r = t;\n            break;\n          }\n        } else if (n != '9999') {\n          break;\n        }\n        wpr += 4;\n      }\n    }\n    external = true;\n    return round(r, pr);\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\n  P.times = P.mul = function (y) {\n    var carry,\n      e,\n      i,\n      k,\n      r,\n      rL,\n      t,\n      xdL,\n      ydL,\n      x = this,\n      Ctor = x.constructor,\n      xd = x.d,\n      yd = (y = new Ctor(y)).d;\n\n    // Return 0 if either is 0.\n    if (!x.s || !y.s) return new Ctor(0);\n    y.s *= x.s;\n    e = x.e + y.e;\n    xdL = xd.length;\n    ydL = yd.length;\n\n    // Ensure xd points to the longer array.\n    if (xdL < ydL) {\n      r = xd;\n      xd = yd;\n      yd = r;\n      rL = xdL;\n      xdL = ydL;\n      ydL = rL;\n    }\n\n    // Initialise the result array with zeros.\n    r = [];\n    rL = xdL + ydL;\n    for (i = rL; i--;) r.push(0);\n\n    // Multiply!\n    for (i = ydL; --i >= 0;) {\n      carry = 0;\n      for (k = xdL + i; k > i;) {\n        t = r[k] + yd[i] * xd[k - i - 1] + carry;\n        r[k--] = t % BASE | 0;\n        carry = t / BASE | 0;\n      }\n      r[k] = (r[k] + carry) % BASE | 0;\n    }\n\n    // Remove trailing zeros.\n    for (; !r[--rL];) r.pop();\n    if (carry) ++e;else r.shift();\n    y.d = r;\n    y.e = e;\n    return external ? round(y, Ctor.precision) : y;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n   * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n   *\r\n   * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\n  P.toDecimalPlaces = P.todp = function (dp, rm) {\n    var x = this,\n      Ctor = x.constructor;\n    x = new Ctor(x);\n    if (dp === void 0) return x;\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\n  };\n\n  /*\r\n   * Return a string representing the value of this Decimal in exponential notation rounded to\r\n   * `dp` fixed decimal places using rounding mode `rounding`.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\n  P.toExponential = function (dp, rm) {\n    var str,\n      x = this,\n      Ctor = x.constructor;\n    if (dp === void 0) {\n      str = toString(x, true);\n    } else {\n      checkInt32(dp, 0, MAX_DIGITS);\n      if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n      x = round(new Ctor(x), dp + 1, rm);\n      str = toString(x, true, dp + 1);\n    }\n    return str;\n  };\n\n  /*\r\n   * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n   * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n   * omitted.\r\n   *\r\n   * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n   * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n   * (-0).toFixed(3) is '0.000'.\r\n   * (-0.5).toFixed(0) is '-0'.\r\n   *\r\n   */\n  P.toFixed = function (dp, rm) {\n    var str,\n      y,\n      x = this,\n      Ctor = x.constructor;\n    if (dp === void 0) return toString(x);\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\n\n    // To determine whether to add the minus sign look at the value before it was rounded,\n    // i.e. look at `x` rather than `y`.\n    return x.isneg() && !x.isZero() ? '-' + str : str;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n   * rounding mode `rounding`.\r\n   *\r\n   */\n  P.toInteger = P.toint = function () {\n    var x = this,\n      Ctor = x.constructor;\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\n  };\n\n  /*\r\n   * Return the value of this Decimal converted to a number primitive.\r\n   *\r\n   */\n  P.toNumber = function () {\n    return +this;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   * For non-integer or very large exponents pow(x, y) is calculated using\r\n   *\r\n   *   x^y = exp(y*ln(x))\r\n   *\r\n   * The maximum error is 1 ulp (unit in last place).\r\n   *\r\n   * y {number|string|Decimal} The power to which to raise this Decimal.\r\n   *\r\n   */\n  P.toPower = P.pow = function (y) {\n    var e,\n      k,\n      pr,\n      r,\n      sign,\n      yIsInt,\n      x = this,\n      Ctor = x.constructor,\n      guard = 12,\n      yn = +(y = new Ctor(y));\n\n    // pow(x, 0) = 1\n    if (!y.s) return new Ctor(ONE);\n    x = new Ctor(x);\n\n    // pow(0, y > 0) = 0\n    // pow(0, y < 0) = Infinity\n    if (!x.s) {\n      if (y.s < 1) throw Error(decimalError + 'Infinity');\n      return x;\n    }\n\n    // pow(1, y) = 1\n    if (x.eq(ONE)) return x;\n    pr = Ctor.precision;\n\n    // pow(x, 1) = x\n    if (y.eq(ONE)) return round(x, pr);\n    e = y.e;\n    k = y.d.length - 1;\n    yIsInt = e >= k;\n    sign = x.s;\n    if (!yIsInt) {\n      // pow(x < 0, y non-integer) = NaN\n      if (sign < 0) throw Error(decimalError + 'NaN');\n\n      // If y is a small integer use the 'exponentiation by squaring' algorithm.\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n      r = new Ctor(ONE);\n\n      // Max k of 9007199254740991 takes 53 loop iterations.\n      // Maximum digits array length; leaves [28, 34] guard digits.\n      e = Math.ceil(pr / LOG_BASE + 4);\n      external = false;\n      for (;;) {\n        if (k % 2) {\n          r = r.times(x);\n          truncate(r.d, e);\n        }\n        k = mathfloor(k / 2);\n        if (k === 0) break;\n        x = x.times(x);\n        truncate(x.d, e);\n      }\n      external = true;\n      return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\n    }\n\n    // Result is negative if x is negative and the last digit of integer y is odd.\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\n    x.s = 1;\n    external = false;\n    r = y.times(ln(x, pr + guard));\n    external = true;\n    r = exp(r);\n    r.s = sign;\n    return r;\n  };\n\n  /*\r\n   * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n   * using rounding mode `rounding`.\r\n   *\r\n   * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n   * the integer part of the value in normal notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\n  P.toPrecision = function (sd, rm) {\n    var e,\n      str,\n      x = this,\n      Ctor = x.constructor;\n    if (sd === void 0) {\n      e = getBase10Exponent(x);\n      str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n    } else {\n      checkInt32(sd, 1, MAX_DIGITS);\n      if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n      x = round(new Ctor(x), sd, rm);\n      e = getBase10Exponent(x);\n      str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\n    }\n    return str;\n  };\n\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n   * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n   * omitted.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\n  P.toSignificantDigits = P.tosd = function (sd, rm) {\n    var x = this,\n      Ctor = x.constructor;\n    if (sd === void 0) {\n      sd = Ctor.precision;\n      rm = Ctor.rounding;\n    } else {\n      checkInt32(sd, 1, MAX_DIGITS);\n      if (rm === void 0) rm = Ctor.rounding;else checkInt32(rm, 0, 8);\n    }\n    return round(new Ctor(x), sd, rm);\n  };\n\n  /*\r\n   * Return a string representing the value of this Decimal.\r\n   *\r\n   * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n   * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n   *\r\n   */\n  P.toString = P.valueOf = P.val = P.toJSON = function () {\n    var x = this,\n      e = getBase10Exponent(x),\n      Ctor = x.constructor;\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n  };\n\n  // Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n\n  /*\r\n   *  add                 P.minus, P.plus\r\n   *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n   *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n   *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n   *  exp                 P.exp, P.pow\r\n   *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n   *                      P.toString, divide, round, toString, exp, ln\r\n   *  getLn10             P.log, ln\r\n   *  getZeroString       digitsToString, toString\r\n   *  ln                  P.log, P.ln, P.pow, exp\r\n   *  parseDecimal        Decimal\r\n   *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n   *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n   *                      divide, getLn10, exp, ln\r\n   *  subtract            P.minus, P.plus\r\n   *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n   *  truncate            P.pow\r\n   *\r\n   *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n   *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n   */\n\n  function add(x, y) {\n    var carry,\n      d,\n      e,\n      i,\n      k,\n      len,\n      xd,\n      yd,\n      Ctor = x.constructor,\n      pr = Ctor.precision;\n\n    // If either is zero...\n    if (!x.s || !y.s) {\n      // Return x if y is zero.\n      // Return y if y is non-zero.\n      if (!y.s) y = new Ctor(x);\n      return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n\n    // x and y are finite, non-zero numbers with the same sign.\n\n    k = x.e;\n    e = y.e;\n    xd = xd.slice();\n    i = k - e;\n\n    // If base 1e7 exponents differ...\n    if (i) {\n      if (i < 0) {\n        d = xd;\n        i = -i;\n        len = yd.length;\n      } else {\n        d = yd;\n        e = k;\n        len = xd.length;\n      }\n\n      // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n      k = Math.ceil(pr / LOG_BASE);\n      len = k > len ? k + 1 : len + 1;\n      if (i > len) {\n        i = len;\n        d.length = 1;\n      }\n\n      // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n      d.reverse();\n      for (; i--;) d.push(0);\n      d.reverse();\n    }\n    len = xd.length;\n    i = yd.length;\n\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n    if (len - i < 0) {\n      i = len;\n      d = yd;\n      yd = xd;\n      xd = d;\n    }\n\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n    for (carry = 0; i;) {\n      carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n      xd[i] %= BASE;\n    }\n    if (carry) {\n      xd.unshift(carry);\n      ++e;\n    }\n\n    // Remove trailing zeros.\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n    for (len = xd.length; xd[--len] == 0;) xd.pop();\n    y.d = xd;\n    y.e = e;\n    return external ? round(y, pr) : y;\n  }\n  function checkInt32(i, min, max) {\n    if (i !== ~~i || i < min || i > max) {\n      throw Error(invalidArgument + i);\n    }\n  }\n  function digitsToString(d) {\n    var i,\n      k,\n      ws,\n      indexOfLastWord = d.length - 1,\n      str = '',\n      w = d[0];\n    if (indexOfLastWord > 0) {\n      str += w;\n      for (i = 1; i < indexOfLastWord; i++) {\n        ws = d[i] + '';\n        k = LOG_BASE - ws.length;\n        if (k) str += getZeroString(k);\n        str += ws;\n      }\n      w = d[i];\n      ws = w + '';\n      k = LOG_BASE - ws.length;\n      if (k) str += getZeroString(k);\n    } else if (w === 0) {\n      return '0';\n    }\n\n    // Remove trailing zeros of last w.\n    for (; w % 10 === 0;) w /= 10;\n    return str + w;\n  }\n  var divide = function () {\n    // Assumes non-zero x and k, and hence non-zero result.\n    function multiplyInteger(x, k) {\n      var temp,\n        carry = 0,\n        i = x.length;\n      for (x = x.slice(); i--;) {\n        temp = x[i] * k + carry;\n        x[i] = temp % BASE | 0;\n        carry = temp / BASE | 0;\n      }\n      if (carry) x.unshift(carry);\n      return x;\n    }\n    function compare(a, b, aL, bL) {\n      var i, r;\n      if (aL != bL) {\n        r = aL > bL ? 1 : -1;\n      } else {\n        for (i = r = 0; i < aL; i++) {\n          if (a[i] != b[i]) {\n            r = a[i] > b[i] ? 1 : -1;\n            break;\n          }\n        }\n      }\n      return r;\n    }\n    function subtract(a, b, aL) {\n      var i = 0;\n\n      // Subtract b from a.\n      for (; aL--;) {\n        a[aL] -= i;\n        i = a[aL] < b[aL] ? 1 : 0;\n        a[aL] = i * BASE + a[aL] - b[aL];\n      }\n\n      // Remove leading zeros.\n      for (; !a[0] && a.length > 1;) a.shift();\n    }\n    return function (x, y, pr, dp) {\n      var cmp,\n        e,\n        i,\n        k,\n        prod,\n        prodL,\n        q,\n        qd,\n        rem,\n        remL,\n        rem0,\n        sd,\n        t,\n        xi,\n        xL,\n        yd0,\n        yL,\n        yz,\n        Ctor = x.constructor,\n        sign = x.s == y.s ? 1 : -1,\n        xd = x.d,\n        yd = y.d;\n\n      // Either 0?\n      if (!x.s) return new Ctor(x);\n      if (!y.s) throw Error(decimalError + 'Division by zero');\n      e = x.e - y.e;\n      yL = yd.length;\n      xL = xd.length;\n      q = new Ctor(sign);\n      qd = q.d = [];\n\n      // Result exponent may be one less than e.\n      for (i = 0; yd[i] == (xd[i] || 0);) ++i;\n      if (yd[i] > (xd[i] || 0)) --e;\n      if (pr == null) {\n        sd = pr = Ctor.precision;\n      } else if (dp) {\n        sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\n      } else {\n        sd = pr;\n      }\n      if (sd < 0) return new Ctor(0);\n\n      // Convert precision in number of base 10 digits to base 1e7 digits.\n      sd = sd / LOG_BASE + 2 | 0;\n      i = 0;\n\n      // divisor < 1e7\n      if (yL == 1) {\n        k = 0;\n        yd = yd[0];\n        sd++;\n\n        // k is the carry.\n        for (; (i < xL || k) && sd--; i++) {\n          t = k * BASE + (xd[i] || 0);\n          qd[i] = t / yd | 0;\n          k = t % yd | 0;\n        }\n\n        // divisor >= 1e7\n      } else {\n        // Normalise xd and yd so highest order digit of yd is >= BASE/2\n        k = BASE / (yd[0] + 1) | 0;\n        if (k > 1) {\n          yd = multiplyInteger(yd, k);\n          xd = multiplyInteger(xd, k);\n          yL = yd.length;\n          xL = xd.length;\n        }\n        xi = yL;\n        rem = xd.slice(0, yL);\n        remL = rem.length;\n\n        // Add zeros to make remainder as long as divisor.\n        for (; remL < yL;) rem[remL++] = 0;\n        yz = yd.slice();\n        yz.unshift(0);\n        yd0 = yd[0];\n        if (yd[1] >= BASE / 2) ++yd0;\n        do {\n          k = 0;\n\n          // Compare divisor and remainder.\n          cmp = compare(yd, rem, yL, remL);\n\n          // If divisor < remainder.\n          if (cmp < 0) {\n            // Calculate trial digit, k.\n            rem0 = rem[0];\n            if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\n\n            // k will be how many times the divisor goes into the current remainder.\n            k = rem0 / yd0 | 0;\n\n            //  Algorithm:\n            //  1. product = divisor * trial digit (k)\n            //  2. if product > remainder: product -= divisor, k--\n            //  3. remainder -= product\n            //  4. if product was < remainder at 2:\n            //    5. compare new remainder and divisor\n            //    6. If remainder > divisor: remainder -= divisor, k++\n\n            if (k > 1) {\n              if (k >= BASE) k = BASE - 1;\n\n              // product = divisor * trial digit.\n              prod = multiplyInteger(yd, k);\n              prodL = prod.length;\n              remL = rem.length;\n\n              // Compare product and remainder.\n              cmp = compare(prod, rem, prodL, remL);\n\n              // product > remainder.\n              if (cmp == 1) {\n                k--;\n\n                // Subtract divisor from product.\n                subtract(prod, yL < prodL ? yz : yd, prodL);\n              }\n            } else {\n              // cmp is -1.\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n              if (k == 0) cmp = k = 1;\n              prod = yd.slice();\n            }\n            prodL = prod.length;\n            if (prodL < remL) prod.unshift(0);\n\n            // Subtract product from remainder.\n            subtract(rem, prod, remL);\n\n            // If product was < previous remainder.\n            if (cmp == -1) {\n              remL = rem.length;\n\n              // Compare divisor and new remainder.\n              cmp = compare(yd, rem, yL, remL);\n\n              // If divisor < new remainder, subtract divisor from remainder.\n              if (cmp < 1) {\n                k++;\n\n                // Subtract divisor from remainder.\n                subtract(rem, yL < remL ? yz : yd, remL);\n              }\n            }\n            remL = rem.length;\n          } else if (cmp === 0) {\n            k++;\n            rem = [0];\n          } // if cmp === 1, k will be 0\n\n          // Add the next digit, k, to the result array.\n          qd[i++] = k;\n\n          // Update the remainder.\n          if (cmp && rem[0]) {\n            rem[remL++] = xd[xi] || 0;\n          } else {\n            rem = [xd[xi]];\n            remL = 1;\n          }\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\n      }\n\n      // Leading zero?\n      if (!qd[0]) qd.shift();\n      q.e = e;\n      return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\n    };\n  }();\n\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n   * significant digits.\r\n   *\r\n   * Taylor/Maclaurin series.\r\n   *\r\n   * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n   *\r\n   * Argument reduction:\r\n   *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n   *   exp(x) = exp(x / 2^k)^(2^k)\r\n   *\r\n   * Previously, the argument was initially reduced by\r\n   * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n   * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n   * found to be slower than just dividing repeatedly by 32 as above.\r\n   *\r\n   * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n   *\r\n   *  exp(x) is non-terminating for any finite, non-zero x.\r\n   *\r\n   */\n  function exp(x, sd) {\n    var denominator,\n      guard,\n      pow,\n      sum,\n      t,\n      wpr,\n      i = 0,\n      k = 0,\n      Ctor = x.constructor,\n      pr = Ctor.precision;\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\n\n    // exp(0) = 1\n    if (!x.s) return new Ctor(ONE);\n    if (sd == null) {\n      external = false;\n      wpr = pr;\n    } else {\n      wpr = sd;\n    }\n    t = new Ctor(0.03125);\n    while (x.abs().gte(0.1)) {\n      x = x.times(t); // x = x / 2^5\n      k += 5;\n    }\n\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n    wpr += guard;\n    denominator = pow = sum = new Ctor(ONE);\n    Ctor.precision = wpr;\n    for (;;) {\n      pow = round(pow.times(x), wpr);\n      denominator = denominator.times(++i);\n      t = sum.plus(divide(pow, denominator, wpr));\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n        while (k--) sum = round(sum.times(sum), wpr);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(sum, pr)) : sum;\n      }\n      sum = t;\n    }\n  }\n\n  // Calculate the base 10 exponent from the base 1e7 exponent.\n  function getBase10Exponent(x) {\n    var e = x.e * LOG_BASE,\n      w = x.d[0];\n\n    // Add the number of digits of the first word of the digits array.\n    for (; w >= 10; w /= 10) e++;\n    return e;\n  }\n  function getLn10(Ctor, sd, pr) {\n    if (sd > Ctor.LN10.sd()) {\n      // Reset global state in case the exception is caught.\n      external = true;\n      if (pr) Ctor.precision = pr;\n      throw Error(decimalError + 'LN10 precision limit exceeded');\n    }\n    return round(new Ctor(Ctor.LN10), sd);\n  }\n  function getZeroString(k) {\n    var zs = '';\n    for (; k--;) zs += '0';\n    return zs;\n  }\n\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n   * digits.\r\n   *\r\n   *  ln(n) is non-terminating (n != 1)\r\n   *\r\n   */\n  function ln(y, sd) {\n    var c,\n      c0,\n      denominator,\n      e,\n      numerator,\n      sum,\n      t,\n      wpr,\n      x2,\n      n = 1,\n      guard = 10,\n      x = y,\n      xd = x.d,\n      Ctor = x.constructor,\n      pr = Ctor.precision;\n\n    // ln(-x) = NaN\n    // ln(0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\n\n    // ln(1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    if (sd == null) {\n      external = false;\n      wpr = pr;\n    } else {\n      wpr = sd;\n    }\n    if (x.eq(10)) {\n      if (sd == null) external = true;\n      return getLn10(Ctor, wpr);\n    }\n    wpr += guard;\n    Ctor.precision = wpr;\n    c = digitsToString(xd);\n    c0 = c.charAt(0);\n    e = getBase10Exponent(x);\n    if (Math.abs(e) < 1.5e15) {\n      // Argument reduction.\n      // The series converges faster the closer the argument is to 1, so using\n      // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n      // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n      // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n      // later be divided by this number, then separate out the power of 10 using\n      // ln(a*10^b) = ln(a) + b*ln(10).\n\n      // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n      //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n      // max n is 6 (gives 0.7 - 1.3)\n      while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\n        x = x.times(y);\n        c = digitsToString(x.d);\n        c0 = c.charAt(0);\n        n++;\n      }\n      e = getBase10Exponent(x);\n      if (c0 > 1) {\n        x = new Ctor('0.' + c);\n        e++;\n      } else {\n        x = new Ctor(c0 + '.' + c.slice(1));\n      }\n    } else {\n      // The argument reduction method above may result in overflow if the argument y is a massive\n      // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n      // function using ln(x*10^e) = ln(x) + e*ln(10).\n      t = getLn10(Ctor, wpr + 2, pr).times(e + '');\n      x = ln(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\n      Ctor.precision = pr;\n      return sd == null ? (external = true, round(x, pr)) : x;\n    }\n\n    // x is reduced to a value near 1.\n\n    // Taylor series.\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\n    x2 = round(x.times(x), wpr);\n    denominator = 3;\n    for (;;) {\n      numerator = round(numerator.times(x2), wpr);\n      t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n        sum = sum.times(2);\n\n        // Reverse the argument reduction.\n        if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\n        sum = divide(sum, new Ctor(n), wpr);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(sum, pr)) : sum;\n      }\n      sum = t;\n      denominator += 2;\n    }\n  }\n\n  /*\r\n   * Parse the value of a new Decimal `x` from string `str`.\r\n   */\n  function parseDecimal(x, str) {\n    var e, i, len;\n\n    // Decimal point?\n    if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\n\n    // Exponential form?\n    if ((i = str.search(/e/i)) > 0) {\n      // Determine exponent.\n      if (e < 0) e = i;\n      e += +str.slice(i + 1);\n      str = str.substring(0, i);\n    } else if (e < 0) {\n      // Integer.\n      e = str.length;\n    }\n\n    // Determine leading zeros.\n    for (i = 0; str.charCodeAt(i) === 48;) ++i;\n\n    // Determine trailing zeros.\n    for (len = str.length; str.charCodeAt(len - 1) === 48;) --len;\n    str = str.slice(i, len);\n    if (str) {\n      len -= i;\n      e = e - i - 1;\n      x.e = mathfloor(e / LOG_BASE);\n      x.d = [];\n\n      // Transform base\n\n      // e is the base 10 exponent.\n      // i is where to slice str to get the first word of the digits array.\n      i = (e + 1) % LOG_BASE;\n      if (e < 0) i += LOG_BASE;\n      if (i < len) {\n        if (i) x.d.push(+str.slice(0, i));\n        for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\n        str = str.slice(i);\n        i = LOG_BASE - str.length;\n      } else {\n        i -= len;\n      }\n      for (; i--;) str += '0';\n      x.d.push(+str);\n      if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\n    } else {\n      // Zero.\n      x.s = 0;\n      x.e = 0;\n      x.d = [0];\n    }\n    return x;\n  }\n\n  /*\r\n   * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n   */\n  function round(x, sd, rm) {\n    var i,\n      j,\n      k,\n      n,\n      rd,\n      doRound,\n      w,\n      xdi,\n      xd = x.d;\n\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // n: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n\n    // Get the length of the first word of the digits array xd.\n    for (n = 1, k = xd[0]; k >= 10; k /= 10) n++;\n    i = sd - n;\n\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n      i += LOG_BASE;\n      j = sd;\n      w = xd[xdi = 0];\n    } else {\n      xdi = Math.ceil((i + 1) / LOG_BASE);\n      k = xd.length;\n      if (xdi >= k) return x;\n      w = k = xd[xdi];\n\n      // Get the number of digits of w.\n      for (n = 1; k >= 10; k /= 10) n++;\n\n      // Get the index of rd within w.\n      i %= LOG_BASE;\n\n      // Get the index of rd within w, adjusted for leading zeros.\n      // The number of leading zeros of w is given by LOG_BASE - n.\n      j = i - LOG_BASE + n;\n    }\n    if (rm !== void 0) {\n      k = mathpow(10, n - j - 1);\n\n      // Get the rounding digit at index j of w.\n      rd = w / k % 10 | 0;\n\n      // Are there any non-zero digits after the rounding digit?\n      doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\n\n      // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\n      // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\n      // 714.\n\n      doRound = rm < 4 ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 &&\n      // Check whether the digit to the left of the rounding digit is odd.\n      (i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    }\n    if (sd < 1 || !xd[0]) {\n      if (doRound) {\n        k = getBase10Exponent(x);\n        xd.length = 1;\n\n        // Convert sd to decimal places.\n        sd = sd - k - 1;\n\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n        x.e = mathfloor(-sd / LOG_BASE) || 0;\n      } else {\n        xd.length = 1;\n\n        // Zero.\n        xd[0] = x.e = x.s = 0;\n      }\n      return x;\n    }\n\n    // Remove excess digits.\n    if (i == 0) {\n      xd.length = xdi;\n      k = 1;\n      xdi--;\n    } else {\n      xd.length = xdi + 1;\n      k = mathpow(10, LOG_BASE - i);\n\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n      // j > 0 means i > number of leading zeros of w.\n      xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (doRound) {\n      for (;;) {\n        // Is the digit to be rounded up in the first word of xd?\n        if (xdi == 0) {\n          if ((xd[0] += k) == BASE) {\n            xd[0] = 1;\n            ++x.e;\n          }\n          break;\n        } else {\n          xd[xdi] += k;\n          if (xd[xdi] != BASE) break;\n          xd[xdi--] = 0;\n          k = 1;\n        }\n      }\n    }\n\n    // Remove trailing zeros.\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\n      throw Error(exponentOutOfRange + getBase10Exponent(x));\n    }\n    return x;\n  }\n  function subtract(x, y) {\n    var d,\n      e,\n      i,\n      j,\n      k,\n      len,\n      xd,\n      xe,\n      xLTy,\n      yd,\n      Ctor = x.constructor,\n      pr = Ctor.precision;\n\n    // Return y negated if x is zero.\n    // Return x if y is zero and x is non-zero.\n    if (!x.s || !y.s) {\n      if (y.s) y.s = -y.s;else y = new Ctor(x);\n      return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n\n    // x and y are non-zero numbers with the same sign.\n\n    e = y.e;\n    xe = x.e;\n    xd = xd.slice();\n    k = xe - e;\n\n    // If exponents differ...\n    if (k) {\n      xLTy = k < 0;\n      if (xLTy) {\n        d = xd;\n        k = -k;\n        len = yd.length;\n      } else {\n        d = yd;\n        e = xe;\n        len = xd.length;\n      }\n\n      // Numbers with massively different exponents would result in a very high number of zeros\n      // needing to be prepended, but this can be avoided while still ensuring correct rounding by\n      // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n      i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n      if (k > i) {\n        k = i;\n        d.length = 1;\n      }\n\n      // Prepend zeros to equalise exponents.\n      d.reverse();\n      for (i = k; i--;) d.push(0);\n      d.reverse();\n\n      // Base 1e7 exponents equal.\n    } else {\n      // Check digits to determine which is the bigger number.\n\n      i = xd.length;\n      len = yd.length;\n      xLTy = i < len;\n      if (xLTy) len = i;\n      for (i = 0; i < len; i++) {\n        if (xd[i] != yd[i]) {\n          xLTy = xd[i] < yd[i];\n          break;\n        }\n      }\n      k = 0;\n    }\n    if (xLTy) {\n      d = xd;\n      xd = yd;\n      yd = d;\n      y.s = -y.s;\n    }\n    len = xd.length;\n\n    // Append zeros to xd if shorter.\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\n    for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\n\n    // Subtract yd from xd.\n    for (i = yd.length; i > k;) {\n      if (xd[--i] < yd[i]) {\n        for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\n        --xd[j];\n        xd[i] += BASE;\n      }\n      xd[i] -= yd[i];\n    }\n\n    // Remove trailing zeros.\n    for (; xd[--len] === 0;) xd.pop();\n\n    // Remove leading zeros and adjust exponent accordingly.\n    for (; xd[0] === 0; xd.shift()) --e;\n\n    // Zero?\n    if (!xd[0]) return new Ctor(0);\n    y.d = xd;\n    y.e = e;\n\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\n    return external ? round(y, pr) : y;\n  }\n  function toString(x, isExp, sd) {\n    var k,\n      e = getBase10Exponent(x),\n      str = digitsToString(x.d),\n      len = str.length;\n    if (isExp) {\n      if (sd && (k = sd - len) > 0) {\n        str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\n      } else if (len > 1) {\n        str = str.charAt(0) + '.' + str.slice(1);\n      }\n      str = str + (e < 0 ? 'e' : 'e+') + e;\n    } else if (e < 0) {\n      str = '0.' + getZeroString(-e - 1) + str;\n      if (sd && (k = sd - len) > 0) str += getZeroString(k);\n    } else if (e >= len) {\n      str += getZeroString(e + 1 - len);\n      if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\n    } else {\n      if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\n      if (sd && (k = sd - len) > 0) {\n        if (e + 1 === len) str += '.';\n        str += getZeroString(k);\n      }\n    }\n    return x.s < 0 ? '-' + str : str;\n  }\n\n  // Does not strip trailing zeros.\n  function truncate(arr, len) {\n    if (arr.length > len) {\n      arr.length = len;\n      return true;\n    }\n  }\n\n  // Decimal methods\n\n  /*\r\n   *  clone\r\n   *  config/set\r\n   */\n\n  /*\r\n   * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n   * constructor.\r\n   *\r\n   */\n  function clone(obj) {\n    var i, p, ps;\n\n    /*\r\n     * The Decimal constructor and exported function.\r\n     * Return a new Decimal instance.\r\n     *\r\n     * value {number|string|Decimal} A numeric value.\r\n     *\r\n     */\n    function Decimal(value) {\n      var x = this;\n\n      // Decimal called without new.\n      if (!(x instanceof Decimal)) return new Decimal(value);\n\n      // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n      // which points to Object.\n      x.constructor = Decimal;\n\n      // Duplicate.\n      if (value instanceof Decimal) {\n        x.s = value.s;\n        x.e = value.e;\n        x.d = (value = value.d) ? value.slice() : value;\n        return;\n      }\n      if (typeof value === 'number') {\n        // Reject Infinity/NaN.\n        if (value * 0 !== 0) {\n          throw Error(invalidArgument + value);\n        }\n        if (value > 0) {\n          x.s = 1;\n        } else if (value < 0) {\n          value = -value;\n          x.s = -1;\n        } else {\n          x.s = 0;\n          x.e = 0;\n          x.d = [0];\n          return;\n        }\n\n        // Fast path for small integers.\n        if (value === ~~value && value < 1e7) {\n          x.e = 0;\n          x.d = [value];\n          return;\n        }\n        return parseDecimal(x, value.toString());\n      } else if (typeof value !== 'string') {\n        throw Error(invalidArgument + value);\n      }\n\n      // Minus sign?\n      if (value.charCodeAt(0) === 45) {\n        value = value.slice(1);\n        x.s = -1;\n      } else {\n        x.s = 1;\n      }\n      if (isDecimal.test(value)) parseDecimal(x, value);else throw Error(invalidArgument + value);\n    }\n    Decimal.prototype = P;\n    Decimal.ROUND_UP = 0;\n    Decimal.ROUND_DOWN = 1;\n    Decimal.ROUND_CEIL = 2;\n    Decimal.ROUND_FLOOR = 3;\n    Decimal.ROUND_HALF_UP = 4;\n    Decimal.ROUND_HALF_DOWN = 5;\n    Decimal.ROUND_HALF_EVEN = 6;\n    Decimal.ROUND_HALF_CEIL = 7;\n    Decimal.ROUND_HALF_FLOOR = 8;\n    Decimal.clone = clone;\n    Decimal.config = Decimal.set = config;\n    if (obj === void 0) obj = {};\n    if (obj) {\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'LN10'];\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n    Decimal.config(obj);\n    return Decimal;\n  }\n\n  /*\r\n   * Configure global settings for a Decimal constructor.\r\n   *\r\n   * `obj` is an object with one or more of the following properties,\r\n   *\r\n   *   precision  {number}\r\n   *   rounding   {number}\r\n   *   toExpNeg   {number}\r\n   *   toExpPos   {number}\r\n   *\r\n   * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n   *\r\n   */\n  function config(obj) {\n    if (!obj || typeof obj !== 'object') {\n      throw Error(decimalError + 'Object expected');\n    }\n    var i,\n      p,\n      v,\n      ps = ['precision', 1, MAX_DIGITS, 'rounding', 0, 8, 'toExpNeg', -1 / 0, 0, 'toExpPos', 0, 1 / 0];\n    for (i = 0; i < ps.length; i += 3) {\n      if ((v = obj[p = ps[i]]) !== void 0) {\n        if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;else throw Error(invalidArgument + p + ': ' + v);\n      }\n    }\n    if ((v = obj[p = 'LN10']) !== void 0) {\n      if (v == Math.LN10) this[p] = new this(v);else throw Error(invalidArgument + p + ': ' + v);\n    }\n    return this;\n  }\n\n  // Create and configure initial Decimal constructor.\n  Decimal = clone(Decimal);\n  Decimal['default'] = Decimal.Decimal = Decimal;\n\n  // Internal constant.\n  ONE = new Decimal(1);\n\n  // Export.\n\n  // AMD.\n  if (typeof define == 'function' && define.amd) {\n    define(function () {\n      return Decimal;\n    });\n\n    // Node and other environments that support module.exports.\n  } else if (typeof module != 'undefined' && module.exports) {\n    module.exports = Decimal;\n\n    // Browser.\n  } else {\n    if (!globalScope) {\n      globalScope = typeof self != 'undefined' && self && self.self == self ? self : Function('return this')();\n    }\n    globalScope.Decimal = Decimal;\n  }\n})(this);", "map": {"version": 3, "names": ["globalScope", "MAX_DIGITS", "Decimal", "precision", "rounding", "toExpNeg", "toExpPos", "LN10", "external", "decimalError", "invalidArgument", "exponentOutOfRange", "mathfloor", "Math", "floor", "mathpow", "pow", "isDecimal", "ONE", "BASE", "LOG_BASE", "MAX_SAFE_INTEGER", "MAX_E", "P", "absoluteValue", "abs", "x", "constructor", "s", "comparedTo", "cmp", "y", "i", "j", "xdL", "ydL", "e", "d", "length", "decimalPlaces", "dp", "w", "dividedBy", "div", "divide", "dividedToIntegerBy", "idiv", "Ctor", "round", "equals", "eq", "exponent", "getBase10Exponent", "greaterThan", "gt", "greaterThanOrEqualTo", "gte", "isInteger", "isint", "isNegative", "isneg", "isPositive", "ispos", "isZero", "lessThan", "lt", "lessThanOrEqualTo", "lte", "logarithm", "log", "base", "r", "pr", "wpr", "Error", "ln", "minus", "sub", "subtract", "add", "modulo", "mod", "q", "times", "naturalExponential", "exp", "naturalLogarithm", "negated", "neg", "plus", "sd", "z", "squareRoot", "sqrt", "n", "t", "digitsToString", "toExponential", "slice", "indexOf", "toString", "mul", "carry", "k", "rL", "xd", "yd", "push", "pop", "shift", "toDecimalPlaces", "todp", "rm", "checkInt32", "str", "toFixed", "toInteger", "toint", "toNumber", "<PERSON><PERSON><PERSON><PERSON>", "sign", "yIsInt", "guard", "yn", "ceil", "truncate", "max", "toPrecision", "toSignificantDigits", "tosd", "valueOf", "val", "toJSON", "len", "reverse", "unshift", "min", "ws", "indexOfLastWord", "getZeroString", "multiplyInteger", "temp", "compare", "a", "b", "aL", "bL", "prod", "prodL", "qd", "rem", "remL", "rem0", "xi", "xL", "yd0", "yL", "yz", "denominator", "sum", "getLn10", "zs", "c", "c0", "numerator", "x2", "char<PERSON>t", "parseDecimal", "replace", "search", "substring", "charCodeAt", "rd", "doRound", "xdi", "xe", "xLTy", "isExp", "arr", "clone", "obj", "p", "ps", "value", "test", "prototype", "ROUND_UP", "ROUND_DOWN", "ROUND_CEIL", "ROUND_FLOOR", "ROUND_HALF_UP", "ROUND_HALF_DOWN", "ROUND_HALF_EVEN", "ROUND_HALF_CEIL", "ROUND_HALF_FLOOR", "config", "set", "hasOwnProperty", "v", "define", "amd", "module", "exports", "self", "Function"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/decimal.js-light/decimal.js"], "sourcesContent": ["/*! decimal.js-light v2.5.1 https://github.com/MikeMcl/decimal.js-light/LICENCE */\r\n;(function (globalScope) {\r\n  'use strict';\r\n\r\n\r\n  /*\r\n   *  decimal.js-light v2.5.1\r\n   *  An arbitrary-precision Decimal type for JavaScript.\r\n   *  https://github.com/MikeMcl/decimal.js-light\r\n   *  Copyright (c) 2020 <PERSON> <<EMAIL>>\r\n   *  MIT Expat Licence\r\n   */\r\n\r\n\r\n  // -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n    // The limit on the value of `precision`, and on the value of the first argument to\r\n    // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  var MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n\r\n    // The initial configuration properties of the Decimal constructor.\r\n    Decimal = {\r\n\r\n      // These values must be integers within the stated ranges (inclusive).\r\n      // Most of these values can be changed during run-time using `Decimal.config`.\r\n\r\n      // The maximum number of significant digits of the result of a calculation or base conversion.\r\n      // E.g. `Decimal.config({ precision: 20 });`\r\n      precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n      // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\r\n      // `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n      //\r\n      // ROUND_UP         0 Away from zero.\r\n      // ROUND_DOWN       1 Towards zero.\r\n      // ROUND_CEIL       2 Towards +Infinity.\r\n      // ROUND_FLOOR      3 Towards -Infinity.\r\n      // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n      // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n      // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n      // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n      // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n      //\r\n      // E.g.\r\n      // `Decimal.rounding = 4;`\r\n      // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n      rounding: 4,                           // 0 to 8\r\n\r\n      // The exponent value at and beneath which `toString` returns exponential notation.\r\n      // JavaScript numbers: -7\r\n      toExpNeg: -7,                          // 0 to -MAX_E\r\n\r\n      // The exponent value at and above which `toString` returns exponential notation.\r\n      // JavaScript numbers: 21\r\n      toExpPos:  21,                         // 0 to MAX_E\r\n\r\n      // The natural logarithm of 10.\r\n      // 115 digits\r\n      LN10: '2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286'\r\n    },\r\n\r\n\r\n  // ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n    external = true,\r\n\r\n    decimalError = '[DecimalError] ',\r\n    invalidArgument = decimalError + 'Invalid argument: ',\r\n    exponentOutOfRange = decimalError + 'Exponent out of range: ',\r\n\r\n    mathfloor = Math.floor,\r\n    mathpow = Math.pow,\r\n\r\n    isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n    ONE,\r\n    BASE = 1e7,\r\n    LOG_BASE = 7,\r\n    MAX_SAFE_INTEGER = 9007199254740991,\r\n    MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE),    // 1286742750677284\r\n\r\n    // Decimal.prototype object\r\n    P = {};\r\n\r\n\r\n  // Decimal prototype methods\r\n\r\n\r\n  /*\r\n   *  absoluteValue                       abs\r\n   *  comparedTo                          cmp\r\n   *  decimalPlaces                       dp\r\n   *  dividedBy                           div\r\n   *  dividedToIntegerBy                  idiv\r\n   *  equals                              eq\r\n   *  exponent\r\n   *  greaterThan                         gt\r\n   *  greaterThanOrEqualTo                gte\r\n   *  isInteger                           isint\r\n   *  isNegative                          isneg\r\n   *  isPositive                          ispos\r\n   *  isZero\r\n   *  lessThan                            lt\r\n   *  lessThanOrEqualTo                   lte\r\n   *  logarithm                           log\r\n   *  minus                               sub\r\n   *  modulo                              mod\r\n   *  naturalExponential                  exp\r\n   *  naturalLogarithm                    ln\r\n   *  negated                             neg\r\n   *  plus                                add\r\n   *  precision                           sd\r\n   *  squareRoot                          sqrt\r\n   *  times                               mul\r\n   *  toDecimalPlaces                     todp\r\n   *  toExponential\r\n   *  toFixed\r\n   *  toInteger                           toint\r\n   *  toNumber\r\n   *  toPower                             pow\r\n   *  toPrecision\r\n   *  toSignificantDigits                 tosd\r\n   *  toString\r\n   *  valueOf                             val\r\n   */\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the absolute value of this Decimal.\r\n   *\r\n   */\r\n  P.absoluteValue = P.abs = function () {\r\n    var x = new this.constructor(this);\r\n    if (x.s) x.s = 1;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return\r\n   *   1    if the value of this Decimal is greater than the value of `y`,\r\n   *  -1    if the value of this Decimal is less than the value of `y`,\r\n   *   0    if they have the same value\r\n   *\r\n   */\r\n  P.comparedTo = P.cmp = function (y) {\r\n    var i, j, xdL, ydL,\r\n      x = this;\r\n\r\n    y = new x.constructor(y);\r\n\r\n    // Signs differ?\r\n    if (x.s !== y.s) return x.s || -y.s;\r\n\r\n    // Compare exponents.\r\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\r\n\r\n    xdL = x.d.length;\r\n    ydL = y.d.length;\r\n\r\n    // Compare digit by digit.\r\n    for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n      if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\r\n    }\r\n\r\n    // Compare lengths.\r\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the number of decimal places of the value of this Decimal.\r\n   *\r\n   */\r\n  P.decimalPlaces = P.dp = function () {\r\n    var x = this,\r\n      w = x.d.length - 1,\r\n      dp = (w - x.e) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = x.d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) dp--;\r\n\r\n    return dp < 0 ? 0 : dp;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.dividedBy = P.div = function (y) {\r\n    return divide(this, new this.constructor(y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n   * by the value of `y`, truncated to `precision` significant digits.\r\n   *\r\n   */\r\n  P.dividedToIntegerBy = P.idiv = function (y) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.equals = P.eq = function (y) {\r\n    return !this.cmp(y);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n   *\r\n   */\r\n  P.exponent = function () {\r\n    return getBase10Exponent(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n   * false.\r\n   *\r\n   */\r\n  P.greaterThan = P.gt = function (y) {\r\n    return this.cmp(y) > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n   * otherwise return false.\r\n   *\r\n   */\r\n  P.greaterThanOrEqualTo = P.gte = function (y) {\r\n    return this.cmp(y) >= 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is an integer, otherwise return false.\r\n   *\r\n   */\r\n  P.isInteger = P.isint = function () {\r\n    return this.e > this.d.length - 2;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is negative, otherwise return false.\r\n   *\r\n   */\r\n  P.isNegative = P.isneg = function () {\r\n    return this.s < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is positive, otherwise return false.\r\n   *\r\n   */\r\n  P.isPositive = P.ispos = function () {\r\n    return this.s > 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is 0, otherwise return false.\r\n   *\r\n   */\r\n  P.isZero = function () {\r\n    return this.s === 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.lessThan = P.lt = function (y) {\r\n    return this.cmp(y) < 0;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n   *\r\n   */\r\n  P.lessThanOrEqualTo = P.lte = function (y) {\r\n    return this.cmp(y) < 1;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   * If no base is specified, return log[10](x).\r\n   *\r\n   * log[base](x) = ln(x) / ln(base)\r\n   *\r\n   * The maximum error of the result is 1 ulp (unit in the last place).\r\n   *\r\n   * [base] {number|string|Decimal} The base of the logarithm.\r\n   *\r\n   */\r\n  P.logarithm = P.log = function (base) {\r\n    var r,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision,\r\n      wpr = pr + 5;\r\n\r\n    // Default base is 10.\r\n    if (base === void 0) {\r\n      base = new Ctor(10);\r\n    } else {\r\n      base = new Ctor(base);\r\n\r\n      // log[-b](x) = NaN\r\n      // log[0](x)  = NaN\r\n      // log[1](x)  = NaN\r\n      if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + 'NaN');\r\n    }\r\n\r\n    // log[b](-x) = NaN\r\n    // log[b](0) = -Infinity\r\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n    // log[b](1) = 0\r\n    if (x.eq(ONE)) return new Ctor(0);\r\n\r\n    external = false;\r\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\r\n    external = true;\r\n\r\n    return round(r, pr);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.minus = P.sub = function (y) {\r\n    var x = this;\r\n    y = new x.constructor(y);\r\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.modulo = P.mod = function (y) {\r\n    var q,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    y = new Ctor(y);\r\n\r\n    // x % 0 = NaN\r\n    if (!y.s) throw Error(decimalError + 'NaN');\r\n\r\n    // Return x if x is 0.\r\n    if (!x.s) return round(new Ctor(x), pr);\r\n\r\n    // Prevent rounding of intermediate calculations.\r\n    external = false;\r\n    q = divide(x, y, 0, 1).times(y);\r\n    external = true;\r\n\r\n    return x.minus(q);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n   * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\r\n  P.naturalExponential = P.exp = function () {\r\n    return exp(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   */\r\n  P.naturalLogarithm = P.ln = function () {\r\n    return ln(this);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n   * -1.\r\n   *\r\n   */\r\n  P.negated = P.neg = function () {\r\n    var x = new this.constructor(this);\r\n    x.s = -x.s || 0;\r\n    return x;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.plus = P.add = function (y) {\r\n    var x = this;\r\n    y = new x.constructor(y);\r\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the number of significant digits of the value of this Decimal.\r\n   *\r\n   * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n   *\r\n   */\r\n  P.precision = P.sd = function (z) {\r\n    var e, sd, w,\r\n      x = this;\r\n\r\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n    e = getBase10Exponent(x) + 1;\r\n    w = x.d.length - 1;\r\n    sd = w * LOG_BASE + 1;\r\n    w = x.d[w];\r\n\r\n    // If non-zero...\r\n    if (w) {\r\n\r\n      // Subtract the number of trailing zeros of the last word.\r\n      for (; w % 10 == 0; w /= 10) sd--;\r\n\r\n      // Add the number of digits of the first word.\r\n      for (w = x.d[0]; w >= 10; w /= 10) sd++;\r\n    }\r\n\r\n    return z && e > sd ? e : sd;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n   * significant digits.\r\n   *\r\n   */\r\n  P.squareRoot = P.sqrt = function () {\r\n    var e, n, pr, r, s, t, wpr,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    // Negative or zero?\r\n    if (x.s < 1) {\r\n      if (!x.s) return new Ctor(0);\r\n\r\n      // sqrt(-x) = NaN\r\n      throw Error(decimalError + 'NaN');\r\n    }\r\n\r\n    e = getBase10Exponent(x);\r\n    external = false;\r\n\r\n    // Initial estimate.\r\n    s = Math.sqrt(+x);\r\n\r\n    // Math.sqrt underflow/overflow?\r\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n    if (s == 0 || s == 1 / 0) {\r\n      n = digitsToString(x.d);\r\n      if ((n.length + e) % 2 == 0) n += '0';\r\n      s = Math.sqrt(n);\r\n      e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n      if (s == 1 / 0) {\r\n        n = '5e' + e;\r\n      } else {\r\n        n = s.toExponential();\r\n        n = n.slice(0, n.indexOf('e') + 1) + e;\r\n      }\r\n\r\n      r = new Ctor(n);\r\n    } else {\r\n      r = new Ctor(s.toString());\r\n    }\r\n\r\n    pr = Ctor.precision;\r\n    s = wpr = pr + 3;\r\n\r\n    // Newton-Raphson iteration.\r\n    for (;;) {\r\n      t = r;\r\n      r = t.plus(divide(x, t, wpr + 2)).times(0.5);\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\r\n        n = n.slice(wpr - 3, wpr + 1);\r\n\r\n        // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n        // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n        if (s == wpr && n == '4999') {\r\n\r\n          // On the first iteration only, check to see if rounding up gives the exact result as the\r\n          // nines may infinitely repeat.\r\n          round(t, pr + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        } else if (n != '9999') {\r\n          break;\r\n        }\r\n\r\n        wpr += 4;\r\n      }\r\n    }\r\n\r\n    external = true;\r\n\r\n    return round(r, pr);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n   * `precision` significant digits.\r\n   *\r\n   */\r\n  P.times = P.mul = function (y) {\r\n    var carry, e, i, k, r, rL, t, xdL, ydL,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      xd = x.d,\r\n      yd = (y = new Ctor(y)).d;\r\n\r\n    // Return 0 if either is 0.\r\n    if (!x.s || !y.s) return new Ctor(0);\r\n\r\n    y.s *= x.s;\r\n    e = x.e + y.e;\r\n    xdL = xd.length;\r\n    ydL = yd.length;\r\n\r\n    // Ensure xd points to the longer array.\r\n    if (xdL < ydL) {\r\n      r = xd;\r\n      xd = yd;\r\n      yd = r;\r\n      rL = xdL;\r\n      xdL = ydL;\r\n      ydL = rL;\r\n    }\r\n\r\n    // Initialise the result array with zeros.\r\n    r = [];\r\n    rL = xdL + ydL;\r\n    for (i = rL; i--;) r.push(0);\r\n\r\n    // Multiply!\r\n    for (i = ydL; --i >= 0;) {\r\n      carry = 0;\r\n      for (k = xdL + i; k > i;) {\r\n        t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n        r[k--] = t % BASE | 0;\r\n        carry = t / BASE | 0;\r\n      }\r\n\r\n      r[k] = (r[k] + carry) % BASE | 0;\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (; !r[--rL];) r.pop();\r\n\r\n    if (carry) ++e;\r\n    else r.shift();\r\n\r\n    y.d = r;\r\n    y.e = e;\r\n\r\n    return external ? round(y, Ctor.precision) : y;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n   * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n   *\r\n   * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toDecimalPlaces = P.todp = function (dp, rm) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n\r\n    x = new Ctor(x);\r\n    if (dp === void 0) return x;\r\n\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal in exponential notation rounded to\r\n   * `dp` fixed decimal places using rounding mode `rounding`.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toExponential = function (dp, rm) {\r\n    var str,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (dp === void 0) {\r\n      str = toString(x, true);\r\n    } else {\r\n      checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n\r\n      x = round(new Ctor(x), dp + 1, rm);\r\n      str = toString(x, true, dp + 1);\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n   * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n   * omitted.\r\n   *\r\n   * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n   *\r\n   * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n   * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n   * (-0).toFixed(3) is '0.000'.\r\n   * (-0.5).toFixed(0) is '-0'.\r\n   *\r\n   */\r\n  P.toFixed = function (dp, rm) {\r\n    var str, y,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (dp === void 0) return toString(x);\r\n\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\r\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\r\n\r\n    // To determine whether to add the minus sign look at the value before it was rounded,\r\n    // i.e. look at `x` rather than `y`.\r\n    return x.isneg() && !x.isZero() ? '-' + str : str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n   * rounding mode `rounding`.\r\n   *\r\n   */\r\n  P.toInteger = P.toint = function () {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return the value of this Decimal converted to a number primitive.\r\n   *\r\n   */\r\n  P.toNumber = function () {\r\n    return +this;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n   * truncated to `precision` significant digits.\r\n   *\r\n   * For non-integer or very large exponents pow(x, y) is calculated using\r\n   *\r\n   *   x^y = exp(y*ln(x))\r\n   *\r\n   * The maximum error is 1 ulp (unit in last place).\r\n   *\r\n   * y {number|string|Decimal} The power to which to raise this Decimal.\r\n   *\r\n   */\r\n  P.toPower = P.pow = function (y) {\r\n    var e, k, pr, r, sign, yIsInt,\r\n      x = this,\r\n      Ctor = x.constructor,\r\n      guard = 12,\r\n      yn = +(y = new Ctor(y));\r\n\r\n    // pow(x, 0) = 1\r\n    if (!y.s) return new Ctor(ONE);\r\n\r\n    x = new Ctor(x);\r\n\r\n    // pow(0, y > 0) = 0\r\n    // pow(0, y < 0) = Infinity\r\n    if (!x.s) {\r\n      if (y.s < 1) throw Error(decimalError + 'Infinity');\r\n      return x;\r\n    }\r\n\r\n    // pow(1, y) = 1\r\n    if (x.eq(ONE)) return x;\r\n\r\n    pr = Ctor.precision;\r\n\r\n    // pow(x, 1) = x\r\n    if (y.eq(ONE)) return round(x, pr);\r\n\r\n    e = y.e;\r\n    k = y.d.length - 1;\r\n    yIsInt = e >= k;\r\n    sign = x.s;\r\n\r\n    if (!yIsInt) {\r\n\r\n      // pow(x < 0, y non-integer) = NaN\r\n      if (sign < 0) throw Error(decimalError + 'NaN');\r\n\r\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n      r = new Ctor(ONE);\r\n\r\n      // Max k of 9007199254740991 takes 53 loop iterations.\r\n      // Maximum digits array length; leaves [28, 34] guard digits.\r\n      e = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n      external = false;\r\n\r\n      for (;;) {\r\n        if (k % 2) {\r\n          r = r.times(x);\r\n          truncate(r.d, e);\r\n        }\r\n\r\n        k = mathfloor(k / 2);\r\n        if (k === 0) break;\r\n\r\n        x = x.times(x);\r\n        truncate(x.d, e);\r\n      }\r\n\r\n      external = true;\r\n\r\n      return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\r\n    }\r\n\r\n    // Result is negative if x is negative and the last digit of integer y is odd.\r\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\r\n\r\n    x.s = 1;\r\n    external = false;\r\n    r = y.times(ln(x, pr + guard));\r\n    external = true;\r\n    r = exp(r);\r\n    r.s = sign;\r\n\r\n    return r;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n   * using rounding mode `rounding`.\r\n   *\r\n   * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n   * the integer part of the value in normal notation.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toPrecision = function (sd, rm) {\r\n    var e, str,\r\n      x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (sd === void 0) {\r\n      e = getBase10Exponent(x);\r\n      str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n    } else {\r\n      checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n\r\n      x = round(new Ctor(x), sd, rm);\r\n      e = getBase10Exponent(x);\r\n      str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\r\n    }\r\n\r\n    return str;\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n   * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n   * omitted.\r\n   *\r\n   * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n   * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n   *\r\n   */\r\n  P.toSignificantDigits = P.tosd = function (sd, rm) {\r\n    var x = this,\r\n      Ctor = x.constructor;\r\n\r\n    if (sd === void 0) {\r\n      sd = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n      if (rm === void 0) rm = Ctor.rounding;\r\n      else checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    return round(new Ctor(x), sd, rm);\r\n  };\r\n\r\n\r\n  /*\r\n   * Return a string representing the value of this Decimal.\r\n   *\r\n   * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n   * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n   *\r\n   */\r\n  P.toString = P.valueOf = P.val = P.toJSON = function () {\r\n    var x = this,\r\n      e = getBase10Exponent(x),\r\n      Ctor = x.constructor;\r\n\r\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\r\n  };\r\n\r\n\r\n  // Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n  /*\r\n   *  add                 P.minus, P.plus\r\n   *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n   *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n   *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n   *  exp                 P.exp, P.pow\r\n   *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n   *                      P.toString, divide, round, toString, exp, ln\r\n   *  getLn10             P.log, ln\r\n   *  getZeroString       digitsToString, toString\r\n   *  ln                  P.log, P.ln, P.pow, exp\r\n   *  parseDecimal        Decimal\r\n   *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n   *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n   *                      divide, getLn10, exp, ln\r\n   *  subtract            P.minus, P.plus\r\n   *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n   *  truncate            P.pow\r\n   *\r\n   *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n   *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n   */\r\n\r\n\r\n  function add(x, y) {\r\n    var carry, d, e, i, k, len, xd, yd,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // If either is zero...\r\n    if (!x.s || !y.s) {\r\n\r\n      // Return x if y is zero.\r\n      // Return y if y is non-zero.\r\n      if (!y.s) y = new Ctor(x);\r\n      return external ? round(y, pr) : y;\r\n    }\r\n\r\n    xd = x.d;\r\n    yd = y.d;\r\n\r\n    // x and y are finite, non-zero numbers with the same sign.\r\n\r\n    k = x.e;\r\n    e = y.e;\r\n    xd = xd.slice();\r\n    i = k - e;\r\n\r\n    // If base 1e7 exponents differ...\r\n    if (i) {\r\n      if (i < 0) {\r\n        d = xd;\r\n        i = -i;\r\n        len = yd.length;\r\n      } else {\r\n        d = yd;\r\n        e = k;\r\n        len = xd.length;\r\n      }\r\n\r\n      // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n      k = Math.ceil(pr / LOG_BASE);\r\n      len = k > len ? k + 1 : len + 1;\r\n\r\n      if (i > len) {\r\n        i = len;\r\n        d.length = 1;\r\n      }\r\n\r\n      // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n      d.reverse();\r\n      for (; i--;) d.push(0);\r\n      d.reverse();\r\n    }\r\n\r\n    len = xd.length;\r\n    i = yd.length;\r\n\r\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n    if (len - i < 0) {\r\n      i = len;\r\n      d = yd;\r\n      yd = xd;\r\n      xd = d;\r\n    }\r\n\r\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n    for (carry = 0; i;) {\r\n      carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n      xd[i] %= BASE;\r\n    }\r\n\r\n    if (carry) {\r\n      xd.unshift(carry);\r\n      ++e;\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n    for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n    y.d = xd;\r\n    y.e = e;\r\n\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n\r\n  function checkInt32(i, min, max) {\r\n    if (i !== ~~i || i < min || i > max) {\r\n      throw Error(invalidArgument + i);\r\n    }\r\n  }\r\n\r\n\r\n  function digitsToString(d) {\r\n    var i, k, ws,\r\n      indexOfLastWord = d.length - 1,\r\n      str = '',\r\n      w = d[0];\r\n\r\n    if (indexOfLastWord > 0) {\r\n      str += w;\r\n      for (i = 1; i < indexOfLastWord; i++) {\r\n        ws = d[i] + '';\r\n        k = LOG_BASE - ws.length;\r\n        if (k) str += getZeroString(k);\r\n        str += ws;\r\n      }\r\n\r\n      w = d[i];\r\n      ws = w + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n    } else if (w === 0) {\r\n      return '0';\r\n    }\r\n\r\n    // Remove trailing zeros of last w.\r\n    for (; w % 10 === 0;) w /= 10;\r\n\r\n    return str + w;\r\n  }\r\n\r\n\r\n  var divide = (function () {\r\n\r\n    // Assumes non-zero x and k, and hence non-zero result.\r\n    function multiplyInteger(x, k) {\r\n      var temp,\r\n        carry = 0,\r\n        i = x.length;\r\n\r\n      for (x = x.slice(); i--;) {\r\n        temp = x[i] * k + carry;\r\n        x[i] = temp % BASE | 0;\r\n        carry = temp / BASE | 0;\r\n      }\r\n\r\n      if (carry) x.unshift(carry);\r\n\r\n      return x;\r\n    }\r\n\r\n    function compare(a, b, aL, bL) {\r\n      var i, r;\r\n\r\n      if (aL != bL) {\r\n        r = aL > bL ? 1 : -1;\r\n      } else {\r\n        for (i = r = 0; i < aL; i++) {\r\n          if (a[i] != b[i]) {\r\n            r = a[i] > b[i] ? 1 : -1;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      return r;\r\n    }\r\n\r\n    function subtract(a, b, aL) {\r\n      var i = 0;\r\n\r\n      // Subtract b from a.\r\n      for (; aL--;) {\r\n        a[aL] -= i;\r\n        i = a[aL] < b[aL] ? 1 : 0;\r\n        a[aL] = i * BASE + a[aL] - b[aL];\r\n      }\r\n\r\n      // Remove leading zeros.\r\n      for (; !a[0] && a.length > 1;) a.shift();\r\n    }\r\n\r\n    return function (x, y, pr, dp) {\r\n      var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz,\r\n        Ctor = x.constructor,\r\n        sign = x.s == y.s ? 1 : -1,\r\n        xd = x.d,\r\n        yd = y.d;\r\n\r\n      // Either 0?\r\n      if (!x.s) return new Ctor(x);\r\n      if (!y.s) throw Error(decimalError + 'Division by zero');\r\n\r\n      e = x.e - y.e;\r\n      yL = yd.length;\r\n      xL = xd.length;\r\n      q = new Ctor(sign);\r\n      qd = q.d = [];\r\n\r\n      // Result exponent may be one less than e.\r\n      for (i = 0; yd[i] == (xd[i] || 0); ) ++i;\r\n      if (yd[i] > (xd[i] || 0)) --e;\r\n\r\n      if (pr == null) {\r\n        sd = pr = Ctor.precision;\r\n      } else if (dp) {\r\n        sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\r\n      } else {\r\n        sd = pr;\r\n      }\r\n\r\n      if (sd < 0) return new Ctor(0);\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / LOG_BASE + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * BASE + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= BASE/2\r\n        k = BASE / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k);\r\n          xd = multiplyInteger(xd, k);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= BASE / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= BASE) k = BASE - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n\r\n      q.e = e;\r\n\r\n      return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\r\n    };\r\n  })();\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n   * significant digits.\r\n   *\r\n   * Taylor/Maclaurin series.\r\n   *\r\n   * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n   *\r\n   * Argument reduction:\r\n   *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n   *   exp(x) = exp(x / 2^k)^(2^k)\r\n   *\r\n   * Previously, the argument was initially reduced by\r\n   * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n   * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n   * found to be slower than just dividing repeatedly by 32 as above.\r\n   *\r\n   * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n   *\r\n   *  exp(x) is non-terminating for any finite, non-zero x.\r\n   *\r\n   */\r\n  function exp(x, sd) {\r\n    var denominator, guard, pow, sum, t, wpr,\r\n      i = 0,\r\n      k = 0,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n\r\n    // exp(0) = 1\r\n    if (!x.s) return new Ctor(ONE);\r\n\r\n    if (sd == null) {\r\n      external = false;\r\n      wpr = pr;\r\n    } else {\r\n      wpr = sd;\r\n    }\r\n\r\n    t = new Ctor(0.03125);\r\n\r\n    while (x.abs().gte(0.1)) {\r\n      x = x.times(t);    // x = x / 2^5\r\n      k += 5;\r\n    }\r\n\r\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\r\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n    wpr += guard;\r\n    denominator = pow = sum = new Ctor(ONE);\r\n    Ctor.precision = wpr;\r\n\r\n    for (;;) {\r\n      pow = round(pow.times(x), wpr);\r\n      denominator = denominator.times(++i);\r\n      t = sum.plus(divide(pow, denominator, wpr));\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n        while (k--) sum = round(sum.times(sum), wpr);\r\n        Ctor.precision = pr;\r\n        return sd == null ? (external = true, round(sum, pr)) : sum;\r\n      }\r\n\r\n      sum = t;\r\n    }\r\n  }\r\n\r\n\r\n  // Calculate the base 10 exponent from the base 1e7 exponent.\r\n  function getBase10Exponent(x) {\r\n    var e = x.e * LOG_BASE,\r\n      w = x.d[0];\r\n\r\n    // Add the number of digits of the first word of the digits array.\r\n    for (; w >= 10; w /= 10) e++;\r\n    return e;\r\n  }\r\n\r\n\r\n  function getLn10(Ctor, sd, pr) {\r\n\r\n    if (sd > Ctor.LN10.sd()) {\r\n\r\n\r\n      // Reset global state in case the exception is caught.\r\n      external = true;\r\n      if (pr) Ctor.precision = pr;\r\n      throw Error(decimalError + 'LN10 precision limit exceeded');\r\n    }\r\n\r\n    return round(new Ctor(Ctor.LN10), sd);\r\n  }\r\n\r\n\r\n  function getZeroString(k) {\r\n    var zs = '';\r\n    for (; k--;) zs += '0';\r\n    return zs;\r\n  }\r\n\r\n\r\n  /*\r\n   * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n   * digits.\r\n   *\r\n   *  ln(n) is non-terminating (n != 1)\r\n   *\r\n   */\r\n  function ln(y, sd) {\r\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2,\r\n      n = 1,\r\n      guard = 10,\r\n      x = y,\r\n      xd = x.d,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // ln(-x) = NaN\r\n    // ln(0) = -Infinity\r\n    if (x.s < 1) throw Error(decimalError + (x.s ? 'NaN' : '-Infinity'));\r\n\r\n    // ln(1) = 0\r\n    if (x.eq(ONE)) return new Ctor(0);\r\n\r\n    if (sd == null) {\r\n      external = false;\r\n      wpr = pr;\r\n    } else {\r\n      wpr = sd;\r\n    }\r\n\r\n    if (x.eq(10)) {\r\n      if (sd == null) external = true;\r\n      return getLn10(Ctor, wpr);\r\n    }\r\n\r\n    wpr += guard;\r\n    Ctor.precision = wpr;\r\n    c = digitsToString(xd);\r\n    c0 = c.charAt(0);\r\n    e = getBase10Exponent(x);\r\n\r\n    if (Math.abs(e) < 1.5e15) {\r\n\r\n      // Argument reduction.\r\n      // The series converges faster the closer the argument is to 1, so using\r\n      // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n      // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n      // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n      // later be divided by this number, then separate out the power of 10 using\r\n      // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n      // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n      //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n      // max n is 6 (gives 0.7 - 1.3)\r\n      while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n        x = x.times(y);\r\n        c = digitsToString(x.d);\r\n        c0 = c.charAt(0);\r\n        n++;\r\n      }\r\n\r\n      e = getBase10Exponent(x);\r\n\r\n      if (c0 > 1) {\r\n        x = new Ctor('0.' + c);\r\n        e++;\r\n      } else {\r\n        x = new Ctor(c0 + '.' + c.slice(1));\r\n      }\r\n    } else {\r\n\r\n      // The argument reduction method above may result in overflow if the argument y is a massive\r\n      // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n      // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n      t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n      x = ln(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n\r\n      Ctor.precision = pr;\r\n      return sd == null ? (external = true, round(x, pr)) : x;\r\n    }\r\n\r\n    // x is reduced to a value near 1.\r\n\r\n    // Taylor series.\r\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\r\n    x2 = round(x.times(x), wpr);\r\n    denominator = 3;\r\n\r\n    for (;;) {\r\n      numerator = round(numerator.times(x2), wpr);\r\n      t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\r\n\r\n      if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n        sum = sum.times(2);\r\n\r\n        // Reverse the argument reduction.\r\n        if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n        sum = divide(sum, new Ctor(n), wpr);\r\n\r\n        Ctor.precision = pr;\r\n        return sd == null ? (external = true, round(sum, pr)) : sum;\r\n      }\r\n\r\n      sum = t;\r\n      denominator += 2;\r\n    }\r\n  }\r\n\r\n\r\n  /*\r\n   * Parse the value of a new Decimal `x` from string `str`.\r\n   */\r\n  function parseDecimal(x, str) {\r\n    var e, i, len;\r\n\r\n    // Decimal point?\r\n    if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n    // Exponential form?\r\n    if ((i = str.search(/e/i)) > 0) {\r\n\r\n      // Determine exponent.\r\n      if (e < 0) e = i;\r\n      e += +str.slice(i + 1);\r\n      str = str.substring(0, i);\r\n    } else if (e < 0) {\r\n\r\n      // Integer.\r\n      e = str.length;\r\n    }\r\n\r\n    // Determine leading zeros.\r\n    for (i = 0; str.charCodeAt(i) === 48;) ++i;\r\n\r\n    // Determine trailing zeros.\r\n    for (len = str.length; str.charCodeAt(len - 1) === 48;) --len;\r\n    str = str.slice(i, len);\r\n\r\n    if (str) {\r\n      len -= i;\r\n      e = e - i - 1;\r\n      x.e = mathfloor(e / LOG_BASE);\r\n      x.d = [];\r\n\r\n      // Transform base\r\n\r\n      // e is the base 10 exponent.\r\n      // i is where to slice str to get the first word of the digits array.\r\n      i = (e + 1) % LOG_BASE;\r\n      if (e < 0) i += LOG_BASE;\r\n\r\n      if (i < len) {\r\n        if (i) x.d.push(+str.slice(0, i));\r\n        for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n        str = str.slice(i);\r\n        i = LOG_BASE - str.length;\r\n      } else {\r\n        i -= len;\r\n      }\r\n\r\n      for (; i--;) str += '0';\r\n      x.d.push(+str);\r\n\r\n      if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\r\n    } else {\r\n\r\n      // Zero.\r\n      x.s = 0;\r\n      x.e = 0;\r\n      x.d = [0];\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  /*\r\n   * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n   */\r\n   function round(x, sd, rm) {\r\n    var i, j, k, n, rd, doRound, w, xdi,\r\n      xd = x.d;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // n: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (n = 1, k = xd[0]; k >= 10; k /= 10) n++;\r\n    i = sd - n;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) return x;\r\n      w = k = xd[xdi];\r\n\r\n      // Get the number of digits of w.\r\n      for (n = 1; k >= 10; k /= 10) n++;\r\n\r\n      // Get the index of rd within w.\r\n      i %= LOG_BASE;\r\n\r\n      // Get the index of rd within w, adjusted for leading zeros.\r\n      // The number of leading zeros of w is given by LOG_BASE - n.\r\n      j = i - LOG_BASE + n;\r\n    }\r\n\r\n    if (rm !== void 0) {\r\n      k = mathpow(10, n - j - 1);\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / k % 10 | 0;\r\n\r\n      // Are there any non-zero digits after the rounding digit?\r\n      doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\r\n\r\n      // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\r\n      // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\r\n      // 714.\r\n\r\n      doRound = rm < 4\r\n        ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n        : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 &&\r\n\r\n          // Check whether the digit to the left of the rounding digit is odd.\r\n          ((i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n            rm == (x.s < 0 ? 8 : 7));\r\n    }\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      if (doRound) {\r\n        k = getBase10Exponent(x);\r\n        xd.length = 1;\r\n\r\n        // Convert sd to decimal places.\r\n        sd = sd - k - 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = mathfloor(-sd / LOG_BASE) || 0;\r\n      } else {\r\n        xd.length = 1;\r\n\r\n        // Zero.\r\n        xd[0] = x.e = x.s = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (doRound) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n          if ((xd[0] += k) == BASE) {\r\n            xd[0] = 1;\r\n            ++x.e;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n\r\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\r\n      throw Error(exponentOutOfRange + getBase10Exponent(x));\r\n    }\r\n\r\n    return x;\r\n  }\r\n\r\n\r\n  function subtract(x, y) {\r\n    var d, e, i, j, k, len, xd, xe, xLTy, yd,\r\n      Ctor = x.constructor,\r\n      pr = Ctor.precision;\r\n\r\n    // Return y negated if x is zero.\r\n    // Return x if y is zero and x is non-zero.\r\n    if (!x.s || !y.s) {\r\n      if (y.s) y.s = -y.s;\r\n      else y = new Ctor(x);\r\n      return external ? round(y, pr) : y;\r\n    }\r\n\r\n    xd = x.d;\r\n    yd = y.d;\r\n\r\n    // x and y are non-zero numbers with the same sign.\r\n\r\n    e = y.e;\r\n    xe = x.e;\r\n    xd = xd.slice();\r\n    k = xe - e;\r\n\r\n    // If exponents differ...\r\n    if (k) {\r\n      xLTy = k < 0;\r\n\r\n      if (xLTy) {\r\n        d = xd;\r\n        k = -k;\r\n        len = yd.length;\r\n      } else {\r\n        d = yd;\r\n        e = xe;\r\n        len = xd.length;\r\n      }\r\n\r\n      // Numbers with massively different exponents would result in a very high number of zeros\r\n      // needing to be prepended, but this can be avoided while still ensuring correct rounding by\r\n      // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n      i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n      if (k > i) {\r\n        k = i;\r\n        d.length = 1;\r\n      }\r\n\r\n      // Prepend zeros to equalise exponents.\r\n      d.reverse();\r\n      for (i = k; i--;) d.push(0);\r\n      d.reverse();\r\n\r\n    // Base 1e7 exponents equal.\r\n    } else {\r\n\r\n      // Check digits to determine which is the bigger number.\r\n\r\n      i = xd.length;\r\n      len = yd.length;\r\n      xLTy = i < len;\r\n      if (xLTy) len = i;\r\n\r\n      for (i = 0; i < len; i++) {\r\n        if (xd[i] != yd[i]) {\r\n          xLTy = xd[i] < yd[i];\r\n          break;\r\n        }\r\n      }\r\n\r\n      k = 0;\r\n    }\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      xd = yd;\r\n      yd = d;\r\n      y.s = -y.s;\r\n    }\r\n\r\n    len = xd.length;\r\n\r\n    // Append zeros to xd if shorter.\r\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\r\n    for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n    // Subtract yd from xd.\r\n    for (i = yd.length; i > k;) {\r\n      if (xd[--i] < yd[i]) {\r\n        for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n        --xd[j];\r\n        xd[i] += BASE;\r\n      }\r\n\r\n      xd[i] -= yd[i];\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] === 0;) xd.pop();\r\n\r\n    // Remove leading zeros and adjust exponent accordingly.\r\n    for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n    // Zero?\r\n    if (!xd[0]) return new Ctor(0);\r\n\r\n    y.d = xd;\r\n    y.e = e;\r\n\r\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\r\n    return external ? round(y, pr) : y;\r\n  }\r\n\r\n\r\n  function toString(x, isExp, sd) {\r\n    var k,\r\n      e = getBase10Exponent(x),\r\n      str = digitsToString(x.d),\r\n      len = str.length;\r\n\r\n    if (isExp) {\r\n      if (sd && (k = sd - len) > 0) {\r\n        str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n      } else if (len > 1) {\r\n        str = str.charAt(0) + '.' + str.slice(1);\r\n      }\r\n\r\n      str = str + (e < 0 ? 'e' : 'e+') + e;\r\n    } else if (e < 0) {\r\n      str = '0.' + getZeroString(-e - 1) + str;\r\n      if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n    } else if (e >= len) {\r\n      str += getZeroString(e + 1 - len);\r\n      if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n    } else {\r\n      if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n      if (sd && (k = sd - len) > 0) {\r\n        if (e + 1 === len) str += '.';\r\n        str += getZeroString(k);\r\n      }\r\n    }\r\n\r\n    return x.s < 0 ? '-' + str : str;\r\n  }\r\n\r\n\r\n  // Does not strip trailing zeros.\r\n  function truncate(arr, len) {\r\n    if (arr.length > len) {\r\n      arr.length = len;\r\n      return true;\r\n    }\r\n  }\r\n\r\n\r\n  // Decimal methods\r\n\r\n\r\n  /*\r\n   *  clone\r\n   *  config/set\r\n   */\r\n\r\n\r\n  /*\r\n   * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n   * constructor.\r\n   *\r\n   */\r\n  function clone(obj) {\r\n    var i, p, ps;\r\n\r\n    /*\r\n     * The Decimal constructor and exported function.\r\n     * Return a new Decimal instance.\r\n     *\r\n     * value {number|string|Decimal} A numeric value.\r\n     *\r\n     */\r\n    function Decimal(value) {\r\n      var x = this;\r\n\r\n      // Decimal called without new.\r\n      if (!(x instanceof Decimal)) return new Decimal(value);\r\n\r\n      // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n      // which points to Object.\r\n      x.constructor = Decimal;\r\n\r\n      // Duplicate.\r\n      if (value instanceof Decimal) {\r\n        x.s = value.s;\r\n        x.e = value.e;\r\n        x.d = (value = value.d) ? value.slice() : value;\r\n        return;\r\n      }\r\n\r\n      if (typeof value === 'number') {\r\n\r\n        // Reject Infinity/NaN.\r\n        if (value * 0 !== 0) {\r\n          throw Error(invalidArgument + value);\r\n        }\r\n\r\n        if (value > 0) {\r\n          x.s = 1;\r\n        } else if (value < 0) {\r\n          value = -value;\r\n          x.s = -1;\r\n        } else {\r\n          x.s = 0;\r\n          x.e = 0;\r\n          x.d = [0];\r\n          return;\r\n        }\r\n\r\n        // Fast path for small integers.\r\n        if (value === ~~value && value < 1e7) {\r\n          x.e = 0;\r\n          x.d = [value];\r\n          return;\r\n        }\r\n\r\n        return parseDecimal(x, value.toString());\r\n      } else if (typeof value !== 'string') {\r\n        throw Error(invalidArgument + value);\r\n      }\r\n\r\n      // Minus sign?\r\n      if (value.charCodeAt(0) === 45) {\r\n        value = value.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      if (isDecimal.test(value)) parseDecimal(x, value);\r\n      else throw Error(invalidArgument + value);\r\n    }\r\n\r\n    Decimal.prototype = P;\r\n\r\n    Decimal.ROUND_UP = 0;\r\n    Decimal.ROUND_DOWN = 1;\r\n    Decimal.ROUND_CEIL = 2;\r\n    Decimal.ROUND_FLOOR = 3;\r\n    Decimal.ROUND_HALF_UP = 4;\r\n    Decimal.ROUND_HALF_DOWN = 5;\r\n    Decimal.ROUND_HALF_EVEN = 6;\r\n    Decimal.ROUND_HALF_CEIL = 7;\r\n    Decimal.ROUND_HALF_FLOOR = 8;\r\n\r\n    Decimal.clone = clone;\r\n    Decimal.config = Decimal.set = config;\r\n\r\n    if (obj === void 0) obj = {};\r\n    if (obj) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'LN10'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n\r\n    Decimal.config(obj);\r\n\r\n    return Decimal;\r\n  }\r\n\r\n\r\n  /*\r\n   * Configure global settings for a Decimal constructor.\r\n   *\r\n   * `obj` is an object with one or more of the following properties,\r\n   *\r\n   *   precision  {number}\r\n   *   rounding   {number}\r\n   *   toExpNeg   {number}\r\n   *   toExpPos   {number}\r\n   *\r\n   * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n   *\r\n   */\r\n  function config(obj) {\r\n    if (!obj || typeof obj !== 'object') {\r\n      throw Error(decimalError + 'Object expected');\r\n    }\r\n    var i, p, v,\r\n      ps = [\r\n        'precision', 1, MAX_DIGITS,\r\n        'rounding', 0, 8,\r\n        'toExpNeg', -1 / 0, 0,\r\n        'toExpPos', 0, 1 / 0\r\n      ];\r\n\r\n    for (i = 0; i < ps.length; i += 3) {\r\n      if ((v = obj[p = ps[i]]) !== void 0) {\r\n        if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n        else throw Error(invalidArgument + p + ': ' + v);\r\n      }\r\n    }\r\n\r\n    if ((v = obj[p = 'LN10']) !== void 0) {\r\n        if (v == Math.LN10) this[p] = new this(v);\r\n        else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n\r\n    return this;\r\n  }\r\n\r\n\r\n  // Create and configure initial Decimal constructor.\r\n  Decimal = clone(Decimal);\r\n\r\n  Decimal['default'] = Decimal.Decimal = Decimal;\r\n\r\n  // Internal constant.\r\n  ONE = new Decimal(1);\r\n\r\n\r\n  // Export.\r\n\r\n\r\n  // AMD.\r\n  if (typeof define == 'function' && define.amd) {\r\n    define(function () {\r\n      return Decimal;\r\n    });\r\n\r\n  // Node and other environments that support module.exports.\r\n  } else if (typeof module != 'undefined' && module.exports) {\r\n    module.exports = Decimal;\r\n\r\n    // Browser.\r\n  } else {\r\n    if (!globalScope) {\r\n      globalScope = typeof self != 'undefined' && self && self.self == self\r\n        ? self : Function('return this')();\r\n    }\r\n\r\n    globalScope.Decimal = Decimal;\r\n  }\r\n})(this);\r\n"], "mappings": "AAAA;AACA;AAAC,CAAC,UAAUA,WAAW,EAAE;EACvB,YAAY;;EAGZ;AACF;AACA;AACA;AACA;AACA;AACA;;EAGE;;EAGE;EACA;EACF,IAAIC,UAAU,GAAG,GAAG;IAAyB;;IAG3C;IACAC,OAAO,GAAG;MAER;MACA;;MAEA;MACA;MACAC,SAAS,EAAE,EAAE;MAA0B;;MAEvC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC,QAAQ,EAAE,CAAC;MAA4B;;MAEvC;MACA;MACAC,QAAQ,EAAE,CAAC,CAAC;MAA2B;;MAEvC;MACA;MACAC,QAAQ,EAAG,EAAE;MAA0B;;MAEvC;MACA;MACAC,IAAI,EAAE;IACR,CAAC;IAGH;;IAGEC,QAAQ,GAAG,IAAI;IAEfC,YAAY,GAAG,iBAAiB;IAChCC,eAAe,GAAGD,YAAY,GAAG,oBAAoB;IACrDE,kBAAkB,GAAGF,YAAY,GAAG,yBAAyB;IAE7DG,SAAS,GAAGC,IAAI,CAACC,KAAK;IACtBC,OAAO,GAAGF,IAAI,CAACG,GAAG;IAElBC,SAAS,GAAG,oCAAoC;IAEhDC,GAAG;IACHC,IAAI,GAAG,GAAG;IACVC,QAAQ,GAAG,CAAC;IACZC,gBAAgB,GAAG,gBAAgB;IACnCC,KAAK,GAAGV,SAAS,CAACS,gBAAgB,GAAGD,QAAQ,CAAC;IAAK;;IAEnD;IACAG,CAAC,GAAG,CAAC,CAAC;;EAGR;;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE;AACF;AACA;AACA;EACEA,CAAC,CAACC,aAAa,GAAGD,CAAC,CAACE,GAAG,GAAG,YAAY;IACpC,IAAIC,CAAC,GAAG,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;IAClC,IAAID,CAAC,CAACE,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAG,CAAC;IAChB,OAAOF,CAAC;EACV,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;EACEH,CAAC,CAACM,UAAU,GAAGN,CAAC,CAACO,GAAG,GAAG,UAAUC,CAAC,EAAE;IAClC,IAAIC,CAAC;MAAEC,CAAC;MAAEC,GAAG;MAAEC,GAAG;MAChBT,CAAC,GAAG,IAAI;IAEVK,CAAC,GAAG,IAAIL,CAAC,CAACC,WAAW,CAACI,CAAC,CAAC;;IAExB;IACA,IAAIL,CAAC,CAACE,CAAC,KAAKG,CAAC,CAACH,CAAC,EAAE,OAAOF,CAAC,CAACE,CAAC,IAAI,CAACG,CAAC,CAACH,CAAC;;IAEnC;IACA,IAAIF,CAAC,CAACU,CAAC,KAAKL,CAAC,CAACK,CAAC,EAAE,OAAOV,CAAC,CAACU,CAAC,GAAGL,CAAC,CAACK,CAAC,GAAGV,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEpDM,GAAG,GAAGR,CAAC,CAACW,CAAC,CAACC,MAAM;IAChBH,GAAG,GAAGJ,CAAC,CAACM,CAAC,CAACC,MAAM;;IAEhB;IACA,KAAKN,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,GAAG,GAAGC,GAAG,GAAGD,GAAG,GAAGC,GAAG,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACjD,IAAIN,CAAC,CAACW,CAAC,CAACL,CAAC,CAAC,KAAKD,CAAC,CAACM,CAAC,CAACL,CAAC,CAAC,EAAE,OAAON,CAAC,CAACW,CAAC,CAACL,CAAC,CAAC,GAAGD,CAAC,CAACM,CAAC,CAACL,CAAC,CAAC,GAAGN,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAClE;;IAEA;IACA,OAAOM,GAAG,KAAKC,GAAG,GAAG,CAAC,GAAGD,GAAG,GAAGC,GAAG,GAAGT,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvD,CAAC;;EAGD;AACF;AACA;AACA;EACEL,CAAC,CAACgB,aAAa,GAAGhB,CAAC,CAACiB,EAAE,GAAG,YAAY;IACnC,IAAId,CAAC,GAAG,IAAI;MACVe,CAAC,GAAGf,CAAC,CAACW,CAAC,CAACC,MAAM,GAAG,CAAC;MAClBE,EAAE,GAAG,CAACC,CAAC,GAAGf,CAAC,CAACU,CAAC,IAAIhB,QAAQ;;IAE3B;IACAqB,CAAC,GAAGf,CAAC,CAACW,CAAC,CAACI,CAAC,CAAC;IACV,IAAIA,CAAC,EAAE,OAAOA,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAED,EAAE,EAAE;IAExC,OAAOA,EAAE,GAAG,CAAC,GAAG,CAAC,GAAGA,EAAE;EACxB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEjB,CAAC,CAACmB,SAAS,GAAGnB,CAAC,CAACoB,GAAG,GAAG,UAAUZ,CAAC,EAAE;IACjC,OAAOa,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,CAACjB,WAAW,CAACI,CAAC,CAAC,CAAC;EAC9C,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACER,CAAC,CAACsB,kBAAkB,GAAGtB,CAAC,CAACuB,IAAI,GAAG,UAAUf,CAAC,EAAE;IAC3C,IAAIL,CAAC,GAAG,IAAI;MACVqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IACtB,OAAOqB,KAAK,CAACJ,MAAM,CAAClB,CAAC,EAAE,IAAIqB,IAAI,CAAChB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEgB,IAAI,CAAC5C,SAAS,CAAC;EAC5D,CAAC;;EAGD;AACF;AACA;AACA;EACEoB,CAAC,CAAC0B,MAAM,GAAG1B,CAAC,CAAC2B,EAAE,GAAG,UAAUnB,CAAC,EAAE;IAC7B,OAAO,CAAC,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC;EACrB,CAAC;;EAGD;AACF;AACA;AACA;EACER,CAAC,CAAC4B,QAAQ,GAAG,YAAY;IACvB,OAAOC,iBAAiB,CAAC,IAAI,CAAC;EAChC,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACE7B,CAAC,CAAC8B,WAAW,GAAG9B,CAAC,CAAC+B,EAAE,GAAG,UAAUvB,CAAC,EAAE;IAClC,OAAO,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,GAAG,CAAC;EACxB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACER,CAAC,CAACgC,oBAAoB,GAAGhC,CAAC,CAACiC,GAAG,GAAG,UAAUzB,CAAC,EAAE;IAC5C,OAAO,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,IAAI,CAAC;EACzB,CAAC;;EAGD;AACF;AACA;AACA;EACER,CAAC,CAACkC,SAAS,GAAGlC,CAAC,CAACmC,KAAK,GAAG,YAAY;IAClC,OAAO,IAAI,CAACtB,CAAC,GAAG,IAAI,CAACC,CAAC,CAACC,MAAM,GAAG,CAAC;EACnC,CAAC;;EAGD;AACF;AACA;AACA;EACEf,CAAC,CAACoC,UAAU,GAAGpC,CAAC,CAACqC,KAAK,GAAG,YAAY;IACnC,OAAO,IAAI,CAAChC,CAAC,GAAG,CAAC;EACnB,CAAC;;EAGD;AACF;AACA;AACA;EACEL,CAAC,CAACsC,UAAU,GAAGtC,CAAC,CAACuC,KAAK,GAAG,YAAY;IACnC,OAAO,IAAI,CAAClC,CAAC,GAAG,CAAC;EACnB,CAAC;;EAGD;AACF;AACA;AACA;EACEL,CAAC,CAACwC,MAAM,GAAG,YAAY;IACrB,OAAO,IAAI,CAACnC,CAAC,KAAK,CAAC;EACrB,CAAC;;EAGD;AACF;AACA;AACA;EACEL,CAAC,CAACyC,QAAQ,GAAGzC,CAAC,CAAC0C,EAAE,GAAG,UAAUlC,CAAC,EAAE;IAC/B,OAAO,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,GAAG,CAAC;EACxB,CAAC;;EAGD;AACF;AACA;AACA;EACER,CAAC,CAAC2C,iBAAiB,GAAG3C,CAAC,CAAC4C,GAAG,GAAG,UAAUpC,CAAC,EAAE;IACzC,OAAO,IAAI,CAACD,GAAG,CAACC,CAAC,CAAC,GAAG,CAAC;EACxB,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACER,CAAC,CAAC6C,SAAS,GAAG7C,CAAC,CAAC8C,GAAG,GAAG,UAAUC,IAAI,EAAE;IACpC,IAAIC,CAAC;MACH7C,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;MACnBsE,GAAG,GAAGD,EAAE,GAAG,CAAC;;IAEd;IACA,IAAIF,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,IAAIvB,IAAI,CAAC,EAAE,CAAC;IACrB,CAAC,MAAM;MACLuB,IAAI,GAAG,IAAIvB,IAAI,CAACuB,IAAI,CAAC;;MAErB;MACA;MACA;MACA,IAAIA,IAAI,CAAC1C,CAAC,GAAG,CAAC,IAAI0C,IAAI,CAACpB,EAAE,CAAChC,GAAG,CAAC,EAAE,MAAMwD,KAAK,CAACjE,YAAY,GAAG,KAAK,CAAC;IACnE;;IAEA;IACA;IACA,IAAIiB,CAAC,CAACE,CAAC,GAAG,CAAC,EAAE,MAAM8C,KAAK,CAACjE,YAAY,IAAIiB,CAAC,CAACE,CAAC,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC;;IAEpE;IACA,IAAIF,CAAC,CAACwB,EAAE,CAAChC,GAAG,CAAC,EAAE,OAAO,IAAI6B,IAAI,CAAC,CAAC,CAAC;IAEjCvC,QAAQ,GAAG,KAAK;IAChB+D,CAAC,GAAG3B,MAAM,CAAC+B,EAAE,CAACjD,CAAC,EAAE+C,GAAG,CAAC,EAAEE,EAAE,CAACL,IAAI,EAAEG,GAAG,CAAC,EAAEA,GAAG,CAAC;IAC1CjE,QAAQ,GAAG,IAAI;IAEf,OAAOwC,KAAK,CAACuB,CAAC,EAAEC,EAAE,CAAC;EACrB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEjD,CAAC,CAACqD,KAAK,GAAGrD,CAAC,CAACsD,GAAG,GAAG,UAAU9C,CAAC,EAAE;IAC7B,IAAIL,CAAC,GAAG,IAAI;IACZK,CAAC,GAAG,IAAIL,CAAC,CAACC,WAAW,CAACI,CAAC,CAAC;IACxB,OAAOL,CAAC,CAACE,CAAC,IAAIG,CAAC,CAACH,CAAC,GAAGkD,QAAQ,CAACpD,CAAC,EAAEK,CAAC,CAAC,GAAGgD,GAAG,CAACrD,CAAC,GAAGK,CAAC,CAACH,CAAC,GAAG,CAACG,CAAC,CAACH,CAAC,EAAEG,CAAC,CAAC,CAAC;EAC9D,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACER,CAAC,CAACyD,MAAM,GAAGzD,CAAC,CAAC0D,GAAG,GAAG,UAAUlD,CAAC,EAAE;IAC9B,IAAImD,CAAC;MACHxD,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;IAErB4B,CAAC,GAAG,IAAIgB,IAAI,CAAChB,CAAC,CAAC;;IAEf;IACA,IAAI,CAACA,CAAC,CAACH,CAAC,EAAE,MAAM8C,KAAK,CAACjE,YAAY,GAAG,KAAK,CAAC;;IAE3C;IACA,IAAI,CAACiB,CAAC,CAACE,CAAC,EAAE,OAAOoB,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAE8C,EAAE,CAAC;;IAEvC;IACAhE,QAAQ,GAAG,KAAK;IAChB0E,CAAC,GAAGtC,MAAM,CAAClB,CAAC,EAAEK,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACoD,KAAK,CAACpD,CAAC,CAAC;IAC/BvB,QAAQ,GAAG,IAAI;IAEf,OAAOkB,CAAC,CAACkD,KAAK,CAACM,CAAC,CAAC;EACnB,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;EACE3D,CAAC,CAAC6D,kBAAkB,GAAG7D,CAAC,CAAC8D,GAAG,GAAG,YAAY;IACzC,OAAOA,GAAG,CAAC,IAAI,CAAC;EAClB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACE9D,CAAC,CAAC+D,gBAAgB,GAAG/D,CAAC,CAACoD,EAAE,GAAG,YAAY;IACtC,OAAOA,EAAE,CAAC,IAAI,CAAC;EACjB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEpD,CAAC,CAACgE,OAAO,GAAGhE,CAAC,CAACiE,GAAG,GAAG,YAAY;IAC9B,IAAI9D,CAAC,GAAG,IAAI,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC;IAClCD,CAAC,CAACE,CAAC,GAAG,CAACF,CAAC,CAACE,CAAC,IAAI,CAAC;IACf,OAAOF,CAAC;EACV,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEH,CAAC,CAACkE,IAAI,GAAGlE,CAAC,CAACwD,GAAG,GAAG,UAAUhD,CAAC,EAAE;IAC5B,IAAIL,CAAC,GAAG,IAAI;IACZK,CAAC,GAAG,IAAIL,CAAC,CAACC,WAAW,CAACI,CAAC,CAAC;IACxB,OAAOL,CAAC,CAACE,CAAC,IAAIG,CAAC,CAACH,CAAC,GAAGmD,GAAG,CAACrD,CAAC,EAAEK,CAAC,CAAC,GAAG+C,QAAQ,CAACpD,CAAC,GAAGK,CAAC,CAACH,CAAC,GAAG,CAACG,CAAC,CAACH,CAAC,EAAEG,CAAC,CAAC,CAAC;EAC9D,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;EACER,CAAC,CAACpB,SAAS,GAAGoB,CAAC,CAACmE,EAAE,GAAG,UAAUC,CAAC,EAAE;IAChC,IAAIvD,CAAC;MAAEsD,EAAE;MAAEjD,CAAC;MACVf,CAAC,GAAG,IAAI;IAEV,IAAIiE,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAACA,CAAC,IAAIA,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE,MAAMjB,KAAK,CAAChE,eAAe,GAAGiF,CAAC,CAAC;IAErFvD,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC,GAAG,CAAC;IAC5Be,CAAC,GAAGf,CAAC,CAACW,CAAC,CAACC,MAAM,GAAG,CAAC;IAClBoD,EAAE,GAAGjD,CAAC,GAAGrB,QAAQ,GAAG,CAAC;IACrBqB,CAAC,GAAGf,CAAC,CAACW,CAAC,CAACI,CAAC,CAAC;;IAEV;IACA,IAAIA,CAAC,EAAE;MAEL;MACA,OAAOA,CAAC,GAAG,EAAE,IAAI,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEiD,EAAE,EAAE;;MAEjC;MACA,KAAKjD,CAAC,GAAGf,CAAC,CAACW,CAAC,CAAC,CAAC,CAAC,EAAEI,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEiD,EAAE,EAAE;IACzC;IAEA,OAAOC,CAAC,IAAIvD,CAAC,GAAGsD,EAAE,GAAGtD,CAAC,GAAGsD,EAAE;EAC7B,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEnE,CAAC,CAACqE,UAAU,GAAGrE,CAAC,CAACsE,IAAI,GAAG,YAAY;IAClC,IAAIzD,CAAC;MAAE0D,CAAC;MAAEtB,EAAE;MAAED,CAAC;MAAE3C,CAAC;MAAEmE,CAAC;MAAEtB,GAAG;MACxB/C,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;;IAEtB;IACA,IAAID,CAAC,CAACE,CAAC,GAAG,CAAC,EAAE;MACX,IAAI,CAACF,CAAC,CAACE,CAAC,EAAE,OAAO,IAAImB,IAAI,CAAC,CAAC,CAAC;;MAE5B;MACA,MAAM2B,KAAK,CAACjE,YAAY,GAAG,KAAK,CAAC;IACnC;IAEA2B,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;IACxBlB,QAAQ,GAAG,KAAK;;IAEhB;IACAoB,CAAC,GAAGf,IAAI,CAACgF,IAAI,CAAC,CAACnE,CAAC,CAAC;;IAEjB;IACA;IACA,IAAIE,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;MACxBkE,CAAC,GAAGE,cAAc,CAACtE,CAAC,CAACW,CAAC,CAAC;MACvB,IAAI,CAACyD,CAAC,CAACxD,MAAM,GAAGF,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE0D,CAAC,IAAI,GAAG;MACrClE,CAAC,GAAGf,IAAI,CAACgF,IAAI,CAACC,CAAC,CAAC;MAChB1D,CAAC,GAAGxB,SAAS,CAAC,CAACwB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;MAE7C,IAAIR,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QACdkE,CAAC,GAAG,IAAI,GAAG1D,CAAC;MACd,CAAC,MAAM;QACL0D,CAAC,GAAGlE,CAAC,CAACqE,aAAa,CAAC,CAAC;QACrBH,CAAC,GAAGA,CAAC,CAACI,KAAK,CAAC,CAAC,EAAEJ,CAAC,CAACK,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG/D,CAAC;MACxC;MAEAmC,CAAC,GAAG,IAAIxB,IAAI,CAAC+C,CAAC,CAAC;IACjB,CAAC,MAAM;MACLvB,CAAC,GAAG,IAAIxB,IAAI,CAACnB,CAAC,CAACwE,QAAQ,CAAC,CAAC,CAAC;IAC5B;IAEA5B,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;IACnByB,CAAC,GAAG6C,GAAG,GAAGD,EAAE,GAAG,CAAC;;IAEhB;IACA,SAAS;MACPuB,CAAC,GAAGxB,CAAC;MACLA,CAAC,GAAGwB,CAAC,CAACN,IAAI,CAAC7C,MAAM,CAAClB,CAAC,EAAEqE,CAAC,EAAEtB,GAAG,GAAG,CAAC,CAAC,CAAC,CAACU,KAAK,CAAC,GAAG,CAAC;MAE5C,IAAIa,cAAc,CAACD,CAAC,CAAC1D,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,KAAK,CAACqB,CAAC,GAAGE,cAAc,CAACzB,CAAC,CAAClC,CAAC,CAAC,EAAE6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,EAAE;QACjFqB,CAAC,GAAGA,CAAC,CAACI,KAAK,CAACzB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,CAAC;;QAE7B;QACA;QACA,IAAI7C,CAAC,IAAI6C,GAAG,IAAIqB,CAAC,IAAI,MAAM,EAAE;UAE3B;UACA;UACA9C,KAAK,CAAC+C,CAAC,EAAEvB,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;UAEnB,IAAIuB,CAAC,CAACZ,KAAK,CAACY,CAAC,CAAC,CAAC7C,EAAE,CAACxB,CAAC,CAAC,EAAE;YACpB6C,CAAC,GAAGwB,CAAC;YACL;UACF;QACF,CAAC,MAAM,IAAID,CAAC,IAAI,MAAM,EAAE;UACtB;QACF;QAEArB,GAAG,IAAI,CAAC;MACV;IACF;IAEAjE,QAAQ,GAAG,IAAI;IAEf,OAAOwC,KAAK,CAACuB,CAAC,EAAEC,EAAE,CAAC;EACrB,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACEjD,CAAC,CAAC4D,KAAK,GAAG5D,CAAC,CAAC8E,GAAG,GAAG,UAAUtE,CAAC,EAAE;IAC7B,IAAIuE,KAAK;MAAElE,CAAC;MAAEJ,CAAC;MAAEuE,CAAC;MAAEhC,CAAC;MAAEiC,EAAE;MAAET,CAAC;MAAE7D,GAAG;MAAEC,GAAG;MACpCT,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB8E,EAAE,GAAG/E,CAAC,CAACW,CAAC;MACRqE,EAAE,GAAG,CAAC3E,CAAC,GAAG,IAAIgB,IAAI,CAAChB,CAAC,CAAC,EAAEM,CAAC;;IAE1B;IACA,IAAI,CAACX,CAAC,CAACE,CAAC,IAAI,CAACG,CAAC,CAACH,CAAC,EAAE,OAAO,IAAImB,IAAI,CAAC,CAAC,CAAC;IAEpChB,CAAC,CAACH,CAAC,IAAIF,CAAC,CAACE,CAAC;IACVQ,CAAC,GAAGV,CAAC,CAACU,CAAC,GAAGL,CAAC,CAACK,CAAC;IACbF,GAAG,GAAGuE,EAAE,CAACnE,MAAM;IACfH,GAAG,GAAGuE,EAAE,CAACpE,MAAM;;IAEf;IACA,IAAIJ,GAAG,GAAGC,GAAG,EAAE;MACboC,CAAC,GAAGkC,EAAE;MACNA,EAAE,GAAGC,EAAE;MACPA,EAAE,GAAGnC,CAAC;MACNiC,EAAE,GAAGtE,GAAG;MACRA,GAAG,GAAGC,GAAG;MACTA,GAAG,GAAGqE,EAAE;IACV;;IAEA;IACAjC,CAAC,GAAG,EAAE;IACNiC,EAAE,GAAGtE,GAAG,GAAGC,GAAG;IACd,KAAKH,CAAC,GAAGwE,EAAE,EAAExE,CAAC,EAAE,GAAGuC,CAAC,CAACoC,IAAI,CAAC,CAAC,CAAC;;IAE5B;IACA,KAAK3E,CAAC,GAAGG,GAAG,EAAE,EAAEH,CAAC,IAAI,CAAC,GAAG;MACvBsE,KAAK,GAAG,CAAC;MACT,KAAKC,CAAC,GAAGrE,GAAG,GAAGF,CAAC,EAAEuE,CAAC,GAAGvE,CAAC,GAAG;QACxB+D,CAAC,GAAGxB,CAAC,CAACgC,CAAC,CAAC,GAAGG,EAAE,CAAC1E,CAAC,CAAC,GAAGyE,EAAE,CAACF,CAAC,GAAGvE,CAAC,GAAG,CAAC,CAAC,GAAGsE,KAAK;QACxC/B,CAAC,CAACgC,CAAC,EAAE,CAAC,GAAGR,CAAC,GAAG5E,IAAI,GAAG,CAAC;QACrBmF,KAAK,GAAGP,CAAC,GAAG5E,IAAI,GAAG,CAAC;MACtB;MAEAoD,CAAC,CAACgC,CAAC,CAAC,GAAG,CAAChC,CAAC,CAACgC,CAAC,CAAC,GAAGD,KAAK,IAAInF,IAAI,GAAG,CAAC;IAClC;;IAEA;IACA,OAAO,CAACoD,CAAC,CAAC,EAAEiC,EAAE,CAAC,GAAGjC,CAAC,CAACqC,GAAG,CAAC,CAAC;IAEzB,IAAIN,KAAK,EAAE,EAAElE,CAAC,CAAC,KACVmC,CAAC,CAACsC,KAAK,CAAC,CAAC;IAEd9E,CAAC,CAACM,CAAC,GAAGkC,CAAC;IACPxC,CAAC,CAACK,CAAC,GAAGA,CAAC;IAEP,OAAO5B,QAAQ,GAAGwC,KAAK,CAACjB,CAAC,EAAEgB,IAAI,CAAC5C,SAAS,CAAC,GAAG4B,CAAC;EAChD,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACER,CAAC,CAACuF,eAAe,GAAGvF,CAAC,CAACwF,IAAI,GAAG,UAAUvE,EAAE,EAAEwE,EAAE,EAAE;IAC7C,IAAItF,CAAC,GAAG,IAAI;MACVqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtBD,CAAC,GAAG,IAAIqB,IAAI,CAACrB,CAAC,CAAC;IACf,IAAIc,EAAE,KAAK,KAAK,CAAC,EAAE,OAAOd,CAAC;IAE3BuF,UAAU,CAACzE,EAAE,EAAE,CAAC,EAAEvC,UAAU,CAAC;IAE7B,IAAI+G,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ,CAAC,KACjC6G,UAAU,CAACD,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzB,OAAOhE,KAAK,CAACtB,CAAC,EAAEc,EAAE,GAAGY,iBAAiB,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEsF,EAAE,CAAC;EACpD,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzF,CAAC,CAAC0E,aAAa,GAAG,UAAUzD,EAAE,EAAEwE,EAAE,EAAE;IAClC,IAAIE,GAAG;MACLxF,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtB,IAAIa,EAAE,KAAK,KAAK,CAAC,EAAE;MACjB0E,GAAG,GAAGd,QAAQ,CAAC1E,CAAC,EAAE,IAAI,CAAC;IACzB,CAAC,MAAM;MACLuF,UAAU,CAACzE,EAAE,EAAE,CAAC,EAAEvC,UAAU,CAAC;MAE7B,IAAI+G,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ,CAAC,KACjC6G,UAAU,CAACD,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzBtF,CAAC,GAAGsB,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAEc,EAAE,GAAG,CAAC,EAAEwE,EAAE,CAAC;MAClCE,GAAG,GAAGd,QAAQ,CAAC1E,CAAC,EAAE,IAAI,EAAEc,EAAE,GAAG,CAAC,CAAC;IACjC;IAEA,OAAO0E,GAAG;EACZ,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3F,CAAC,CAAC4F,OAAO,GAAG,UAAU3E,EAAE,EAAEwE,EAAE,EAAE;IAC5B,IAAIE,GAAG;MAAEnF,CAAC;MACRL,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtB,IAAIa,EAAE,KAAK,KAAK,CAAC,EAAE,OAAO4D,QAAQ,CAAC1E,CAAC,CAAC;IAErCuF,UAAU,CAACzE,EAAE,EAAE,CAAC,EAAEvC,UAAU,CAAC;IAE7B,IAAI+G,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ,CAAC,KACjC6G,UAAU,CAACD,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAEzBjF,CAAC,GAAGiB,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAEc,EAAE,GAAGY,iBAAiB,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEsF,EAAE,CAAC;IACzDE,GAAG,GAAGd,QAAQ,CAACrE,CAAC,CAACN,GAAG,CAAC,CAAC,EAAE,KAAK,EAAEe,EAAE,GAAGY,iBAAiB,CAACrB,CAAC,CAAC,GAAG,CAAC,CAAC;;IAE7D;IACA;IACA,OAAOL,CAAC,CAACkC,KAAK,CAAC,CAAC,IAAI,CAAClC,CAAC,CAACqC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGmD,GAAG,GAAGA,GAAG;EACnD,CAAC;;EAGD;AACF;AACA;AACA;AACA;EACE3F,CAAC,CAAC6F,SAAS,GAAG7F,CAAC,CAAC8F,KAAK,GAAG,YAAY;IAClC,IAAI3F,CAAC,GAAG,IAAI;MACVqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IACtB,OAAOqB,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAE0B,iBAAiB,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEqB,IAAI,CAAC3C,QAAQ,CAAC;EACpE,CAAC;;EAGD;AACF;AACA;AACA;EACEmB,CAAC,CAAC+F,QAAQ,GAAG,YAAY;IACvB,OAAO,CAAC,IAAI;EACd,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE/F,CAAC,CAACgG,OAAO,GAAGhG,CAAC,CAACP,GAAG,GAAG,UAAUe,CAAC,EAAE;IAC/B,IAAIK,CAAC;MAAEmE,CAAC;MAAE/B,EAAE;MAAED,CAAC;MAAEiD,IAAI;MAAEC,MAAM;MAC3B/F,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB+F,KAAK,GAAG,EAAE;MACVC,EAAE,GAAG,EAAE5F,CAAC,GAAG,IAAIgB,IAAI,CAAChB,CAAC,CAAC,CAAC;;IAEzB;IACA,IAAI,CAACA,CAAC,CAACH,CAAC,EAAE,OAAO,IAAImB,IAAI,CAAC7B,GAAG,CAAC;IAE9BQ,CAAC,GAAG,IAAIqB,IAAI,CAACrB,CAAC,CAAC;;IAEf;IACA;IACA,IAAI,CAACA,CAAC,CAACE,CAAC,EAAE;MACR,IAAIG,CAAC,CAACH,CAAC,GAAG,CAAC,EAAE,MAAM8C,KAAK,CAACjE,YAAY,GAAG,UAAU,CAAC;MACnD,OAAOiB,CAAC;IACV;;IAEA;IACA,IAAIA,CAAC,CAACwB,EAAE,CAAChC,GAAG,CAAC,EAAE,OAAOQ,CAAC;IAEvB8C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;;IAEnB;IACA,IAAI4B,CAAC,CAACmB,EAAE,CAAChC,GAAG,CAAC,EAAE,OAAO8B,KAAK,CAACtB,CAAC,EAAE8C,EAAE,CAAC;IAElCpC,CAAC,GAAGL,CAAC,CAACK,CAAC;IACPmE,CAAC,GAAGxE,CAAC,CAACM,CAAC,CAACC,MAAM,GAAG,CAAC;IAClBmF,MAAM,GAAGrF,CAAC,IAAImE,CAAC;IACfiB,IAAI,GAAG9F,CAAC,CAACE,CAAC;IAEV,IAAI,CAAC6F,MAAM,EAAE;MAEX;MACA,IAAID,IAAI,GAAG,CAAC,EAAE,MAAM9C,KAAK,CAACjE,YAAY,GAAG,KAAK,CAAC;;MAEjD;IACA,CAAC,MAAM,IAAI,CAAC8F,CAAC,GAAGoB,EAAE,GAAG,CAAC,GAAG,CAACA,EAAE,GAAGA,EAAE,KAAKtG,gBAAgB,EAAE;MACtDkD,CAAC,GAAG,IAAIxB,IAAI,CAAC7B,GAAG,CAAC;;MAEjB;MACA;MACAkB,CAAC,GAAGvB,IAAI,CAAC+G,IAAI,CAACpD,EAAE,GAAGpD,QAAQ,GAAG,CAAC,CAAC;MAEhCZ,QAAQ,GAAG,KAAK;MAEhB,SAAS;QACP,IAAI+F,CAAC,GAAG,CAAC,EAAE;UACThC,CAAC,GAAGA,CAAC,CAACY,KAAK,CAACzD,CAAC,CAAC;UACdmG,QAAQ,CAACtD,CAAC,CAAClC,CAAC,EAAED,CAAC,CAAC;QAClB;QAEAmE,CAAC,GAAG3F,SAAS,CAAC2F,CAAC,GAAG,CAAC,CAAC;QACpB,IAAIA,CAAC,KAAK,CAAC,EAAE;QAEb7E,CAAC,GAAGA,CAAC,CAACyD,KAAK,CAACzD,CAAC,CAAC;QACdmG,QAAQ,CAACnG,CAAC,CAACW,CAAC,EAAED,CAAC,CAAC;MAClB;MAEA5B,QAAQ,GAAG,IAAI;MAEf,OAAOuB,CAAC,CAACH,CAAC,GAAG,CAAC,GAAG,IAAImB,IAAI,CAAC7B,GAAG,CAAC,CAACyB,GAAG,CAAC4B,CAAC,CAAC,GAAGvB,KAAK,CAACuB,CAAC,EAAEC,EAAE,CAAC;IACtD;;IAEA;IACAgD,IAAI,GAAGA,IAAI,GAAG,CAAC,IAAIzF,CAAC,CAACM,CAAC,CAACxB,IAAI,CAACiH,GAAG,CAAC1F,CAAC,EAAEmE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAEnD7E,CAAC,CAACE,CAAC,GAAG,CAAC;IACPpB,QAAQ,GAAG,KAAK;IAChB+D,CAAC,GAAGxC,CAAC,CAACoD,KAAK,CAACR,EAAE,CAACjD,CAAC,EAAE8C,EAAE,GAAGkD,KAAK,CAAC,CAAC;IAC9BlH,QAAQ,GAAG,IAAI;IACf+D,CAAC,GAAGc,GAAG,CAACd,CAAC,CAAC;IACVA,CAAC,CAAC3C,CAAC,GAAG4F,IAAI;IAEV,OAAOjD,CAAC;EACV,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhD,CAAC,CAACwG,WAAW,GAAG,UAAUrC,EAAE,EAAEsB,EAAE,EAAE;IAChC,IAAI5E,CAAC;MAAE8E,GAAG;MACRxF,CAAC,GAAG,IAAI;MACRqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtB,IAAI+D,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBtD,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;MACxBwF,GAAG,GAAGd,QAAQ,CAAC1E,CAAC,EAAEU,CAAC,IAAIW,IAAI,CAAC1C,QAAQ,IAAI+B,CAAC,IAAIW,IAAI,CAACzC,QAAQ,CAAC;IAC7D,CAAC,MAAM;MACL2G,UAAU,CAACvB,EAAE,EAAE,CAAC,EAAEzF,UAAU,CAAC;MAE7B,IAAI+G,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ,CAAC,KACjC6G,UAAU,CAACD,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;MAEzBtF,CAAC,GAAGsB,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAEgE,EAAE,EAAEsB,EAAE,CAAC;MAC9B5E,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;MACxBwF,GAAG,GAAGd,QAAQ,CAAC1E,CAAC,EAAEgE,EAAE,IAAItD,CAAC,IAAIA,CAAC,IAAIW,IAAI,CAAC1C,QAAQ,EAAEqF,EAAE,CAAC;IACtD;IAEA,OAAOwB,GAAG;EACZ,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE3F,CAAC,CAACyG,mBAAmB,GAAGzG,CAAC,CAAC0G,IAAI,GAAG,UAAUvC,EAAE,EAAEsB,EAAE,EAAE;IACjD,IAAItF,CAAC,GAAG,IAAI;MACVqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtB,IAAI+D,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAG3C,IAAI,CAAC5C,SAAS;MACnB6G,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ;IACpB,CAAC,MAAM;MACL6G,UAAU,CAACvB,EAAE,EAAE,CAAC,EAAEzF,UAAU,CAAC;MAE7B,IAAI+G,EAAE,KAAK,KAAK,CAAC,EAAEA,EAAE,GAAGjE,IAAI,CAAC3C,QAAQ,CAAC,KACjC6G,UAAU,CAACD,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3B;IAEA,OAAOhE,KAAK,CAAC,IAAID,IAAI,CAACrB,CAAC,CAAC,EAAEgE,EAAE,EAAEsB,EAAE,CAAC;EACnC,CAAC;;EAGD;AACF;AACA;AACA;AACA;AACA;AACA;EACEzF,CAAC,CAAC6E,QAAQ,GAAG7E,CAAC,CAAC2G,OAAO,GAAG3G,CAAC,CAAC4G,GAAG,GAAG5G,CAAC,CAAC6G,MAAM,GAAG,YAAY;IACtD,IAAI1G,CAAC,GAAG,IAAI;MACVU,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;MACxBqB,IAAI,GAAGrB,CAAC,CAACC,WAAW;IAEtB,OAAOyE,QAAQ,CAAC1E,CAAC,EAAEU,CAAC,IAAIW,IAAI,CAAC1C,QAAQ,IAAI+B,CAAC,IAAIW,IAAI,CAACzC,QAAQ,CAAC;EAC9D,CAAC;;EAGD;;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASyE,GAAGA,CAACrD,CAAC,EAAEK,CAAC,EAAE;IACjB,IAAIuE,KAAK;MAAEjE,CAAC;MAAED,CAAC;MAAEJ,CAAC;MAAEuE,CAAC;MAAE8B,GAAG;MAAE5B,EAAE;MAAEC,EAAE;MAChC3D,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;;IAErB;IACA,IAAI,CAACuB,CAAC,CAACE,CAAC,IAAI,CAACG,CAAC,CAACH,CAAC,EAAE;MAEhB;MACA;MACA,IAAI,CAACG,CAAC,CAACH,CAAC,EAAEG,CAAC,GAAG,IAAIgB,IAAI,CAACrB,CAAC,CAAC;MACzB,OAAOlB,QAAQ,GAAGwC,KAAK,CAACjB,CAAC,EAAEyC,EAAE,CAAC,GAAGzC,CAAC;IACpC;IAEA0E,EAAE,GAAG/E,CAAC,CAACW,CAAC;IACRqE,EAAE,GAAG3E,CAAC,CAACM,CAAC;;IAER;;IAEAkE,CAAC,GAAG7E,CAAC,CAACU,CAAC;IACPA,CAAC,GAAGL,CAAC,CAACK,CAAC;IACPqE,EAAE,GAAGA,EAAE,CAACP,KAAK,CAAC,CAAC;IACflE,CAAC,GAAGuE,CAAC,GAAGnE,CAAC;;IAET;IACA,IAAIJ,CAAC,EAAE;MACL,IAAIA,CAAC,GAAG,CAAC,EAAE;QACTK,CAAC,GAAGoE,EAAE;QACNzE,CAAC,GAAG,CAACA,CAAC;QACNqG,GAAG,GAAG3B,EAAE,CAACpE,MAAM;MACjB,CAAC,MAAM;QACLD,CAAC,GAAGqE,EAAE;QACNtE,CAAC,GAAGmE,CAAC;QACL8B,GAAG,GAAG5B,EAAE,CAACnE,MAAM;MACjB;;MAEA;MACAiE,CAAC,GAAG1F,IAAI,CAAC+G,IAAI,CAACpD,EAAE,GAAGpD,QAAQ,CAAC;MAC5BiH,GAAG,GAAG9B,CAAC,GAAG8B,GAAG,GAAG9B,CAAC,GAAG,CAAC,GAAG8B,GAAG,GAAG,CAAC;MAE/B,IAAIrG,CAAC,GAAGqG,GAAG,EAAE;QACXrG,CAAC,GAAGqG,GAAG;QACPhG,CAAC,CAACC,MAAM,GAAG,CAAC;MACd;;MAEA;MACAD,CAAC,CAACiG,OAAO,CAAC,CAAC;MACX,OAAOtG,CAAC,EAAE,GAAGK,CAAC,CAACsE,IAAI,CAAC,CAAC,CAAC;MACtBtE,CAAC,CAACiG,OAAO,CAAC,CAAC;IACb;IAEAD,GAAG,GAAG5B,EAAE,CAACnE,MAAM;IACfN,CAAC,GAAG0E,EAAE,CAACpE,MAAM;;IAEb;IACA,IAAI+F,GAAG,GAAGrG,CAAC,GAAG,CAAC,EAAE;MACfA,CAAC,GAAGqG,GAAG;MACPhG,CAAC,GAAGqE,EAAE;MACNA,EAAE,GAAGD,EAAE;MACPA,EAAE,GAAGpE,CAAC;IACR;;IAEA;IACA,KAAKiE,KAAK,GAAG,CAAC,EAAEtE,CAAC,GAAG;MAClBsE,KAAK,GAAG,CAACG,EAAE,CAAC,EAAEzE,CAAC,CAAC,GAAGyE,EAAE,CAACzE,CAAC,CAAC,GAAG0E,EAAE,CAAC1E,CAAC,CAAC,GAAGsE,KAAK,IAAInF,IAAI,GAAG,CAAC;MACpDsF,EAAE,CAACzE,CAAC,CAAC,IAAIb,IAAI;IACf;IAEA,IAAImF,KAAK,EAAE;MACTG,EAAE,CAAC8B,OAAO,CAACjC,KAAK,CAAC;MACjB,EAAElE,CAAC;IACL;;IAEA;IACA;IACA,KAAKiG,GAAG,GAAG5B,EAAE,CAACnE,MAAM,EAAEmE,EAAE,CAAC,EAAE4B,GAAG,CAAC,IAAI,CAAC,GAAG5B,EAAE,CAACG,GAAG,CAAC,CAAC;IAE/C7E,CAAC,CAACM,CAAC,GAAGoE,EAAE;IACR1E,CAAC,CAACK,CAAC,GAAGA,CAAC;IAEP,OAAO5B,QAAQ,GAAGwC,KAAK,CAACjB,CAAC,EAAEyC,EAAE,CAAC,GAAGzC,CAAC;EACpC;EAGA,SAASkF,UAAUA,CAACjF,CAAC,EAAEwG,GAAG,EAAEV,GAAG,EAAE;IAC/B,IAAI9F,CAAC,KAAK,CAAC,CAACA,CAAC,IAAIA,CAAC,GAAGwG,GAAG,IAAIxG,CAAC,GAAG8F,GAAG,EAAE;MACnC,MAAMpD,KAAK,CAAChE,eAAe,GAAGsB,CAAC,CAAC;IAClC;EACF;EAGA,SAASgE,cAAcA,CAAC3D,CAAC,EAAE;IACzB,IAAIL,CAAC;MAAEuE,CAAC;MAAEkC,EAAE;MACVC,eAAe,GAAGrG,CAAC,CAACC,MAAM,GAAG,CAAC;MAC9B4E,GAAG,GAAG,EAAE;MACRzE,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;IAEV,IAAIqG,eAAe,GAAG,CAAC,EAAE;MACvBxB,GAAG,IAAIzE,CAAC;MACR,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0G,eAAe,EAAE1G,CAAC,EAAE,EAAE;QACpCyG,EAAE,GAAGpG,CAAC,CAACL,CAAC,CAAC,GAAG,EAAE;QACduE,CAAC,GAAGnF,QAAQ,GAAGqH,EAAE,CAACnG,MAAM;QACxB,IAAIiE,CAAC,EAAEW,GAAG,IAAIyB,aAAa,CAACpC,CAAC,CAAC;QAC9BW,GAAG,IAAIuB,EAAE;MACX;MAEAhG,CAAC,GAAGJ,CAAC,CAACL,CAAC,CAAC;MACRyG,EAAE,GAAGhG,CAAC,GAAG,EAAE;MACX8D,CAAC,GAAGnF,QAAQ,GAAGqH,EAAE,CAACnG,MAAM;MACxB,IAAIiE,CAAC,EAAEW,GAAG,IAAIyB,aAAa,CAACpC,CAAC,CAAC;IAChC,CAAC,MAAM,IAAI9D,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,GAAG;IACZ;;IAEA;IACA,OAAOA,CAAC,GAAG,EAAE,KAAK,CAAC,GAAGA,CAAC,IAAI,EAAE;IAE7B,OAAOyE,GAAG,GAAGzE,CAAC;EAChB;EAGA,IAAIG,MAAM,GAAI,YAAY;IAExB;IACA,SAASgG,eAAeA,CAAClH,CAAC,EAAE6E,CAAC,EAAE;MAC7B,IAAIsC,IAAI;QACNvC,KAAK,GAAG,CAAC;QACTtE,CAAC,GAAGN,CAAC,CAACY,MAAM;MAEd,KAAKZ,CAAC,GAAGA,CAAC,CAACwE,KAAK,CAAC,CAAC,EAAElE,CAAC,EAAE,GAAG;QACxB6G,IAAI,GAAGnH,CAAC,CAACM,CAAC,CAAC,GAAGuE,CAAC,GAAGD,KAAK;QACvB5E,CAAC,CAACM,CAAC,CAAC,GAAG6G,IAAI,GAAG1H,IAAI,GAAG,CAAC;QACtBmF,KAAK,GAAGuC,IAAI,GAAG1H,IAAI,GAAG,CAAC;MACzB;MAEA,IAAImF,KAAK,EAAE5E,CAAC,CAAC6G,OAAO,CAACjC,KAAK,CAAC;MAE3B,OAAO5E,CAAC;IACV;IAEA,SAASoH,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;MAC7B,IAAIlH,CAAC,EAAEuC,CAAC;MAER,IAAI0E,EAAE,IAAIC,EAAE,EAAE;QACZ3E,CAAC,GAAG0E,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;MACtB,CAAC,MAAM;QACL,KAAKlH,CAAC,GAAGuC,CAAC,GAAG,CAAC,EAAEvC,CAAC,GAAGiH,EAAE,EAAEjH,CAAC,EAAE,EAAE;UAC3B,IAAI+G,CAAC,CAAC/G,CAAC,CAAC,IAAIgH,CAAC,CAAChH,CAAC,CAAC,EAAE;YAChBuC,CAAC,GAAGwE,CAAC,CAAC/G,CAAC,CAAC,GAAGgH,CAAC,CAAChH,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxB;UACF;QACF;MACF;MAEA,OAAOuC,CAAC;IACV;IAEA,SAASO,QAAQA,CAACiE,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAE;MAC1B,IAAIjH,CAAC,GAAG,CAAC;;MAET;MACA,OAAOiH,EAAE,EAAE,GAAG;QACZF,CAAC,CAACE,EAAE,CAAC,IAAIjH,CAAC;QACVA,CAAC,GAAG+G,CAAC,CAACE,EAAE,CAAC,GAAGD,CAAC,CAACC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC;QACzBF,CAAC,CAACE,EAAE,CAAC,GAAGjH,CAAC,GAAGb,IAAI,GAAG4H,CAAC,CAACE,EAAE,CAAC,GAAGD,CAAC,CAACC,EAAE,CAAC;MAClC;;MAEA;MACA,OAAO,CAACF,CAAC,CAAC,CAAC,CAAC,IAAIA,CAAC,CAACzG,MAAM,GAAG,CAAC,GAAGyG,CAAC,CAAClC,KAAK,CAAC,CAAC;IAC1C;IAEA,OAAO,UAAUnF,CAAC,EAAEK,CAAC,EAAEyC,EAAE,EAAEhC,EAAE,EAAE;MAC7B,IAAIV,GAAG;QAAEM,CAAC;QAAEJ,CAAC;QAAEuE,CAAC;QAAE4C,IAAI;QAAEC,KAAK;QAAElE,CAAC;QAAEmE,EAAE;QAAEC,GAAG;QAAEC,IAAI;QAAEC,IAAI;QAAE9D,EAAE;QAAEK,CAAC;QAAE0D,EAAE;QAAEC,EAAE;QAAEC,GAAG;QAAEC,EAAE;QAAEC,EAAE;QAC/E9G,IAAI,GAAGrB,CAAC,CAACC,WAAW;QACpB6F,IAAI,GAAG9F,CAAC,CAACE,CAAC,IAAIG,CAAC,CAACH,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1B6E,EAAE,GAAG/E,CAAC,CAACW,CAAC;QACRqE,EAAE,GAAG3E,CAAC,CAACM,CAAC;;MAEV;MACA,IAAI,CAACX,CAAC,CAACE,CAAC,EAAE,OAAO,IAAImB,IAAI,CAACrB,CAAC,CAAC;MAC5B,IAAI,CAACK,CAAC,CAACH,CAAC,EAAE,MAAM8C,KAAK,CAACjE,YAAY,GAAG,kBAAkB,CAAC;MAExD2B,CAAC,GAAGV,CAAC,CAACU,CAAC,GAAGL,CAAC,CAACK,CAAC;MACbwH,EAAE,GAAGlD,EAAE,CAACpE,MAAM;MACdoH,EAAE,GAAGjD,EAAE,CAACnE,MAAM;MACd4C,CAAC,GAAG,IAAInC,IAAI,CAACyE,IAAI,CAAC;MAClB6B,EAAE,GAAGnE,CAAC,CAAC7C,CAAC,GAAG,EAAE;;MAEb;MACA,KAAKL,CAAC,GAAG,CAAC,EAAE0E,EAAE,CAAC1E,CAAC,CAAC,KAAKyE,EAAE,CAACzE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAI,EAAEA,CAAC;MACxC,IAAI0E,EAAE,CAAC1E,CAAC,CAAC,IAAIyE,EAAE,CAACzE,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAEI,CAAC;MAE7B,IAAIoC,EAAE,IAAI,IAAI,EAAE;QACdkB,EAAE,GAAGlB,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;MAC1B,CAAC,MAAM,IAAIqC,EAAE,EAAE;QACbkD,EAAE,GAAGlB,EAAE,IAAIpB,iBAAiB,CAAC1B,CAAC,CAAC,GAAG0B,iBAAiB,CAACrB,CAAC,CAAC,CAAC,GAAG,CAAC;MAC7D,CAAC,MAAM;QACL2D,EAAE,GAAGlB,EAAE;MACT;MAEA,IAAIkB,EAAE,GAAG,CAAC,EAAE,OAAO,IAAI3C,IAAI,CAAC,CAAC,CAAC;;MAE9B;MACA2C,EAAE,GAAGA,EAAE,GAAGtE,QAAQ,GAAG,CAAC,GAAG,CAAC;MAC1BY,CAAC,GAAG,CAAC;;MAEL;MACA,IAAI4H,EAAE,IAAI,CAAC,EAAE;QACXrD,CAAC,GAAG,CAAC;QACLG,EAAE,GAAGA,EAAE,CAAC,CAAC,CAAC;QACVhB,EAAE,EAAE;;QAEJ;QACA,OAAO,CAAC1D,CAAC,GAAG0H,EAAE,IAAInD,CAAC,KAAKb,EAAE,EAAE,EAAE1D,CAAC,EAAE,EAAE;UACjC+D,CAAC,GAAGQ,CAAC,GAAGpF,IAAI,IAAIsF,EAAE,CAACzE,CAAC,CAAC,IAAI,CAAC,CAAC;UAC3BqH,EAAE,CAACrH,CAAC,CAAC,GAAG+D,CAAC,GAAGW,EAAE,GAAG,CAAC;UAClBH,CAAC,GAAGR,CAAC,GAAGW,EAAE,GAAG,CAAC;QAChB;;QAEF;MACA,CAAC,MAAM;QAEL;QACAH,CAAC,GAAGpF,IAAI,IAAIuF,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QAE1B,IAAIH,CAAC,GAAG,CAAC,EAAE;UACTG,EAAE,GAAGkC,eAAe,CAAClC,EAAE,EAAEH,CAAC,CAAC;UAC3BE,EAAE,GAAGmC,eAAe,CAACnC,EAAE,EAAEF,CAAC,CAAC;UAC3BqD,EAAE,GAAGlD,EAAE,CAACpE,MAAM;UACdoH,EAAE,GAAGjD,EAAE,CAACnE,MAAM;QAChB;QAEAmH,EAAE,GAAGG,EAAE;QACPN,GAAG,GAAG7C,EAAE,CAACP,KAAK,CAAC,CAAC,EAAE0D,EAAE,CAAC;QACrBL,IAAI,GAAGD,GAAG,CAAChH,MAAM;;QAEjB;QACA,OAAOiH,IAAI,GAAGK,EAAE,GAAGN,GAAG,CAACC,IAAI,EAAE,CAAC,GAAG,CAAC;QAElCM,EAAE,GAAGnD,EAAE,CAACR,KAAK,CAAC,CAAC;QACf2D,EAAE,CAACtB,OAAO,CAAC,CAAC,CAAC;QACboB,GAAG,GAAGjD,EAAE,CAAC,CAAC,CAAC;QAEX,IAAIA,EAAE,CAAC,CAAC,CAAC,IAAIvF,IAAI,GAAG,CAAC,EAAE,EAAEwI,GAAG;QAE5B,GAAG;UACDpD,CAAC,GAAG,CAAC;;UAEL;UACAzE,GAAG,GAAGgH,OAAO,CAACpC,EAAE,EAAE4C,GAAG,EAAEM,EAAE,EAAEL,IAAI,CAAC;;UAEhC;UACA,IAAIzH,GAAG,GAAG,CAAC,EAAE;YAEX;YACA0H,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC;YACb,IAAIM,EAAE,IAAIL,IAAI,EAAEC,IAAI,GAAGA,IAAI,GAAGrI,IAAI,IAAImI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;;YAElD;YACA/C,CAAC,GAAGiD,IAAI,GAAGG,GAAG,GAAG,CAAC;;YAElB;YACA;YACA;YACA;YACA;YACA;YACA;;YAEA,IAAIpD,CAAC,GAAG,CAAC,EAAE;cACT,IAAIA,CAAC,IAAIpF,IAAI,EAAEoF,CAAC,GAAGpF,IAAI,GAAG,CAAC;;cAE3B;cACAgI,IAAI,GAAGP,eAAe,CAAClC,EAAE,EAAEH,CAAC,CAAC;cAC7B6C,KAAK,GAAGD,IAAI,CAAC7G,MAAM;cACnBiH,IAAI,GAAGD,GAAG,CAAChH,MAAM;;cAEjB;cACAR,GAAG,GAAGgH,OAAO,CAACK,IAAI,EAAEG,GAAG,EAAEF,KAAK,EAAEG,IAAI,CAAC;;cAErC;cACA,IAAIzH,GAAG,IAAI,CAAC,EAAE;gBACZyE,CAAC,EAAE;;gBAEH;gBACAzB,QAAQ,CAACqE,IAAI,EAAES,EAAE,GAAGR,KAAK,GAAGS,EAAE,GAAGnD,EAAE,EAAE0C,KAAK,CAAC;cAC7C;YACF,CAAC,MAAM;cAEL;cACA;cACA;cACA,IAAI7C,CAAC,IAAI,CAAC,EAAEzE,GAAG,GAAGyE,CAAC,GAAG,CAAC;cACvB4C,IAAI,GAAGzC,EAAE,CAACR,KAAK,CAAC,CAAC;YACnB;YAEAkD,KAAK,GAAGD,IAAI,CAAC7G,MAAM;YACnB,IAAI8G,KAAK,GAAGG,IAAI,EAAEJ,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC;;YAEjC;YACAzD,QAAQ,CAACwE,GAAG,EAAEH,IAAI,EAAEI,IAAI,CAAC;;YAEzB;YACA,IAAIzH,GAAG,IAAI,CAAC,CAAC,EAAE;cACbyH,IAAI,GAAGD,GAAG,CAAChH,MAAM;;cAEjB;cACAR,GAAG,GAAGgH,OAAO,CAACpC,EAAE,EAAE4C,GAAG,EAAEM,EAAE,EAAEL,IAAI,CAAC;;cAEhC;cACA,IAAIzH,GAAG,GAAG,CAAC,EAAE;gBACXyE,CAAC,EAAE;;gBAEH;gBACAzB,QAAQ,CAACwE,GAAG,EAAEM,EAAE,GAAGL,IAAI,GAAGM,EAAE,GAAGnD,EAAE,EAAE6C,IAAI,CAAC;cAC1C;YACF;YAEAA,IAAI,GAAGD,GAAG,CAAChH,MAAM;UACnB,CAAC,MAAM,IAAIR,GAAG,KAAK,CAAC,EAAE;YACpByE,CAAC,EAAE;YACH+C,GAAG,GAAG,CAAC,CAAC,CAAC;UACX,CAAC,CAAI;;UAEL;UACAD,EAAE,CAACrH,CAAC,EAAE,CAAC,GAAGuE,CAAC;;UAEX;UACA,IAAIzE,GAAG,IAAIwH,GAAG,CAAC,CAAC,CAAC,EAAE;YACjBA,GAAG,CAACC,IAAI,EAAE,CAAC,GAAG9C,EAAE,CAACgD,EAAE,CAAC,IAAI,CAAC;UAC3B,CAAC,MAAM;YACLH,GAAG,GAAG,CAAC7C,EAAE,CAACgD,EAAE,CAAC,CAAC;YACdF,IAAI,GAAG,CAAC;UACV;QAEF,CAAC,QAAQ,CAACE,EAAE,EAAE,GAAGC,EAAE,IAAIJ,GAAG,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK5D,EAAE,EAAE;MACnD;;MAEA;MACA,IAAI,CAAC2D,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAACxC,KAAK,CAAC,CAAC;MAEtB3B,CAAC,CAAC9C,CAAC,GAAGA,CAAC;MAEP,OAAOY,KAAK,CAACkC,CAAC,EAAE1C,EAAE,GAAGgC,EAAE,GAAGpB,iBAAiB,CAAC8B,CAAC,CAAC,GAAG,CAAC,GAAGV,EAAE,CAAC;IAC1D,CAAC;EACH,CAAC,CAAE,CAAC;;EAGJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASa,GAAGA,CAAC3D,CAAC,EAAEgE,EAAE,EAAE;IAClB,IAAIoE,WAAW;MAAEpC,KAAK;MAAE1G,GAAG;MAAE+I,GAAG;MAAEhE,CAAC;MAAEtB,GAAG;MACtCzC,CAAC,GAAG,CAAC;MACLuE,CAAC,GAAG,CAAC;MACLxD,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;IAErB,IAAIiD,iBAAiB,CAAC1B,CAAC,CAAC,GAAG,EAAE,EAAE,MAAMgD,KAAK,CAAC/D,kBAAkB,GAAGyC,iBAAiB,CAAC1B,CAAC,CAAC,CAAC;;IAErF;IACA,IAAI,CAACA,CAAC,CAACE,CAAC,EAAE,OAAO,IAAImB,IAAI,CAAC7B,GAAG,CAAC;IAE9B,IAAIwE,EAAE,IAAI,IAAI,EAAE;MACdlF,QAAQ,GAAG,KAAK;MAChBiE,GAAG,GAAGD,EAAE;IACV,CAAC,MAAM;MACLC,GAAG,GAAGiB,EAAE;IACV;IAEAK,CAAC,GAAG,IAAIhD,IAAI,CAAC,OAAO,CAAC;IAErB,OAAOrB,CAAC,CAACD,GAAG,CAAC,CAAC,CAAC+B,GAAG,CAAC,GAAG,CAAC,EAAE;MACvB9B,CAAC,GAAGA,CAAC,CAACyD,KAAK,CAACY,CAAC,CAAC,CAAC,CAAI;MACnBQ,CAAC,IAAI,CAAC;IACR;;IAEA;IACAmB,KAAK,GAAG7G,IAAI,CAACwD,GAAG,CAACtD,OAAO,CAAC,CAAC,EAAEwF,CAAC,CAAC,CAAC,GAAG1F,IAAI,CAACN,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACvDkE,GAAG,IAAIiD,KAAK;IACZoC,WAAW,GAAG9I,GAAG,GAAG+I,GAAG,GAAG,IAAIhH,IAAI,CAAC7B,GAAG,CAAC;IACvC6B,IAAI,CAAC5C,SAAS,GAAGsE,GAAG;IAEpB,SAAS;MACPzD,GAAG,GAAGgC,KAAK,CAAChC,GAAG,CAACmE,KAAK,CAACzD,CAAC,CAAC,EAAE+C,GAAG,CAAC;MAC9BqF,WAAW,GAAGA,WAAW,CAAC3E,KAAK,CAAC,EAAEnD,CAAC,CAAC;MACpC+D,CAAC,GAAGgE,GAAG,CAACtE,IAAI,CAAC7C,MAAM,CAAC5B,GAAG,EAAE8I,WAAW,EAAErF,GAAG,CAAC,CAAC;MAE3C,IAAIuB,cAAc,CAACD,CAAC,CAAC1D,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,KAAKuB,cAAc,CAAC+D,GAAG,CAAC1H,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,EAAE;QAC7E,OAAO8B,CAAC,EAAE,EAAEwD,GAAG,GAAG/G,KAAK,CAAC+G,GAAG,CAAC5E,KAAK,CAAC4E,GAAG,CAAC,EAAEtF,GAAG,CAAC;QAC5C1B,IAAI,CAAC5C,SAAS,GAAGqE,EAAE;QACnB,OAAOkB,EAAE,IAAI,IAAI,IAAIlF,QAAQ,GAAG,IAAI,EAAEwC,KAAK,CAAC+G,GAAG,EAAEvF,EAAE,CAAC,IAAIuF,GAAG;MAC7D;MAEAA,GAAG,GAAGhE,CAAC;IACT;EACF;;EAGA;EACA,SAAS3C,iBAAiBA,CAAC1B,CAAC,EAAE;IAC5B,IAAIU,CAAC,GAAGV,CAAC,CAACU,CAAC,GAAGhB,QAAQ;MACpBqB,CAAC,GAAGf,CAAC,CAACW,CAAC,CAAC,CAAC,CAAC;;IAEZ;IACA,OAAOI,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAEL,CAAC,EAAE;IAC5B,OAAOA,CAAC;EACV;EAGA,SAAS4H,OAAOA,CAACjH,IAAI,EAAE2C,EAAE,EAAElB,EAAE,EAAE;IAE7B,IAAIkB,EAAE,GAAG3C,IAAI,CAACxC,IAAI,CAACmF,EAAE,CAAC,CAAC,EAAE;MAGvB;MACAlF,QAAQ,GAAG,IAAI;MACf,IAAIgE,EAAE,EAAEzB,IAAI,CAAC5C,SAAS,GAAGqE,EAAE;MAC3B,MAAME,KAAK,CAACjE,YAAY,GAAG,+BAA+B,CAAC;IAC7D;IAEA,OAAOuC,KAAK,CAAC,IAAID,IAAI,CAACA,IAAI,CAACxC,IAAI,CAAC,EAAEmF,EAAE,CAAC;EACvC;EAGA,SAASiD,aAAaA,CAACpC,CAAC,EAAE;IACxB,IAAI0D,EAAE,GAAG,EAAE;IACX,OAAO1D,CAAC,EAAE,GAAG0D,EAAE,IAAI,GAAG;IACtB,OAAOA,EAAE;EACX;;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAAStF,EAAEA,CAAC5C,CAAC,EAAE2D,EAAE,EAAE;IACjB,IAAIwE,CAAC;MAAEC,EAAE;MAAEL,WAAW;MAAE1H,CAAC;MAAEgI,SAAS;MAAEL,GAAG;MAAEhE,CAAC;MAAEtB,GAAG;MAAE4F,EAAE;MACnDvE,CAAC,GAAG,CAAC;MACL4B,KAAK,GAAG,EAAE;MACVhG,CAAC,GAAGK,CAAC;MACL0E,EAAE,GAAG/E,CAAC,CAACW,CAAC;MACRU,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;;IAErB;IACA;IACA,IAAIuB,CAAC,CAACE,CAAC,GAAG,CAAC,EAAE,MAAM8C,KAAK,CAACjE,YAAY,IAAIiB,CAAC,CAACE,CAAC,GAAG,KAAK,GAAG,WAAW,CAAC,CAAC;;IAEpE;IACA,IAAIF,CAAC,CAACwB,EAAE,CAAChC,GAAG,CAAC,EAAE,OAAO,IAAI6B,IAAI,CAAC,CAAC,CAAC;IAEjC,IAAI2C,EAAE,IAAI,IAAI,EAAE;MACdlF,QAAQ,GAAG,KAAK;MAChBiE,GAAG,GAAGD,EAAE;IACV,CAAC,MAAM;MACLC,GAAG,GAAGiB,EAAE;IACV;IAEA,IAAIhE,CAAC,CAACwB,EAAE,CAAC,EAAE,CAAC,EAAE;MACZ,IAAIwC,EAAE,IAAI,IAAI,EAAElF,QAAQ,GAAG,IAAI;MAC/B,OAAOwJ,OAAO,CAACjH,IAAI,EAAE0B,GAAG,CAAC;IAC3B;IAEAA,GAAG,IAAIiD,KAAK;IACZ3E,IAAI,CAAC5C,SAAS,GAAGsE,GAAG;IACpByF,CAAC,GAAGlE,cAAc,CAACS,EAAE,CAAC;IACtB0D,EAAE,GAAGD,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC;IAChBlI,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;IAExB,IAAIb,IAAI,CAACY,GAAG,CAACW,CAAC,CAAC,GAAG,MAAM,EAAE;MAExB;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA,OAAO+H,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC,IAAID,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;QACtD5I,CAAC,GAAGA,CAAC,CAACyD,KAAK,CAACpD,CAAC,CAAC;QACdmI,CAAC,GAAGlE,cAAc,CAACtE,CAAC,CAACW,CAAC,CAAC;QACvB8H,EAAE,GAAGD,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC;QAChBxE,CAAC,EAAE;MACL;MAEA1D,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;MAExB,IAAIyI,EAAE,GAAG,CAAC,EAAE;QACVzI,CAAC,GAAG,IAAIqB,IAAI,CAAC,IAAI,GAAGmH,CAAC,CAAC;QACtB9H,CAAC,EAAE;MACL,CAAC,MAAM;QACLV,CAAC,GAAG,IAAIqB,IAAI,CAACoH,EAAE,GAAG,GAAG,GAAGD,CAAC,CAAChE,KAAK,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,MAAM;MAEL;MACA;MACA;MACAH,CAAC,GAAGiE,OAAO,CAACjH,IAAI,EAAE0B,GAAG,GAAG,CAAC,EAAED,EAAE,CAAC,CAACW,KAAK,CAAC/C,CAAC,GAAG,EAAE,CAAC;MAC5CV,CAAC,GAAGiD,EAAE,CAAC,IAAI5B,IAAI,CAACoH,EAAE,GAAG,GAAG,GAAGD,CAAC,CAAChE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAEzB,GAAG,GAAGiD,KAAK,CAAC,CAACjC,IAAI,CAACM,CAAC,CAAC;MAE5DhD,IAAI,CAAC5C,SAAS,GAAGqE,EAAE;MACnB,OAAOkB,EAAE,IAAI,IAAI,IAAIlF,QAAQ,GAAG,IAAI,EAAEwC,KAAK,CAACtB,CAAC,EAAE8C,EAAE,CAAC,IAAI9C,CAAC;IACzD;;IAEA;;IAEA;IACA;IACA;IACAqI,GAAG,GAAGK,SAAS,GAAG1I,CAAC,GAAGkB,MAAM,CAAClB,CAAC,CAACkD,KAAK,CAAC1D,GAAG,CAAC,EAAEQ,CAAC,CAAC+D,IAAI,CAACvE,GAAG,CAAC,EAAEuD,GAAG,CAAC;IAC5D4F,EAAE,GAAGrH,KAAK,CAACtB,CAAC,CAACyD,KAAK,CAACzD,CAAC,CAAC,EAAE+C,GAAG,CAAC;IAC3BqF,WAAW,GAAG,CAAC;IAEf,SAAS;MACPM,SAAS,GAAGpH,KAAK,CAACoH,SAAS,CAACjF,KAAK,CAACkF,EAAE,CAAC,EAAE5F,GAAG,CAAC;MAC3CsB,CAAC,GAAGgE,GAAG,CAACtE,IAAI,CAAC7C,MAAM,CAACwH,SAAS,EAAE,IAAIrH,IAAI,CAAC+G,WAAW,CAAC,EAAErF,GAAG,CAAC,CAAC;MAE3D,IAAIuB,cAAc,CAACD,CAAC,CAAC1D,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,KAAKuB,cAAc,CAAC+D,GAAG,CAAC1H,CAAC,CAAC,CAAC6D,KAAK,CAAC,CAAC,EAAEzB,GAAG,CAAC,EAAE;QAC7EsF,GAAG,GAAGA,GAAG,CAAC5E,KAAK,CAAC,CAAC,CAAC;;QAElB;QACA,IAAI/C,CAAC,KAAK,CAAC,EAAE2H,GAAG,GAAGA,GAAG,CAACtE,IAAI,CAACuE,OAAO,CAACjH,IAAI,EAAE0B,GAAG,GAAG,CAAC,EAAED,EAAE,CAAC,CAACW,KAAK,CAAC/C,CAAC,GAAG,EAAE,CAAC,CAAC;QACrE2H,GAAG,GAAGnH,MAAM,CAACmH,GAAG,EAAE,IAAIhH,IAAI,CAAC+C,CAAC,CAAC,EAAErB,GAAG,CAAC;QAEnC1B,IAAI,CAAC5C,SAAS,GAAGqE,EAAE;QACnB,OAAOkB,EAAE,IAAI,IAAI,IAAIlF,QAAQ,GAAG,IAAI,EAAEwC,KAAK,CAAC+G,GAAG,EAAEvF,EAAE,CAAC,IAAIuF,GAAG;MAC7D;MAEAA,GAAG,GAAGhE,CAAC;MACP+D,WAAW,IAAI,CAAC;IAClB;EACF;;EAGA;AACF;AACA;EACE,SAASS,YAAYA,CAAC7I,CAAC,EAAEwF,GAAG,EAAE;IAC5B,IAAI9E,CAAC,EAAEJ,CAAC,EAAEqG,GAAG;;IAEb;IACA,IAAI,CAACjG,CAAC,GAAG8E,GAAG,CAACf,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAEe,GAAG,GAAGA,GAAG,CAACsD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;;IAE3D;IACA,IAAI,CAACxI,CAAC,GAAGkF,GAAG,CAACuD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;MAE9B;MACA,IAAIrI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC;MAChBI,CAAC,IAAI,CAAC8E,GAAG,CAAChB,KAAK,CAAClE,CAAC,GAAG,CAAC,CAAC;MACtBkF,GAAG,GAAGA,GAAG,CAACwD,SAAS,CAAC,CAAC,EAAE1I,CAAC,CAAC;IAC3B,CAAC,MAAM,IAAII,CAAC,GAAG,CAAC,EAAE;MAEhB;MACAA,CAAC,GAAG8E,GAAG,CAAC5E,MAAM;IAChB;;IAEA;IACA,KAAKN,CAAC,GAAG,CAAC,EAAEkF,GAAG,CAACyD,UAAU,CAAC3I,CAAC,CAAC,KAAK,EAAE,GAAG,EAAEA,CAAC;;IAE1C;IACA,KAAKqG,GAAG,GAAGnB,GAAG,CAAC5E,MAAM,EAAE4E,GAAG,CAACyD,UAAU,CAACtC,GAAG,GAAG,CAAC,CAAC,KAAK,EAAE,GAAG,EAAEA,GAAG;IAC7DnB,GAAG,GAAGA,GAAG,CAAChB,KAAK,CAAClE,CAAC,EAAEqG,GAAG,CAAC;IAEvB,IAAInB,GAAG,EAAE;MACPmB,GAAG,IAAIrG,CAAC;MACRI,CAAC,GAAGA,CAAC,GAAGJ,CAAC,GAAG,CAAC;MACbN,CAAC,CAACU,CAAC,GAAGxB,SAAS,CAACwB,CAAC,GAAGhB,QAAQ,CAAC;MAC7BM,CAAC,CAACW,CAAC,GAAG,EAAE;;MAER;;MAEA;MACA;MACAL,CAAC,GAAG,CAACI,CAAC,GAAG,CAAC,IAAIhB,QAAQ;MACtB,IAAIgB,CAAC,GAAG,CAAC,EAAEJ,CAAC,IAAIZ,QAAQ;MAExB,IAAIY,CAAC,GAAGqG,GAAG,EAAE;QACX,IAAIrG,CAAC,EAAEN,CAAC,CAACW,CAAC,CAACsE,IAAI,CAAC,CAACO,GAAG,CAAChB,KAAK,CAAC,CAAC,EAAElE,CAAC,CAAC,CAAC;QACjC,KAAKqG,GAAG,IAAIjH,QAAQ,EAAEY,CAAC,GAAGqG,GAAG,GAAG3G,CAAC,CAACW,CAAC,CAACsE,IAAI,CAAC,CAACO,GAAG,CAAChB,KAAK,CAAClE,CAAC,EAAEA,CAAC,IAAIZ,QAAQ,CAAC,CAAC;QACtE8F,GAAG,GAAGA,GAAG,CAAChB,KAAK,CAAClE,CAAC,CAAC;QAClBA,CAAC,GAAGZ,QAAQ,GAAG8F,GAAG,CAAC5E,MAAM;MAC3B,CAAC,MAAM;QACLN,CAAC,IAAIqG,GAAG;MACV;MAEA,OAAOrG,CAAC,EAAE,GAAGkF,GAAG,IAAI,GAAG;MACvBxF,CAAC,CAACW,CAAC,CAACsE,IAAI,CAAC,CAACO,GAAG,CAAC;MAEd,IAAI1G,QAAQ,KAAKkB,CAAC,CAACU,CAAC,GAAGd,KAAK,IAAII,CAAC,CAACU,CAAC,GAAG,CAACd,KAAK,CAAC,EAAE,MAAMoD,KAAK,CAAC/D,kBAAkB,GAAGyB,CAAC,CAAC;IACpF,CAAC,MAAM;MAEL;MACAV,CAAC,CAACE,CAAC,GAAG,CAAC;MACPF,CAAC,CAACU,CAAC,GAAG,CAAC;MACPV,CAAC,CAACW,CAAC,GAAG,CAAC,CAAC,CAAC;IACX;IAEA,OAAOX,CAAC;EACV;;EAGA;AACF;AACA;EACG,SAASsB,KAAKA,CAACtB,CAAC,EAAEgE,EAAE,EAAEsB,EAAE,EAAE;IACzB,IAAIhF,CAAC;MAAEC,CAAC;MAAEsE,CAAC;MAAET,CAAC;MAAE8E,EAAE;MAAEC,OAAO;MAAEpI,CAAC;MAAEqI,GAAG;MACjCrE,EAAE,GAAG/E,CAAC,CAACW,CAAC;;IAEV;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,KAAKyD,CAAC,GAAG,CAAC,EAAES,CAAC,GAAGE,EAAE,CAAC,CAAC,CAAC,EAAEF,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAET,CAAC,EAAE;IAC5C9D,CAAC,GAAG0D,EAAE,GAAGI,CAAC;;IAEV;IACA,IAAI9D,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,IAAIZ,QAAQ;MACba,CAAC,GAAGyD,EAAE;MACNjD,CAAC,GAAGgE,EAAE,CAACqE,GAAG,GAAG,CAAC,CAAC;IACjB,CAAC,MAAM;MACLA,GAAG,GAAGjK,IAAI,CAAC+G,IAAI,CAAC,CAAC5F,CAAC,GAAG,CAAC,IAAIZ,QAAQ,CAAC;MACnCmF,CAAC,GAAGE,EAAE,CAACnE,MAAM;MACb,IAAIwI,GAAG,IAAIvE,CAAC,EAAE,OAAO7E,CAAC;MACtBe,CAAC,GAAG8D,CAAC,GAAGE,EAAE,CAACqE,GAAG,CAAC;;MAEf;MACA,KAAKhF,CAAC,GAAG,CAAC,EAAES,CAAC,IAAI,EAAE,EAAEA,CAAC,IAAI,EAAE,EAAET,CAAC,EAAE;;MAEjC;MACA9D,CAAC,IAAIZ,QAAQ;;MAEb;MACA;MACAa,CAAC,GAAGD,CAAC,GAAGZ,QAAQ,GAAG0E,CAAC;IACtB;IAEA,IAAIkB,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBT,CAAC,GAAGxF,OAAO,CAAC,EAAE,EAAE+E,CAAC,GAAG7D,CAAC,GAAG,CAAC,CAAC;;MAE1B;MACA2I,EAAE,GAAGnI,CAAC,GAAG8D,CAAC,GAAG,EAAE,GAAG,CAAC;;MAEnB;MACAsE,OAAO,GAAGnF,EAAE,GAAG,CAAC,IAAIe,EAAE,CAACqE,GAAG,GAAG,CAAC,CAAC,KAAK,KAAK,CAAC,IAAIrI,CAAC,GAAG8D,CAAC;;MAEnD;MACA;MACA;;MAEAsE,OAAO,GAAG7D,EAAE,GAAG,CAAC,GACZ,CAAC4D,EAAE,IAAIC,OAAO,MAAM7D,EAAE,IAAI,CAAC,IAAIA,EAAE,KAAKtF,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GACvDgJ,EAAE,GAAG,CAAC,IAAIA,EAAE,IAAI,CAAC,KAAK5D,EAAE,IAAI,CAAC,IAAI6D,OAAO,IAAI7D,EAAE,IAAI,CAAC;MAEnD;MACC,CAAChF,CAAC,GAAG,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGQ,CAAC,GAAG1B,OAAO,CAAC,EAAE,EAAE+E,CAAC,GAAG7D,CAAC,CAAC,GAAG,CAAC,GAAGwE,EAAE,CAACqE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAI,CAAC,IACnE9D,EAAE,KAAKtF,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAChC;IAEA,IAAI8D,EAAE,GAAG,CAAC,IAAI,CAACe,EAAE,CAAC,CAAC,CAAC,EAAE;MACpB,IAAIoE,OAAO,EAAE;QACXtE,CAAC,GAAGnD,iBAAiB,CAAC1B,CAAC,CAAC;QACxB+E,EAAE,CAACnE,MAAM,GAAG,CAAC;;QAEb;QACAoD,EAAE,GAAGA,EAAE,GAAGa,CAAC,GAAG,CAAC;;QAEf;QACAE,EAAE,CAAC,CAAC,CAAC,GAAG1F,OAAO,CAAC,EAAE,EAAE,CAACK,QAAQ,GAAGsE,EAAE,GAAGtE,QAAQ,IAAIA,QAAQ,CAAC;QAC1DM,CAAC,CAACU,CAAC,GAAGxB,SAAS,CAAC,CAAC8E,EAAE,GAAGtE,QAAQ,CAAC,IAAI,CAAC;MACtC,CAAC,MAAM;QACLqF,EAAE,CAACnE,MAAM,GAAG,CAAC;;QAEb;QACAmE,EAAE,CAAC,CAAC,CAAC,GAAG/E,CAAC,CAACU,CAAC,GAAGV,CAAC,CAACE,CAAC,GAAG,CAAC;MACvB;MAEA,OAAOF,CAAC;IACV;;IAEA;IACA,IAAIM,CAAC,IAAI,CAAC,EAAE;MACVyE,EAAE,CAACnE,MAAM,GAAGwI,GAAG;MACfvE,CAAC,GAAG,CAAC;MACLuE,GAAG,EAAE;IACP,CAAC,MAAM;MACLrE,EAAE,CAACnE,MAAM,GAAGwI,GAAG,GAAG,CAAC;MACnBvE,CAAC,GAAGxF,OAAO,CAAC,EAAE,EAAEK,QAAQ,GAAGY,CAAC,CAAC;;MAE7B;MACA;MACAyE,EAAE,CAACqE,GAAG,CAAC,GAAG7I,CAAC,GAAG,CAAC,GAAG,CAACQ,CAAC,GAAG1B,OAAO,CAAC,EAAE,EAAE+E,CAAC,GAAG7D,CAAC,CAAC,GAAGlB,OAAO,CAAC,EAAE,EAAEkB,CAAC,CAAC,GAAG,CAAC,IAAIsE,CAAC,GAAG,CAAC;IACzE;IAEA,IAAIsE,OAAO,EAAE;MACX,SAAS;QAEP;QACA,IAAIC,GAAG,IAAI,CAAC,EAAE;UACZ,IAAI,CAACrE,EAAE,CAAC,CAAC,CAAC,IAAIF,CAAC,KAAKpF,IAAI,EAAE;YACxBsF,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC;YACT,EAAE/E,CAAC,CAACU,CAAC;UACP;UAEA;QACF,CAAC,MAAM;UACLqE,EAAE,CAACqE,GAAG,CAAC,IAAIvE,CAAC;UACZ,IAAIE,EAAE,CAACqE,GAAG,CAAC,IAAI3J,IAAI,EAAE;UACrBsF,EAAE,CAACqE,GAAG,EAAE,CAAC,GAAG,CAAC;UACbvE,CAAC,GAAG,CAAC;QACP;MACF;IACF;;IAEA;IACA,KAAKvE,CAAC,GAAGyE,EAAE,CAACnE,MAAM,EAAEmE,EAAE,CAAC,EAAEzE,CAAC,CAAC,KAAK,CAAC,GAAGyE,EAAE,CAACG,GAAG,CAAC,CAAC;IAE5C,IAAIpG,QAAQ,KAAKkB,CAAC,CAACU,CAAC,GAAGd,KAAK,IAAII,CAAC,CAACU,CAAC,GAAG,CAACd,KAAK,CAAC,EAAE;MAC7C,MAAMoD,KAAK,CAAC/D,kBAAkB,GAAGyC,iBAAiB,CAAC1B,CAAC,CAAC,CAAC;IACxD;IAEA,OAAOA,CAAC;EACV;EAGA,SAASoD,QAAQA,CAACpD,CAAC,EAAEK,CAAC,EAAE;IACtB,IAAIM,CAAC;MAAED,CAAC;MAAEJ,CAAC;MAAEC,CAAC;MAAEsE,CAAC;MAAE8B,GAAG;MAAE5B,EAAE;MAAEsE,EAAE;MAAEC,IAAI;MAAEtE,EAAE;MACtC3D,IAAI,GAAGrB,CAAC,CAACC,WAAW;MACpB6C,EAAE,GAAGzB,IAAI,CAAC5C,SAAS;;IAErB;IACA;IACA,IAAI,CAACuB,CAAC,CAACE,CAAC,IAAI,CAACG,CAAC,CAACH,CAAC,EAAE;MAChB,IAAIG,CAAC,CAACH,CAAC,EAAEG,CAAC,CAACH,CAAC,GAAG,CAACG,CAAC,CAACH,CAAC,CAAC,KACfG,CAAC,GAAG,IAAIgB,IAAI,CAACrB,CAAC,CAAC;MACpB,OAAOlB,QAAQ,GAAGwC,KAAK,CAACjB,CAAC,EAAEyC,EAAE,CAAC,GAAGzC,CAAC;IACpC;IAEA0E,EAAE,GAAG/E,CAAC,CAACW,CAAC;IACRqE,EAAE,GAAG3E,CAAC,CAACM,CAAC;;IAER;;IAEAD,CAAC,GAAGL,CAAC,CAACK,CAAC;IACP2I,EAAE,GAAGrJ,CAAC,CAACU,CAAC;IACRqE,EAAE,GAAGA,EAAE,CAACP,KAAK,CAAC,CAAC;IACfK,CAAC,GAAGwE,EAAE,GAAG3I,CAAC;;IAEV;IACA,IAAImE,CAAC,EAAE;MACLyE,IAAI,GAAGzE,CAAC,GAAG,CAAC;MAEZ,IAAIyE,IAAI,EAAE;QACR3I,CAAC,GAAGoE,EAAE;QACNF,CAAC,GAAG,CAACA,CAAC;QACN8B,GAAG,GAAG3B,EAAE,CAACpE,MAAM;MACjB,CAAC,MAAM;QACLD,CAAC,GAAGqE,EAAE;QACNtE,CAAC,GAAG2I,EAAE;QACN1C,GAAG,GAAG5B,EAAE,CAACnE,MAAM;MACjB;;MAEA;MACA;MACA;MACAN,CAAC,GAAGnB,IAAI,CAACiH,GAAG,CAACjH,IAAI,CAAC+G,IAAI,CAACpD,EAAE,GAAGpD,QAAQ,CAAC,EAAEiH,GAAG,CAAC,GAAG,CAAC;MAE/C,IAAI9B,CAAC,GAAGvE,CAAC,EAAE;QACTuE,CAAC,GAAGvE,CAAC;QACLK,CAAC,CAACC,MAAM,GAAG,CAAC;MACd;;MAEA;MACAD,CAAC,CAACiG,OAAO,CAAC,CAAC;MACX,KAAKtG,CAAC,GAAGuE,CAAC,EAAEvE,CAAC,EAAE,GAAGK,CAAC,CAACsE,IAAI,CAAC,CAAC,CAAC;MAC3BtE,CAAC,CAACiG,OAAO,CAAC,CAAC;;MAEb;IACA,CAAC,MAAM;MAEL;;MAEAtG,CAAC,GAAGyE,EAAE,CAACnE,MAAM;MACb+F,GAAG,GAAG3B,EAAE,CAACpE,MAAM;MACf0I,IAAI,GAAGhJ,CAAC,GAAGqG,GAAG;MACd,IAAI2C,IAAI,EAAE3C,GAAG,GAAGrG,CAAC;MAEjB,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,GAAG,EAAErG,CAAC,EAAE,EAAE;QACxB,IAAIyE,EAAE,CAACzE,CAAC,CAAC,IAAI0E,EAAE,CAAC1E,CAAC,CAAC,EAAE;UAClBgJ,IAAI,GAAGvE,EAAE,CAACzE,CAAC,CAAC,GAAG0E,EAAE,CAAC1E,CAAC,CAAC;UACpB;QACF;MACF;MAEAuE,CAAC,GAAG,CAAC;IACP;IAEA,IAAIyE,IAAI,EAAE;MACR3I,CAAC,GAAGoE,EAAE;MACNA,EAAE,GAAGC,EAAE;MACPA,EAAE,GAAGrE,CAAC;MACNN,CAAC,CAACH,CAAC,GAAG,CAACG,CAAC,CAACH,CAAC;IACZ;IAEAyG,GAAG,GAAG5B,EAAE,CAACnE,MAAM;;IAEf;IACA;IACA,KAAKN,CAAC,GAAG0E,EAAE,CAACpE,MAAM,GAAG+F,GAAG,EAAErG,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAEyE,EAAE,CAAC4B,GAAG,EAAE,CAAC,GAAG,CAAC;;IAEnD;IACA,KAAKrG,CAAC,GAAG0E,EAAE,CAACpE,MAAM,EAAEN,CAAC,GAAGuE,CAAC,GAAG;MAC1B,IAAIE,EAAE,CAAC,EAAEzE,CAAC,CAAC,GAAG0E,EAAE,CAAC1E,CAAC,CAAC,EAAE;QACnB,KAAKC,CAAC,GAAGD,CAAC,EAAEC,CAAC,IAAIwE,EAAE,CAAC,EAAExE,CAAC,CAAC,KAAK,CAAC,GAAGwE,EAAE,CAACxE,CAAC,CAAC,GAAGd,IAAI,GAAG,CAAC;QACjD,EAAEsF,EAAE,CAACxE,CAAC,CAAC;QACPwE,EAAE,CAACzE,CAAC,CAAC,IAAIb,IAAI;MACf;MAEAsF,EAAE,CAACzE,CAAC,CAAC,IAAI0E,EAAE,CAAC1E,CAAC,CAAC;IAChB;;IAEA;IACA,OAAOyE,EAAE,CAAC,EAAE4B,GAAG,CAAC,KAAK,CAAC,GAAG5B,EAAE,CAACG,GAAG,CAAC,CAAC;;IAEjC;IACA,OAAOH,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,EAAEA,EAAE,CAACI,KAAK,CAAC,CAAC,EAAE,EAAEzE,CAAC;;IAEnC;IACA,IAAI,CAACqE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI1D,IAAI,CAAC,CAAC,CAAC;IAE9BhB,CAAC,CAACM,CAAC,GAAGoE,EAAE;IACR1E,CAAC,CAACK,CAAC,GAAGA,CAAC;;IAEP;IACA,OAAO5B,QAAQ,GAAGwC,KAAK,CAACjB,CAAC,EAAEyC,EAAE,CAAC,GAAGzC,CAAC;EACpC;EAGA,SAASqE,QAAQA,CAAC1E,CAAC,EAAEuJ,KAAK,EAAEvF,EAAE,EAAE;IAC9B,IAAIa,CAAC;MACHnE,CAAC,GAAGgB,iBAAiB,CAAC1B,CAAC,CAAC;MACxBwF,GAAG,GAAGlB,cAAc,CAACtE,CAAC,CAACW,CAAC,CAAC;MACzBgG,GAAG,GAAGnB,GAAG,CAAC5E,MAAM;IAElB,IAAI2I,KAAK,EAAE;MACT,IAAIvF,EAAE,IAAI,CAACa,CAAC,GAAGb,EAAE,GAAG2C,GAAG,IAAI,CAAC,EAAE;QAC5BnB,GAAG,GAAGA,GAAG,CAACoD,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGpD,GAAG,CAAChB,KAAK,CAAC,CAAC,CAAC,GAAGyC,aAAa,CAACpC,CAAC,CAAC;MAC7D,CAAC,MAAM,IAAI8B,GAAG,GAAG,CAAC,EAAE;QAClBnB,GAAG,GAAGA,GAAG,CAACoD,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGpD,GAAG,CAAChB,KAAK,CAAC,CAAC,CAAC;MAC1C;MAEAgB,GAAG,GAAGA,GAAG,IAAI9E,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,GAAGA,CAAC;IACtC,CAAC,MAAM,IAAIA,CAAC,GAAG,CAAC,EAAE;MAChB8E,GAAG,GAAG,IAAI,GAAGyB,aAAa,CAAC,CAACvG,CAAC,GAAG,CAAC,CAAC,GAAG8E,GAAG;MACxC,IAAIxB,EAAE,IAAI,CAACa,CAAC,GAAGb,EAAE,GAAG2C,GAAG,IAAI,CAAC,EAAEnB,GAAG,IAAIyB,aAAa,CAACpC,CAAC,CAAC;IACvD,CAAC,MAAM,IAAInE,CAAC,IAAIiG,GAAG,EAAE;MACnBnB,GAAG,IAAIyB,aAAa,CAACvG,CAAC,GAAG,CAAC,GAAGiG,GAAG,CAAC;MACjC,IAAI3C,EAAE,IAAI,CAACa,CAAC,GAAGb,EAAE,GAAGtD,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE8E,GAAG,GAAGA,GAAG,GAAG,GAAG,GAAGyB,aAAa,CAACpC,CAAC,CAAC;IACpE,CAAC,MAAM;MACL,IAAI,CAACA,CAAC,GAAGnE,CAAC,GAAG,CAAC,IAAIiG,GAAG,EAAEnB,GAAG,GAAGA,GAAG,CAAChB,KAAK,CAAC,CAAC,EAAEK,CAAC,CAAC,GAAG,GAAG,GAAGW,GAAG,CAAChB,KAAK,CAACK,CAAC,CAAC;MACjE,IAAIb,EAAE,IAAI,CAACa,CAAC,GAAGb,EAAE,GAAG2C,GAAG,IAAI,CAAC,EAAE;QAC5B,IAAIjG,CAAC,GAAG,CAAC,KAAKiG,GAAG,EAAEnB,GAAG,IAAI,GAAG;QAC7BA,GAAG,IAAIyB,aAAa,CAACpC,CAAC,CAAC;MACzB;IACF;IAEA,OAAO7E,CAAC,CAACE,CAAC,GAAG,CAAC,GAAG,GAAG,GAAGsF,GAAG,GAAGA,GAAG;EAClC;;EAGA;EACA,SAASW,QAAQA,CAACqD,GAAG,EAAE7C,GAAG,EAAE;IAC1B,IAAI6C,GAAG,CAAC5I,MAAM,GAAG+F,GAAG,EAAE;MACpB6C,GAAG,CAAC5I,MAAM,GAAG+F,GAAG;MAChB,OAAO,IAAI;IACb;EACF;;EAGA;;EAGA;AACF;AACA;AACA;;EAGE;AACF;AACA;AACA;AACA;EACE,SAAS8C,KAAKA,CAACC,GAAG,EAAE;IAClB,IAAIpJ,CAAC,EAAEqJ,CAAC,EAAEC,EAAE;;IAEZ;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASpL,OAAOA,CAACqL,KAAK,EAAE;MACtB,IAAI7J,CAAC,GAAG,IAAI;;MAEZ;MACA,IAAI,EAAEA,CAAC,YAAYxB,OAAO,CAAC,EAAE,OAAO,IAAIA,OAAO,CAACqL,KAAK,CAAC;;MAEtD;MACA;MACA7J,CAAC,CAACC,WAAW,GAAGzB,OAAO;;MAEvB;MACA,IAAIqL,KAAK,YAAYrL,OAAO,EAAE;QAC5BwB,CAAC,CAACE,CAAC,GAAG2J,KAAK,CAAC3J,CAAC;QACbF,CAAC,CAACU,CAAC,GAAGmJ,KAAK,CAACnJ,CAAC;QACbV,CAAC,CAACW,CAAC,GAAG,CAACkJ,KAAK,GAAGA,KAAK,CAAClJ,CAAC,IAAIkJ,KAAK,CAACrF,KAAK,CAAC,CAAC,GAAGqF,KAAK;QAC/C;MACF;MAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAE7B;QACA,IAAIA,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;UACnB,MAAM7G,KAAK,CAAChE,eAAe,GAAG6K,KAAK,CAAC;QACtC;QAEA,IAAIA,KAAK,GAAG,CAAC,EAAE;UACb7J,CAAC,CAACE,CAAC,GAAG,CAAC;QACT,CAAC,MAAM,IAAI2J,KAAK,GAAG,CAAC,EAAE;UACpBA,KAAK,GAAG,CAACA,KAAK;UACd7J,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;QACV,CAAC,MAAM;UACLF,CAAC,CAACE,CAAC,GAAG,CAAC;UACPF,CAAC,CAACU,CAAC,GAAG,CAAC;UACPV,CAAC,CAACW,CAAC,GAAG,CAAC,CAAC,CAAC;UACT;QACF;;QAEA;QACA,IAAIkJ,KAAK,KAAK,CAAC,CAACA,KAAK,IAAIA,KAAK,GAAG,GAAG,EAAE;UACpC7J,CAAC,CAACU,CAAC,GAAG,CAAC;UACPV,CAAC,CAACW,CAAC,GAAG,CAACkJ,KAAK,CAAC;UACb;QACF;QAEA,OAAOhB,YAAY,CAAC7I,CAAC,EAAE6J,KAAK,CAACnF,QAAQ,CAAC,CAAC,CAAC;MAC1C,CAAC,MAAM,IAAI,OAAOmF,KAAK,KAAK,QAAQ,EAAE;QACpC,MAAM7G,KAAK,CAAChE,eAAe,GAAG6K,KAAK,CAAC;MACtC;;MAEA;MACA,IAAIA,KAAK,CAACZ,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;QAC9BY,KAAK,GAAGA,KAAK,CAACrF,KAAK,CAAC,CAAC,CAAC;QACtBxE,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC;MACV,CAAC,MAAM;QACLF,CAAC,CAACE,CAAC,GAAG,CAAC;MACT;MAEA,IAAIX,SAAS,CAACuK,IAAI,CAACD,KAAK,CAAC,EAAEhB,YAAY,CAAC7I,CAAC,EAAE6J,KAAK,CAAC,CAAC,KAC7C,MAAM7G,KAAK,CAAChE,eAAe,GAAG6K,KAAK,CAAC;IAC3C;IAEArL,OAAO,CAACuL,SAAS,GAAGlK,CAAC;IAErBrB,OAAO,CAACwL,QAAQ,GAAG,CAAC;IACpBxL,OAAO,CAACyL,UAAU,GAAG,CAAC;IACtBzL,OAAO,CAAC0L,UAAU,GAAG,CAAC;IACtB1L,OAAO,CAAC2L,WAAW,GAAG,CAAC;IACvB3L,OAAO,CAAC4L,aAAa,GAAG,CAAC;IACzB5L,OAAO,CAAC6L,eAAe,GAAG,CAAC;IAC3B7L,OAAO,CAAC8L,eAAe,GAAG,CAAC;IAC3B9L,OAAO,CAAC+L,eAAe,GAAG,CAAC;IAC3B/L,OAAO,CAACgM,gBAAgB,GAAG,CAAC;IAE5BhM,OAAO,CAACiL,KAAK,GAAGA,KAAK;IACrBjL,OAAO,CAACiM,MAAM,GAAGjM,OAAO,CAACkM,GAAG,GAAGD,MAAM;IAErC,IAAIf,GAAG,KAAK,KAAK,CAAC,EAAEA,GAAG,GAAG,CAAC,CAAC;IAC5B,IAAIA,GAAG,EAAE;MACPE,EAAE,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;MAC9D,KAAKtJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,EAAE,CAAChJ,MAAM,GAAG,IAAI,CAAC8I,GAAG,CAACiB,cAAc,CAAChB,CAAC,GAAGC,EAAE,CAACtJ,CAAC,EAAE,CAAC,CAAC,EAAEoJ,GAAG,CAACC,CAAC,CAAC,GAAG,IAAI,CAACA,CAAC,CAAC;IACpF;IAEAnL,OAAO,CAACiM,MAAM,CAACf,GAAG,CAAC;IAEnB,OAAOlL,OAAO;EAChB;;EAGA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASiM,MAAMA,CAACf,GAAG,EAAE;IACnB,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MACnC,MAAM1G,KAAK,CAACjE,YAAY,GAAG,iBAAiB,CAAC;IAC/C;IACA,IAAIuB,CAAC;MAAEqJ,CAAC;MAAEiB,CAAC;MACThB,EAAE,GAAG,CACH,WAAW,EAAE,CAAC,EAAErL,UAAU,EAC1B,UAAU,EAAE,CAAC,EAAE,CAAC,EAChB,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EACrB,UAAU,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CACrB;IAEH,KAAK+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,EAAE,CAAChJ,MAAM,EAAEN,CAAC,IAAI,CAAC,EAAE;MACjC,IAAI,CAACsK,CAAC,GAAGlB,GAAG,CAACC,CAAC,GAAGC,EAAE,CAACtJ,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;QACnC,IAAIpB,SAAS,CAAC0L,CAAC,CAAC,KAAKA,CAAC,IAAIA,CAAC,IAAIhB,EAAE,CAACtJ,CAAC,GAAG,CAAC,CAAC,IAAIsK,CAAC,IAAIhB,EAAE,CAACtJ,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACqJ,CAAC,CAAC,GAAGiB,CAAC,CAAC,KACnE,MAAM5H,KAAK,CAAChE,eAAe,GAAG2K,CAAC,GAAG,IAAI,GAAGiB,CAAC,CAAC;MAClD;IACF;IAEA,IAAI,CAACA,CAAC,GAAGlB,GAAG,CAACC,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;MAClC,IAAIiB,CAAC,IAAIzL,IAAI,CAACN,IAAI,EAAE,IAAI,CAAC8K,CAAC,CAAC,GAAG,IAAI,IAAI,CAACiB,CAAC,CAAC,CAAC,KACrC,MAAM5H,KAAK,CAAChE,eAAe,GAAG2K,CAAC,GAAG,IAAI,GAAGiB,CAAC,CAAC;IACpD;IAEA,OAAO,IAAI;EACb;;EAGA;EACApM,OAAO,GAAGiL,KAAK,CAACjL,OAAO,CAAC;EAExBA,OAAO,CAAC,SAAS,CAAC,GAAGA,OAAO,CAACA,OAAO,GAAGA,OAAO;;EAE9C;EACAgB,GAAG,GAAG,IAAIhB,OAAO,CAAC,CAAC,CAAC;;EAGpB;;EAGA;EACA,IAAI,OAAOqM,MAAM,IAAI,UAAU,IAAIA,MAAM,CAACC,GAAG,EAAE;IAC7CD,MAAM,CAAC,YAAY;MACjB,OAAOrM,OAAO;IAChB,CAAC,CAAC;;IAEJ;EACA,CAAC,MAAM,IAAI,OAAOuM,MAAM,IAAI,WAAW,IAAIA,MAAM,CAACC,OAAO,EAAE;IACzDD,MAAM,CAACC,OAAO,GAAGxM,OAAO;;IAExB;EACF,CAAC,MAAM;IACL,IAAI,CAACF,WAAW,EAAE;MAChBA,WAAW,GAAG,OAAO2M,IAAI,IAAI,WAAW,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,IAAIA,IAAI,GACjEA,IAAI,GAAGC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;IACtC;IAEA5M,WAAW,CAACE,OAAO,GAAGA,OAAO;EAC/B;AACF,CAAC,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}