{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bot_fake_discord\\\\dashboard\\\\src\\\\components\\\\Servers\\\\Servers.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Box, Grid, Card, CardContent, Typography, Button, Avatar, LinearProgress, Dialog, DialogTitle, DialogContent, DialogActions, TextField } from '@mui/material';\nimport { Add as AddIcon, Storage as ServerIcon } from '@mui/icons-material';\nimport { serversAPI, handleApiError } from '../../services/api';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Servers = () => {\n  _s();\n  const navigate = useNavigate();\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    loadServers();\n  }, []);\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const response = await serversAPI.getAll();\n      setServers(response.data.servers);\n    } catch (error) {\n      handleApiError(error, 'Failed to load servers');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"Servers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 4,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        fontWeight: \"bold\",\n        children: \"Servers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 22\n        }, this),\n        sx: {\n          background: 'linear-gradient(45deg, #5865f2 30%, #57f287 90%)'\n        },\n        children: \"Create Server\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), servers.length === 0 ? /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          textAlign: 'center',\n          py: 8\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'primary.main',\n            width: 80,\n            height: 80,\n            mx: 'auto',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(ServerIcon, {\n            sx: {\n              fontSize: 40\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"No Servers Yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          mb: 3,\n          children: \"Create your first server to deploy bots.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 26\n          }, this),\n          size: \"large\",\n          children: \"Create Your First Server\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: servers.map(server => {\n        var _server$channels;\n        return /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"hover-card\",\n            onClick: () => navigate(`/servers/${server.id}`),\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: server.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: server.description || 'No description'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                mt: 2,\n                display: \"block\",\n                children: [\"Channels: \", ((_server$channels = server.channels) === null || _server$channels === void 0 ? void 0 : _server$channels.length) || 0]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)\n        }, server.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Servers, \"OfZJk+5GsNiuzH11ZASQ9vlW9xA=\", false, function () {\n  return [useNavigate];\n});\n_c = Servers;\nexport default Servers;\nvar _c;\n$RefreshReg$(_c, \"Servers\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Box", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Avatar", "LinearProgress", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Add", "AddIcon", "Storage", "ServerIcon", "serversAPI", "handleApiError", "toast", "jsxDEV", "_jsxDEV", "Servers", "_s", "navigate", "servers", "setServers", "loading", "setLoading", "loadServers", "response", "getAll", "data", "error", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "display", "justifyContent", "alignItems", "mb", "fontWeight", "startIcon", "sx", "background", "length", "textAlign", "py", "bgcolor", "width", "height", "mx", "fontSize", "color", "size", "container", "spacing", "map", "server", "_server$channels", "item", "xs", "sm", "md", "onClick", "id", "name", "description", "mt", "channels", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/components/Servers/Servers.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Box,\n  Grid,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  Avatar,\n  LinearProgress,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n} from '@mui/material';\nimport {\n  Add as AddIcon,\n  Storage as ServerIcon,\n} from '@mui/icons-material';\nimport { serversAPI, handleApiError } from '../../services/api';\nimport toast from 'react-hot-toast';\n\nconst Servers = () => {\n  const navigate = useNavigate();\n  const [servers, setServers] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    loadServers();\n  }, []);\n\n  const loadServers = async () => {\n    try {\n      setLoading(true);\n      const response = await serversAPI.getAll();\n      setServers(response.data.servers);\n    } catch (error) {\n      handleApiError(error, 'Failed to load servers');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <Box>\n        <Typography variant=\"h4\" gutterBottom>\n          Servers\n        </Typography>\n        <LinearProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Box className=\"fade-in\">\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={4}>\n        <Typography variant=\"h4\" fontWeight=\"bold\">\n          Servers\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<AddIcon />}\n          sx={{\n            background: 'linear-gradient(45deg, #5865f2 30%, #57f287 90%)',\n          }}\n        >\n          Create Server\n        </Button>\n      </Box>\n\n      {servers.length === 0 ? (\n        <Card>\n          <CardContent sx={{ textAlign: 'center', py: 8 }}>\n            <Avatar\n              sx={{\n                bgcolor: 'primary.main',\n                width: 80,\n                height: 80,\n                mx: 'auto',\n                mb: 2,\n              }}\n            >\n              <ServerIcon sx={{ fontSize: 40 }} />\n            </Avatar>\n            <Typography variant=\"h5\" gutterBottom>\n              No Servers Yet\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" mb={3}>\n              Create your first server to deploy bots.\n            </Typography>\n            <Button\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              size=\"large\"\n            >\n              Create Your First Server\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <Grid container spacing={3}>\n          {servers.map((server) => (\n            <Grid item xs={12} sm={6} md={4} key={server.id}>\n              <Card className=\"hover-card\" onClick={() => navigate(`/servers/${server.id}`)}>\n                <CardContent>\n                  <Typography variant=\"h6\" gutterBottom>\n                    {server.name}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {server.description || 'No description'}\n                  </Typography>\n                  <Typography variant=\"caption\" color=\"text.secondary\" mt={2} display=\"block\">\n                    Channels: {server.channels?.length || 0}\n                  </Typography>\n                </CardContent>\n              </Card>\n            </Grid>\n          ))}\n        </Grid>\n      )}\n    </Box>\n  );\n};\n\nexport default Servers;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,GAAG,EACHC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,cAAc,EACdC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,QACJ,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,OAAO,IAAIC,UAAU,QAChB,qBAAqB;AAC5B,SAASC,UAAU,EAAEC,cAAc,QAAQ,oBAAoB;AAC/D,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd+B,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,QAAQ,GAAG,MAAMb,UAAU,CAACc,MAAM,CAAC,CAAC;MAC1CL,UAAU,CAACI,QAAQ,CAACE,IAAI,CAACP,OAAO,CAAC;IACnC,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdf,cAAc,CAACe,KAAK,EAAE,wBAAwB,CAAC;IACjD,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEN,OAAA,CAACrB,GAAG;MAAAkC,QAAA,gBACFb,OAAA,CAACjB,UAAU;QAAC+B,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAEtC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnB,OAAA,CAACd,cAAc;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,oBACEnB,OAAA,CAACrB,GAAG;IAACyC,SAAS,EAAC,SAAS;IAAAP,QAAA,gBACtBb,OAAA,CAACrB,GAAG;MAAC0C,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAX,QAAA,gBAC3Eb,OAAA,CAACjB,UAAU;QAAC+B,OAAO,EAAC,IAAI;QAACW,UAAU,EAAC,MAAM;QAAAZ,QAAA,EAAC;MAE3C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,MAAM;QACL8B,OAAO,EAAC,WAAW;QACnBY,SAAS,eAAE1B,OAAA,CAACP,OAAO;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBQ,EAAE,EAAE;UACFC,UAAU,EAAE;QACd,CAAE;QAAAf,QAAA,EACH;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELf,OAAO,CAACyB,MAAM,KAAK,CAAC,gBACnB7B,OAAA,CAACnB,IAAI;MAAAgC,QAAA,eACHb,OAAA,CAAClB,WAAW;QAAC6C,EAAE,EAAE;UAAEG,SAAS,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBAC9Cb,OAAA,CAACf,MAAM;UACL0C,EAAE,EAAE;YACFK,OAAO,EAAE,cAAc;YACvBC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVC,EAAE,EAAE,MAAM;YACVX,EAAE,EAAE;UACN,CAAE;UAAAX,QAAA,eAEFb,OAAA,CAACL,UAAU;YAACgC,EAAE,EAAE;cAAES,QAAQ,EAAE;YAAG;UAAE;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACTnB,OAAA,CAACjB,UAAU;UAAC+B,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,EAAC;QAEtC;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAACjB,UAAU;UAAC+B,OAAO,EAAC,OAAO;UAACuB,KAAK,EAAC,gBAAgB;UAACb,EAAE,EAAE,CAAE;UAAAX,QAAA,EAAC;QAE1D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbnB,OAAA,CAAChB,MAAM;UACL8B,OAAO,EAAC,WAAW;UACnBY,SAAS,eAAE1B,OAAA,CAACP,OAAO;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBmB,IAAI,EAAC,OAAO;UAAAzB,QAAA,EACb;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEPnB,OAAA,CAACpB,IAAI;MAAC2D,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA3B,QAAA,EACxBT,OAAO,CAACqC,GAAG,CAAEC,MAAM;QAAA,IAAAC,gBAAA;QAAA,oBAClB3C,OAAA,CAACpB,IAAI;UAACgE,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAlC,QAAA,eAC9Bb,OAAA,CAACnB,IAAI;YAACuC,SAAS,EAAC,YAAY;YAAC4B,OAAO,EAAEA,CAAA,KAAM7C,QAAQ,CAAC,YAAYuC,MAAM,CAACO,EAAE,EAAE,CAAE;YAAApC,QAAA,eAC5Eb,OAAA,CAAClB,WAAW;cAAA+B,QAAA,gBACVb,OAAA,CAACjB,UAAU;gBAAC+B,OAAO,EAAC,IAAI;gBAACC,YAAY;gBAAAF,QAAA,EAClC6B,MAAM,CAACQ;cAAI;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACbnB,OAAA,CAACjB,UAAU;gBAAC+B,OAAO,EAAC,OAAO;gBAACuB,KAAK,EAAC,gBAAgB;gBAAAxB,QAAA,EAC/C6B,MAAM,CAACS,WAAW,IAAI;cAAgB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACbnB,OAAA,CAACjB,UAAU;gBAAC+B,OAAO,EAAC,SAAS;gBAACuB,KAAK,EAAC,gBAAgB;gBAACe,EAAE,EAAE,CAAE;gBAAC/B,OAAO,EAAC,OAAO;gBAAAR,QAAA,GAAC,YAChE,EAAC,EAAA8B,gBAAA,GAAAD,MAAM,CAACW,QAAQ,cAAAV,gBAAA,uBAAfA,gBAAA,CAAiBd,MAAM,KAAI,CAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC,GAb6BuB,MAAM,CAACO,EAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAczC,CAAC;MAAA,CACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACjB,EAAA,CArGID,OAAO;EAAA,QACMvB,WAAW;AAAA;AAAA4E,EAAA,GADxBrD,OAAO;AAuGb,eAAeA,OAAO;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}