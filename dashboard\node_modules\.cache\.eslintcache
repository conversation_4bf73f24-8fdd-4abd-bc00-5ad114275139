[{"C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js": "4", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js": "5", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js": "9", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js": "10", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js": "11", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js": "12", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js": "13", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js": "14", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js": "15", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js": "16", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js": "17", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js": "18", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js": "19", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js": "20", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js": "21", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js": "22", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js": "23", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js": "24", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js": "25", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js": "26", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\websocket.js": "27"}, {"size": 1991, "mtime": 1749052404537, "results": "28", "hashOfConfig": "29"}, {"size": 3994, "mtime": 1749062321778, "results": "30", "hashOfConfig": "29"}, {"size": 3532, "mtime": 1749052446327, "results": "31", "hashOfConfig": "29"}, {"size": 11609, "mtime": 1749052616010, "results": "32", "hashOfConfig": "29"}, {"size": 9778, "mtime": 1749052716786, "results": "33", "hashOfConfig": "29"}, {"size": 6896, "mtime": 1749052574600, "results": "34", "hashOfConfig": "29"}, {"size": 9470, "mtime": 1749062381799, "results": "35", "hashOfConfig": "29"}, {"size": 13590, "mtime": 1749061640801, "results": "36", "hashOfConfig": "29"}, {"size": 30134, "mtime": 1749062280343, "results": "37", "hashOfConfig": "29"}, {"size": 2649, "mtime": 1749052734801, "results": "38", "hashOfConfig": "29"}, {"size": 2069, "mtime": 1749052794187, "results": "39", "hashOfConfig": "29"}, {"size": 726, "mtime": 1749052818589, "results": "40", "hashOfConfig": "29"}, {"size": 746, "mtime": 1749052802284, "results": "41", "hashOfConfig": "29"}, {"size": 2461, "mtime": 1749052681630, "results": "42", "hashOfConfig": "29"}, {"size": 3308, "mtime": 1749052769149, "results": "43", "hashOfConfig": "29"}, {"size": 737, "mtime": 1749052781889, "results": "44", "hashOfConfig": "29"}, {"size": 4139, "mtime": 1749052837555, "results": "45", "hashOfConfig": "29"}, {"size": 9594, "mtime": 1749061616868, "results": "46", "hashOfConfig": "29"}, {"size": 7101, "mtime": 1749061053794, "results": "47", "hashOfConfig": "29"}, {"size": 18353, "mtime": 1749061024758, "results": "48", "hashOfConfig": "29"}, {"size": 11835, "mtime": 1749061093745, "results": "49", "hashOfConfig": "29"}, {"size": 12912, "mtime": 1749061176127, "results": "50", "hashOfConfig": "29"}, {"size": 7575, "mtime": 1749052484685, "results": "51", "hashOfConfig": "29"}, {"size": 9642, "mtime": 1749062194247, "results": "52", "hashOfConfig": "29"}, {"size": 11372, "mtime": 1749063863204, "results": "53", "hashOfConfig": "29"}, {"size": 13888, "mtime": 1749065639971, "results": "54", "hashOfConfig": "29"}, {"size": 10068, "mtime": 1749063534361, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c595us", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js", ["137", "138"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js", ["139"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js", ["140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js", ["167", "168"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js", ["169", "170"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js", ["171", "172"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js", ["173", "174", "175", "176"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js", ["177"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\websocket.js", [], [], {"ruleId": "178", "severity": 1, "message": "179", "line": 18, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 18, "endColumn": 6}, {"ruleId": "178", "severity": 1, "message": "182", "line": 29, "column": 11, "nodeType": "180", "messageId": "181", "endLine": 29, "endColumn": 19}, {"ruleId": "178", "severity": 1, "message": "183", "line": 42, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 42, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "184", "line": 10, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 10, "endColumn": 8}, {"ruleId": "178", "severity": 1, "message": "185", "line": 20, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 20, "endColumn": 12}, {"ruleId": "178", "severity": 1, "message": "186", "line": 34, "column": 16, "nodeType": "180", "messageId": "181", "endLine": 34, "endColumn": 25}, {"ruleId": "178", "severity": 1, "message": "187", "line": 35, "column": 14, "nodeType": "180", "messageId": "181", "endLine": 35, "endColumn": 25}, {"ruleId": "178", "severity": 1, "message": "188", "line": 36, "column": 15, "nodeType": "180", "messageId": "181", "endLine": 36, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "189", "line": 37, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 37, "endColumn": 17}, {"ruleId": "178", "severity": 1, "message": "190", "line": 38, "column": 12, "nodeType": "180", "messageId": "181", "endLine": 38, "endColumn": 21}, {"ruleId": "178", "severity": 1, "message": "191", "line": 47, "column": 8, "nodeType": "180", "messageId": "181", "endLine": 47, "endColumn": 20}, {"ruleId": "178", "severity": 1, "message": "192", "line": 49, "column": 8, "nodeType": "180", "messageId": "181", "endLine": 49, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "193", "line": 53, "column": 9, "nodeType": "180", "messageId": "181", "endLine": 53, "endColumn": 17}, {"ruleId": "178", "severity": 1, "message": "194", "line": 69, "column": 20, "nodeType": "180", "messageId": "181", "endLine": 69, "endColumn": 31}, {"ruleId": "178", "severity": 1, "message": "195", "line": 70, "column": 29, "nodeType": "180", "messageId": "181", "endLine": 70, "endColumn": 49}, {"ruleId": "178", "severity": 1, "message": "196", "line": 71, "column": 24, "nodeType": "180", "messageId": "181", "endLine": 71, "endColumn": 39}, {"ruleId": "178", "severity": 1, "message": "197", "line": 72, "column": 25, "nodeType": "180", "messageId": "181", "endLine": 72, "endColumn": 41}, {"ruleId": "178", "severity": 1, "message": "198", "line": 78, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 78, "endColumn": 26}, {"ruleId": "178", "severity": 1, "message": "199", "line": 78, "column": 28, "nodeType": "180", "messageId": "181", "endLine": 78, "endColumn": 47}, {"ruleId": "178", "severity": 1, "message": "200", "line": 93, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 93, "endColumn": 15}, {"ruleId": "178", "severity": 1, "message": "201", "line": 93, "column": 17, "nodeType": "180", "messageId": "181", "endLine": 93, "endColumn": 25}, {"ruleId": "178", "severity": 1, "message": "202", "line": 94, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 94, "endColumn": 21}, {"ruleId": "178", "severity": 1, "message": "203", "line": 94, "column": 23, "nodeType": "180", "messageId": "181", "endLine": 94, "endColumn": 37}, {"ruleId": "204", "severity": 1, "message": "205", "line": 111, "column": 6, "nodeType": "206", "endLine": 111, "endColumn": 29, "suggestions": "207"}, {"ruleId": "204", "severity": 1, "message": "208", "line": 130, "column": 6, "nodeType": "206", "endLine": 130, "endColumn": 45, "suggestions": "209"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 426, "column": 25, "nodeType": "212", "messageId": "213", "endLine": 426, "endColumn": 72}, {"ruleId": "210", "severity": 1, "message": "211", "line": 433, "column": 25, "nodeType": "212", "messageId": "213", "endLine": 433, "endColumn": 101}, {"ruleId": "210", "severity": 1, "message": "211", "line": 440, "column": 25, "nodeType": "212", "messageId": "213", "endLine": 440, "endColumn": 70}, {"ruleId": "210", "severity": 1, "message": "211", "line": 447, "column": 25, "nodeType": "212", "messageId": "213", "endLine": 447, "endColumn": 58}, {"ruleId": "210", "severity": 1, "message": "211", "line": 454, "column": 25, "nodeType": "212", "messageId": "213", "endLine": 454, "endColumn": 167}, {"ruleId": "178", "severity": 1, "message": "184", "line": 6, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 6, "endColumn": 8}, {"ruleId": "178", "severity": 1, "message": "214", "line": 7, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 7, "endColumn": 10}, {"ruleId": "178", "severity": 1, "message": "215", "line": 25, "column": 13, "nodeType": "180", "messageId": "181", "endLine": 25, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "216", "line": 83, "column": 9, "nodeType": "180", "messageId": "181", "endLine": 83, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "217", "line": 14, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 14, "endColumn": 8}, {"ruleId": "178", "severity": 1, "message": "214", "line": 15, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 15, "endColumn": 10}, {"ruleId": "178", "severity": 1, "message": "184", "line": 5, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 5, "endColumn": 8}, {"ruleId": "178", "severity": 1, "message": "218", "line": 29, "column": 8, "nodeType": "180", "messageId": "181", "endLine": 29, "endColumn": 13}, {"ruleId": "178", "severity": 1, "message": "219", "line": 33, "column": 11, "nodeType": "180", "messageId": "181", "endLine": 33, "endColumn": 15}, {"ruleId": "178", "severity": 1, "message": "220", "line": 37, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 37, "endColumn": 21}, {"ruleId": "204", "severity": 1, "message": "221", "line": 59, "column": 6, "nodeType": "206", "endLine": 59, "endColumn": 27, "suggestions": "222"}, "no-unused-vars", "'Fab' is defined but never used.", "Identifier", "unusedVar", "'StopIcon' is defined but never used.", "'recentServers' is assigned a value but never used.", "'Paper' is defined but never used.", "'TextField' is defined but never used.", "'DebugIcon' is defined but never used.", "'RefreshIcon' is defined but never used.", "'MoreIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'FileExplorer' is defined but never used.", "'DeploymentPanel' is defined but never used.", "'navigate' is assigned a value but never used.", "'setBotToken' is assigned a value but never used.", "'setIsConnectedToChat' is assigned a value but never used.", "'setChatServerId' is assigned a value but never used.", "'setChatChannelId' is assigned a value but never used.", "'showDeployDialog' is assigned a value but never used.", "'setShowDeployDialog' is assigned a value but never used.", "'files' is assigned a value but never used.", "'setFiles' is assigned a value but never used.", "'currentFile' is assigned a value but never used.", "'setCurrentFile' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCodeData' and 'loadCodeVersions'. Either include them or remove the dependency array.", "ArrayExpression", ["223"], "React Hook useEffect has a missing dependency: 'handleAutoSave'. Either include it or remove the dependency array.", ["224"], "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "'Divider' is defined but never used.", "'DeleteIcon' is defined but never used.", "'renderCodeDiff' is assigned a value but never used.", "'Alert' is defined but never used.", "'toast' is defined but never used.", "'user' is assigned a value but never used.", "'selectedBot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'connectToChat', 'loadChannelBots', and 'loadMessages'. Either include them or remove the dependency array.", ["225"], {"desc": "226", "fix": "227"}, {"desc": "228", "fix": "229"}, {"desc": "230", "fix": "231"}, "Update the dependencies array to be: [applicationId, codeId, loadCodeData, loadCodeVersions]", {"range": "232", "text": "233"}, "Update the dependencies array to be: [code, handleAutoSave, hasUnsavedChanges, originalCode]", {"range": "234", "text": "235"}, "Update the dependencies array to be: [serverId, channelId, connectToChat, loadMessages, loadChannelBots]", {"range": "236", "text": "237"}, [3347, 3370], "[applicationId, codeId, loadCodeData, loadCodeVersions]", [3841, 3880], "[code, handleAutoSave, hasUnsavedChanges, originalCode]", [1537, 1558], "[serverId, channelId, connectToChat, loadMessages, loadChannelBots]"]