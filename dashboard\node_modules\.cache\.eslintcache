[{"C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js": "4", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js": "5", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js": "7", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js": "9", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js": "10", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js": "11", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js": "12", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js": "13", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js": "14", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js": "15", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js": "16", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js": "17", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js": "18", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js": "19", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js": "20", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js": "21", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js": "22", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js": "23", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js": "24", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js": "25", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js": "26", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\websocket.js": "27", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Bot\\BotCodeEditor.js": "28"}, {"size": 1991, "mtime": 1749052404537, "results": "29", "hashOfConfig": "30"}, {"size": 3994, "mtime": 1749062321778, "results": "31", "hashOfConfig": "30"}, {"size": 3532, "mtime": 1749052446327, "results": "32", "hashOfConfig": "30"}, {"size": 11609, "mtime": 1749052616010, "results": "33", "hashOfConfig": "30"}, {"size": 9828, "mtime": 1749066322852, "results": "34", "hashOfConfig": "30"}, {"size": 6896, "mtime": 1749052574600, "results": "35", "hashOfConfig": "30"}, {"size": 9470, "mtime": 1749062381799, "results": "36", "hashOfConfig": "30"}, {"size": 13590, "mtime": 1749061640801, "results": "37", "hashOfConfig": "30"}, {"size": 30134, "mtime": 1749062280343, "results": "38", "hashOfConfig": "30"}, {"size": 2649, "mtime": 1749052734801, "results": "39", "hashOfConfig": "30"}, {"size": 2069, "mtime": 1749052794187, "results": "40", "hashOfConfig": "30"}, {"size": 726, "mtime": 1749052818589, "results": "41", "hashOfConfig": "30"}, {"size": 746, "mtime": 1749052802284, "results": "42", "hashOfConfig": "30"}, {"size": 2461, "mtime": 1749052681630, "results": "43", "hashOfConfig": "30"}, {"size": 3308, "mtime": 1749052769149, "results": "44", "hashOfConfig": "30"}, {"size": 737, "mtime": 1749052781889, "results": "45", "hashOfConfig": "30"}, {"size": 4139, "mtime": 1749052837555, "results": "46", "hashOfConfig": "30"}, {"size": 9594, "mtime": 1749061616868, "results": "47", "hashOfConfig": "30"}, {"size": 7101, "mtime": 1749061053794, "results": "48", "hashOfConfig": "30"}, {"size": 18353, "mtime": 1749061024758, "results": "49", "hashOfConfig": "30"}, {"size": 11835, "mtime": 1749061093745, "results": "50", "hashOfConfig": "30"}, {"size": 12912, "mtime": 1749061176127, "results": "51", "hashOfConfig": "30"}, {"size": 7575, "mtime": 1749052484685, "results": "52", "hashOfConfig": "30"}, {"size": 9642, "mtime": 1749062194247, "results": "53", "hashOfConfig": "30"}, {"size": 15615, "mtime": 1749066286478, "results": "54", "hashOfConfig": "30"}, {"size": 13888, "mtime": 1749065639971, "results": "55", "hashOfConfig": "30"}, {"size": 10068, "mtime": 1749063534361, "results": "56", "hashOfConfig": "30"}, {"size": 9390, "mtime": 1749066223825, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c595us", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 27, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Register.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\Applications.js", ["142", "143", "144"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Auth\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Layout\\Layout.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Dashboard\\Dashboard.js", ["145"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeEditor.js", ["146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Applications\\ApplicationDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Runtime\\Runtime.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Logs\\Logs.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Storage\\Storage.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Common\\NotFound.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\Servers.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Servers\\ServerDetail.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Profile\\Profile.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\FileExplorer.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\LiveConsole.js", ["173", "174"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeTemplateSelector.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\CodeHistory.js", ["175", "176"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\CodeEditor\\DeploymentPanel.js", ["177", "178"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\Chat.js", ["179", "180", "181", "182"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ChatInterface.js", ["183"], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Chat\\ServerSidebar.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\services\\websocket.js", [], [], "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\src\\components\\Bot\\BotCodeEditor.js", ["184", "185"], [], {"ruleId": "186", "severity": 1, "message": "187", "line": 18, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 18, "endColumn": 6}, {"ruleId": "186", "severity": 1, "message": "190", "line": 29, "column": 11, "nodeType": "188", "messageId": "189", "endLine": 29, "endColumn": 19}, {"ruleId": "186", "severity": 1, "message": "191", "line": 35, "column": 8, "nodeType": "188", "messageId": "189", "endLine": 35, "endColumn": 21}, {"ruleId": "186", "severity": 1, "message": "192", "line": 42, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 42, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "193", "line": 10, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 10, "endColumn": 8}, {"ruleId": "186", "severity": 1, "message": "194", "line": 20, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 20, "endColumn": 12}, {"ruleId": "186", "severity": 1, "message": "195", "line": 34, "column": 16, "nodeType": "188", "messageId": "189", "endLine": 34, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "196", "line": 35, "column": 14, "nodeType": "188", "messageId": "189", "endLine": 35, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "197", "line": 36, "column": 15, "nodeType": "188", "messageId": "189", "endLine": 36, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "198", "line": 37, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 37, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "199", "line": 38, "column": 12, "nodeType": "188", "messageId": "189", "endLine": 38, "endColumn": 21}, {"ruleId": "186", "severity": 1, "message": "200", "line": 47, "column": 8, "nodeType": "188", "messageId": "189", "endLine": 47, "endColumn": 20}, {"ruleId": "186", "severity": 1, "message": "201", "line": 49, "column": 8, "nodeType": "188", "messageId": "189", "endLine": 49, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "202", "line": 53, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 53, "endColumn": 17}, {"ruleId": "186", "severity": 1, "message": "203", "line": 69, "column": 20, "nodeType": "188", "messageId": "189", "endLine": 69, "endColumn": 31}, {"ruleId": "186", "severity": 1, "message": "204", "line": 70, "column": 29, "nodeType": "188", "messageId": "189", "endLine": 70, "endColumn": 49}, {"ruleId": "186", "severity": 1, "message": "205", "line": 71, "column": 24, "nodeType": "188", "messageId": "189", "endLine": 71, "endColumn": 39}, {"ruleId": "186", "severity": 1, "message": "206", "line": 72, "column": 25, "nodeType": "188", "messageId": "189", "endLine": 72, "endColumn": 41}, {"ruleId": "186", "severity": 1, "message": "207", "line": 78, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 78, "endColumn": 26}, {"ruleId": "186", "severity": 1, "message": "208", "line": 78, "column": 28, "nodeType": "188", "messageId": "189", "endLine": 78, "endColumn": 47}, {"ruleId": "186", "severity": 1, "message": "209", "line": 93, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 93, "endColumn": 15}, {"ruleId": "186", "severity": 1, "message": "210", "line": 93, "column": 17, "nodeType": "188", "messageId": "189", "endLine": 93, "endColumn": 25}, {"ruleId": "186", "severity": 1, "message": "211", "line": 94, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 94, "endColumn": 21}, {"ruleId": "186", "severity": 1, "message": "212", "line": 94, "column": 23, "nodeType": "188", "messageId": "189", "endLine": 94, "endColumn": 37}, {"ruleId": "213", "severity": 1, "message": "214", "line": 111, "column": 6, "nodeType": "215", "endLine": 111, "endColumn": 29, "suggestions": "216"}, {"ruleId": "213", "severity": 1, "message": "217", "line": 130, "column": 6, "nodeType": "215", "endLine": 130, "endColumn": 45, "suggestions": "218"}, {"ruleId": "219", "severity": 1, "message": "220", "line": 426, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 426, "endColumn": 72}, {"ruleId": "219", "severity": 1, "message": "220", "line": 433, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 433, "endColumn": 101}, {"ruleId": "219", "severity": 1, "message": "220", "line": 440, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 440, "endColumn": 70}, {"ruleId": "219", "severity": 1, "message": "220", "line": 447, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 447, "endColumn": 58}, {"ruleId": "219", "severity": 1, "message": "220", "line": 454, "column": 25, "nodeType": "221", "messageId": "222", "endLine": 454, "endColumn": 167}, {"ruleId": "186", "severity": 1, "message": "193", "line": 6, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 6, "endColumn": 8}, {"ruleId": "186", "severity": 1, "message": "223", "line": 7, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 7, "endColumn": 10}, {"ruleId": "186", "severity": 1, "message": "224", "line": 25, "column": 13, "nodeType": "188", "messageId": "189", "endLine": 25, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "225", "line": 83, "column": 9, "nodeType": "188", "messageId": "189", "endLine": 83, "endColumn": 23}, {"ruleId": "186", "severity": 1, "message": "226", "line": 14, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 14, "endColumn": 8}, {"ruleId": "186", "severity": 1, "message": "223", "line": 15, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 15, "endColumn": 10}, {"ruleId": "186", "severity": 1, "message": "193", "line": 5, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 5, "endColumn": 8}, {"ruleId": "186", "severity": 1, "message": "227", "line": 29, "column": 8, "nodeType": "188", "messageId": "189", "endLine": 29, "endColumn": 13}, {"ruleId": "186", "severity": 1, "message": "228", "line": 33, "column": 11, "nodeType": "188", "messageId": "189", "endLine": 33, "endColumn": 15}, {"ruleId": "186", "severity": 1, "message": "229", "line": 37, "column": 10, "nodeType": "188", "messageId": "189", "endLine": 37, "endColumn": 21}, {"ruleId": "213", "severity": 1, "message": "230", "line": 59, "column": 6, "nodeType": "215", "endLine": 59, "endColumn": 27, "suggestions": "231"}, {"ruleId": "186", "severity": 1, "message": "232", "line": 9, "column": 3, "nodeType": "188", "messageId": "189", "endLine": 9, "endColumn": 14}, {"ruleId": "213", "severity": 1, "message": "233", "line": 35, "column": 6, "nodeType": "215", "endLine": 35, "endColumn": 13, "suggestions": "234"}, "no-unused-vars", "'Fab' is defined but never used.", "Identifier", "unusedVar", "'StopIcon' is defined but never used.", "'BotCodeEditor' is defined but never used.", "'recentServers' is assigned a value but never used.", "'Paper' is defined but never used.", "'TextField' is defined but never used.", "'DebugIcon' is defined but never used.", "'RefreshIcon' is defined but never used.", "'MoreIcon' is defined but never used.", "'AddIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'FileExplorer' is defined but never used.", "'DeploymentPanel' is defined but never used.", "'navigate' is assigned a value but never used.", "'setBotToken' is assigned a value but never used.", "'setIsConnectedToChat' is assigned a value but never used.", "'setChatServerId' is assigned a value but never used.", "'setChatChannelId' is assigned a value but never used.", "'showDeployDialog' is assigned a value but never used.", "'setShowDeployDialog' is assigned a value but never used.", "'files' is assigned a value but never used.", "'setFiles' is assigned a value but never used.", "'currentFile' is assigned a value but never used.", "'setCurrentFile' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadCodeData' and 'loadCodeVersions'. Either include them or remove the dependency array.", "ArrayExpression", ["235"], "React Hook useEffect has a missing dependency: 'handleAutoSave'. Either include it or remove the dependency array.", ["236"], "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "'Divider' is defined but never used.", "'DeleteIcon' is defined but never used.", "'renderCodeDiff' is assigned a value but never used.", "'Alert' is defined but never used.", "'toast' is defined but never used.", "'user' is assigned a value but never used.", "'selectedBot' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'connectToChat', 'loadChannelBots', and 'loadMessages'. Either include them or remove the dependency array.", ["237"], "'CardActions' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadBotCode' and 'loadBotInfo'. Either include them or remove the dependency array.", ["238"], {"desc": "239", "fix": "240"}, {"desc": "241", "fix": "242"}, {"desc": "243", "fix": "244"}, {"desc": "245", "fix": "246"}, "Update the dependencies array to be: [applicationId, codeId, loadCodeData, loadCodeVersions]", {"range": "247", "text": "248"}, "Update the dependencies array to be: [code, handleAutoSave, hasUnsavedChanges, originalCode]", {"range": "249", "text": "250"}, "Update the dependencies array to be: [serverId, channelId, connectToChat, loadMessages, loadChannelBots]", {"range": "251", "text": "252"}, "Update the dependencies array to be: [botId, loadBotCode, loadBotInfo]", {"range": "253", "text": "254"}, [3347, 3370], "[applicationId, codeId, loadCodeData, loadCodeVersions]", [3841, 3880], "[code, handleAutoSave, hasUnsavedChanges, originalCode]", [1537, 1558], "[serverId, channelId, connectToChat, loadMessages, loadChannelBots]", [770, 777], "[botId, loadBotCode, loadBotInfo]"]