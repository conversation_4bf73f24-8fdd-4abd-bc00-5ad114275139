-- Users table (developers who create bots)
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Applications table (bot applications)
CREATE TABLE IF NOT EXISTS applications (
    id TEXT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id TEXT NOT NULL,
    bot_token TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'disabled', 'suspended')),
    FOREIG<PERSON> KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Servers table (Discord-like servers)
CREATE TABLE IF NOT EXISTS servers (
    id TEXT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Channels table
CREATE TABLE IF NOT EXISTS channels (
    id TEXT PRIMARY KEY,
    server_id TEXT NOT NULL,
    name VARCHAR(100) NOT NULL,
    type TEXT DEFAULT 'text' CHECK (type IN ('text', 'voice')),
    position INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE
);

-- Server members table
CREATE TABLE IF NOT EXISTS server_members (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    server_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    role TEXT DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(server_id, user_id)
);

-- Channel bots table (bots invited to specific channels)
CREATE TABLE IF NOT EXISTS channel_bots (
    id TEXT PRIMARY KEY DEFAULT (hex(randomblob(16))),
    channel_id TEXT NOT NULL,
    bot_id TEXT NOT NULL,
    invited_by TEXT NOT NULL,
    invited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE,
    FOREIGN KEY (bot_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(channel_id, bot_id)
);

-- Bot instances table (bots deployed to servers)
CREATE TABLE IF NOT EXISTS bot_instances (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    server_id TEXT NOT NULL,
    config TEXT, -- JSON config
    last_active DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'online' CHECK (status IN ('online', 'offline', 'error')),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE(application_id, server_id)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id TEXT PRIMARY KEY,
    channel_id TEXT NOT NULL,
    author_id TEXT, -- NULL for bot messages
    author_type TEXT DEFAULT 'user' CHECK (author_type IN ('user', 'bot')),
    content TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (channel_id) REFERENCES channels(id) ON DELETE CASCADE
);

-- Bot permissions table
CREATE TABLE IF NOT EXISTS bot_permissions (
    id TEXT PRIMARY KEY,
    bot_instance_id TEXT NOT NULL,
    permission TEXT NOT NULL,
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bot_instance_id) REFERENCES bot_instances(id) ON DELETE CASCADE
);

-- Rate limiting table
CREATE TABLE IF NOT EXISTS rate_limits (
    id TEXT PRIMARY KEY,
    bot_token TEXT NOT NULL,
    endpoint TEXT NOT NULL,
    requests_count INTEGER DEFAULT 0,
    window_start DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(bot_token, endpoint)
);

-- Bot code table (for runtime environment)
CREATE TABLE IF NOT EXISTS bot_code (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    version INTEGER DEFAULT 1,
    code TEXT NOT NULL,
    entry_point TEXT DEFAULT 'index.js',
    dependencies TEXT, -- JSON array of dependencies
    environment_vars TEXT, -- JSON object of environment variables
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'deployed', 'archived')),
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Bot runtime instances table
CREATE TABLE IF NOT EXISTS bot_runtime_instances (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    code_id TEXT NOT NULL,
    server_id TEXT NOT NULL,
    process_id TEXT,
    status TEXT DEFAULT 'stopped' CHECK (status IN ('starting', 'running', 'stopped', 'error', 'crashed')),
    last_restart DATETIME,
    restart_count INTEGER DEFAULT 0,
    cpu_usage REAL DEFAULT 0,
    memory_usage INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (code_id) REFERENCES bot_code(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE(application_id, server_id)
);

-- Bot logs table
CREATE TABLE IF NOT EXISTS bot_logs (
    id TEXT PRIMARY KEY,
    runtime_instance_id TEXT NOT NULL,
    level TEXT DEFAULT 'info' CHECK (level IN ('debug', 'info', 'warn', 'error', 'fatal')),
    message TEXT NOT NULL,
    metadata TEXT, -- JSON object for additional data
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (runtime_instance_id) REFERENCES bot_runtime_instances(id) ON DELETE CASCADE
);

-- Bot storage table (persistent data for bots)
CREATE TABLE IF NOT EXISTS bot_storage (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    server_id TEXT,
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    UNIQUE(application_id, server_id, key)
);

-- Bot scheduled tasks table
CREATE TABLE IF NOT EXISTS bot_scheduled_tasks (
    id TEXT PRIMARY KEY,
    application_id TEXT NOT NULL,
    name VARCHAR(100) NOT NULL,
    cron_expression VARCHAR(100) NOT NULL,
    function_name VARCHAR(100) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    last_run DATETIME,
    next_run DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_applications_owner_id ON applications(owner_id);
CREATE INDEX IF NOT EXISTS idx_applications_bot_token ON applications(bot_token);
CREATE INDEX IF NOT EXISTS idx_bot_instances_application_id ON bot_instances(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_instances_server_id ON bot_instances(server_id);
CREATE INDEX IF NOT EXISTS idx_messages_channel_id ON messages(channel_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_rate_limits_bot_token ON rate_limits(bot_token);
CREATE INDEX IF NOT EXISTS idx_channels_server_id ON channels(server_id);
CREATE INDEX IF NOT EXISTS idx_bot_code_application_id ON bot_code(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_runtime_instances_application_id ON bot_runtime_instances(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_logs_runtime_instance_id ON bot_logs(runtime_instance_id);
CREATE INDEX IF NOT EXISTS idx_bot_logs_created_at ON bot_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_bot_storage_application_id ON bot_storage(application_id);
CREATE INDEX IF NOT EXISTS idx_bot_storage_key ON bot_storage(application_id, server_id, key);
CREATE INDEX IF NOT EXISTS idx_bot_scheduled_tasks_application_id ON bot_scheduled_tasks(application_id);
