const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const cluster = require('cluster');
require('dotenv').config();

// Cluster support
const ClusterWorker = require('./cluster/worker');
const clusterWorker = new ClusterWorker();

// Monitoring and metrics
const MetricsManager = require('./monitoring/metrics');
const metrics = new MetricsManager();

// Database performance
const DatabasePerformance = require('./database/performance');

const database = require('./database/connection');
const authRoutes = require('./api/auth');
const applicationRoutes = require('./api/applications');
const botRoutes = require('./api/bot');
const serverRoutes = require('./api/servers');
const botCodeRoutes = require('./api/bot-code');
const botRuntimeRoutes = require('./api/bot-runtime');
const botStorageRoutes = require('./api/bot-storage');
const GatewayServer = require('./gateway/server');
const ChatWebSocketServer = require('./chat/websocket-server');

const app = express();
const PORT = process.env.PORT || 3002;

// Security middleware
app.use(helmet());
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
}));

// Rate limiting for general API
const generalLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: {
        error: 'Too many requests from this IP, please try again later.'
    }
});

app.use(generalLimiter);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Cluster worker middleware
if (clusterWorker.isClusterMode) {
    app.use(clusterWorker.createRequestMiddleware());
    console.log(`🔧 Worker ${clusterWorker.workerId} middleware enabled`);
}

// Metrics middleware
app.use(metrics.createHttpMiddleware());

// Serve dashboard static files
const path = require('path');
const fs = require('fs');
const dashboardBuildPath = path.join(__dirname, '../dashboard/build');
if (fs.existsSync(dashboardBuildPath)) {
    app.use(express.static(dashboardBuildPath));
    console.log('Dashboard static files served from:', dashboardBuildPath);
}

// Request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        cluster: clusterWorker.isClusterMode ? {
            workerId: clusterWorker.workerId,
            stats: clusterWorker.getStats()
        } : null
    });
});

// Metrics endpoint for Prometheus
app.get('/metrics', async (req, res) => {
    try {
        const metricsData = await metrics.getMetrics();
        res.set('Content-Type', metrics.register.contentType);
        res.end(metricsData);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get metrics' });
    }
});

// Metrics JSON endpoint
app.get('/metrics/json', async (req, res) => {
    try {
        const metricsData = await metrics.getMetricsJSON();
        res.json(metricsData);
    } catch (error) {
        res.status(500).json({ error: 'Failed to get metrics' });
    }
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/applications', applicationRoutes);
app.use('/api/applications', botCodeRoutes);
app.use('/api/applications', botRuntimeRoutes);
app.use('/api/bot', botRoutes);
app.use('/api/bot', botStorageRoutes);
app.use('/api/servers', serverRoutes);

// Serve dashboard demo
app.get('/dashboard', (req, res) => {
    const demoPath = path.join(__dirname, '../dashboard/public/demo.html');
    if (fs.existsSync(demoPath)) {
        res.sendFile(demoPath);
    } else {
        res.status(404).json({
            error: 'Dashboard demo not found'
        });
    }
});

// Serve React app for other dashboard routes
app.get('*', (req, res) => {
    // If it's an API route, return 404 JSON
    if (req.path.startsWith('/api/')) {
        return res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.method} ${req.originalUrl} not found`
        });
    }

    // For dashboard routes, try to serve React app first, then demo
    const indexPath = path.join(dashboardBuildPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        res.sendFile(indexPath);
    } else {
        // Fallback to demo for now
        const demoPath = path.join(__dirname, '../dashboard/public/demo.html');
        if (fs.existsSync(demoPath)) {
            res.sendFile(demoPath);
        } else {
            res.status(404).json({
                error: 'Dashboard not available',
                message: 'Please run "npm run dashboard:build" to build the dashboard'
            });
        }
    }
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Global error handler:', err);

    // Record error in cluster worker
    if (clusterWorker.isClusterMode) {
        clusterWorker.recordError();
    }

    // Don't leak error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';

    res.status(err.status || 500).json({
        error: 'Internal Server Error',
        message: isDevelopment ? err.message : 'Something went wrong',
        ...(isDevelopment && { stack: err.stack })
    });
});

// Cluster error middleware
if (clusterWorker.isClusterMode) {
    app.use(clusterWorker.createErrorMiddleware());
}

// Graceful shutdown handler
process.on('SIGTERM', async () => {
    console.log('SIGTERM received, shutting down gracefully...');
    const gateway = app.get('gateway');
    const chatWebSocket = app.get('chatWebSocket');
    if (gateway) {
        await gateway.stop();
    }
    if (chatWebSocket) {
        await chatWebSocket.stop();
    }
    await database.close();
    process.exit(0);
});

process.on('SIGINT', async () => {
    console.log('SIGINT received, shutting down gracefully...');
    const gateway = app.get('gateway');
    const chatWebSocket = app.get('chatWebSocket');
    if (gateway) {
        await gateway.stop();
    }
    if (chatWebSocket) {
        await chatWebSocket.stop();
    }
    await database.close();
    process.exit(0);
});

// Start server
async function startServer() {
    try {
        // Connect to database
        await database.connect();
        console.log('Database connected successfully');

        // Run migrations
        await database.migrate();
        console.log('Database migrations completed');

        // Initialize database performance monitoring (disabled for now)
        // const dbPerformance = new DatabasePerformance(database);
        // await dbPerformance.initialize();
        // app.set('dbPerformance', dbPerformance);
        console.log('Database performance monitoring disabled (schema compatibility)');

        // Start WebSocket Gateway (only in non-cluster mode or master process)
        let gateway = null;
        let chatWebSocket = null;
        if (!clusterWorker.isClusterMode) {
            gateway = new GatewayServer();
            await gateway.start();
            console.log('WebSocket Gateway started successfully');

            // Start Chat WebSocket Server
            chatWebSocket = new ChatWebSocketServer();
            await chatWebSocket.start();
            console.log('Chat WebSocket server started successfully');
        } else {
            console.log('WebSocket Gateway disabled in cluster worker mode');
        }



        // Start HTTP server
        app.listen(PORT, () => {
            const workerInfo = clusterWorker.isClusterMode ? ` (Worker ${clusterWorker.workerId})` : '';
            console.log(`Server running on port ${PORT}${workerInfo}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
            console.log(`Health check: http://localhost:${PORT}/health`);

            if (clusterWorker.isClusterMode) {
                console.log(`🔧 Worker ${clusterWorker.workerId} ready to serve requests`);
            }
        });

        // Store gateway reference for graceful shutdown
        if (gateway) {
            app.set('gateway', gateway);
        }
        if (chatWebSocket) {
            app.set('chatWebSocket', chatWebSocket);
        }

    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// Start the server
startServer();

module.exports = app;
