import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  Card,
  CardContent,
  CardActions,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  PlayArrow as RunIcon,
  Save as SaveIcon,
  Stop as StopIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import toast from 'react-hot-toast';

const BotCodeEditor = ({ botId, onClose }) => {
  const [code, setCode] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [logs, setLogs] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [botInfo, setBotInfo] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadBotCode();
    loadBotInfo();
  }, [botId]);

  const loadBotInfo = async () => {
    try {
      const response = await fetch(`/api/applications/${botId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setBotInfo(data.application);
      }
    } catch (error) {
      console.error('Error loading bot info:', error);
    }
  };

  const loadBotCode = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/applications/${botId}/code`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setCode(data.code || getDefaultBotCode());
      } else {
        setCode(getDefaultBotCode());
      }
    } catch (error) {
      console.error('Error loading bot code:', error);
      setCode(getDefaultBotCode());
    } finally {
      setLoading(false);
    }
  };

  const getDefaultBotCode = () => {
    return `// Discord-like Bot Code
// Bạn có thể viết code JavaScript để điều khiển bot

// Lắng nghe tin nhắn
bot.on('messageCreate', (message) => {
  // Không phản hồi tin nhắn của bot khác
  if (message.author.bot) return;
  
  // Phản hồi khi ai đó nói "hello"
  if (message.content.toLowerCase().includes('hello')) {
    message.reply('Xin chào! Tôi là bot của bạn 🤖');
  }
  
  // Lệnh !ping
  if (message.content === '!ping') {
    message.reply('Pong! 🏓');
  }
  
  // Lệnh !time
  if (message.content === '!time') {
    const now = new Date().toLocaleString('vi-VN');
    message.reply(\`Bây giờ là: \${now}\`);
  }
});

// Khi bot được khởi động
bot.on('ready', () => {
  console.log(\`Bot \${bot.user.username} đã sẵn sàng!\`);
});

// Bạn có thể thêm nhiều tính năng khác...
`;
  };

  const saveCode = async () => {
    try {
      const response = await fetch(`/api/applications/${botId}/code`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ code })
      });

      if (response.ok) {
        toast.success('Code đã được lưu!');
      } else {
        toast.error('Lỗi khi lưu code');
      }
    } catch (error) {
      console.error('Error saving code:', error);
      toast.error('Lỗi khi lưu code');
    }
  };

  const runBot = async () => {
    try {
      setIsRunning(true);
      setLogs([]);
      
      const response = await fetch(`/api/applications/${botId}/runtime/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ code })
      });

      if (response.ok) {
        toast.success('Bot đã được khởi động!');
        setLogs(prev => [...prev, { type: 'info', message: 'Bot đã được khởi động thành công' }]);
      } else {
        const error = await response.json();
        toast.error('Lỗi khi khởi động bot');
        setLogs(prev => [...prev, { type: 'error', message: error.error || 'Lỗi không xác định' }]);
        setIsRunning(false);
      }
    } catch (error) {
      console.error('Error running bot:', error);
      toast.error('Lỗi khi khởi động bot');
      setIsRunning(false);
    }
  };

  const stopBot = async () => {
    try {
      const response = await fetch(`/api/applications/${botId}/runtime/stop`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        toast.success('Bot đã được dừng!');
        setLogs(prev => [...prev, { type: 'info', message: 'Bot đã được dừng' }]);
      } else {
        toast.error('Lỗi khi dừng bot');
      }
    } catch (error) {
      console.error('Error stopping bot:', error);
      toast.error('Lỗi khi dừng bot');
    } finally {
      setIsRunning(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ height: '80vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={2}>
            <CodeIcon />
            <Typography variant="h6">
              Bot Code Editor - {botInfo?.name}
            </Typography>
            <Chip 
              label={isRunning ? 'Running' : 'Stopped'} 
              color={isRunning ? 'success' : 'default'}
              size="small"
            />
          </Box>
          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={saveCode}
            >
              Lưu Code
            </Button>
            {isRunning ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<StopIcon />}
                onClick={stopBot}
              >
                Dừng Bot
              </Button>
            ) : (
              <Button
                variant="contained"
                color="primary"
                startIcon={<RunIcon />}
                onClick={runBot}
              >
                Chạy Bot
              </Button>
            )}
            <Button onClick={onClose}>
              Đóng
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
          <Tab label="Code Editor" />
          <Tab label="Logs" />
          <Tab label="Hướng dẫn" />
        </Tabs>
      </Box>

      {/* Tab Content */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        {activeTab === 0 && (
          <Box sx={{ height: '100%', p: 2 }}>
            <TextField
              fullWidth
              multiline
              rows={25}
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder="Viết code JavaScript cho bot của bạn..."
              variant="outlined"
              sx={{
                '& .MuiInputBase-root': {
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '14px',
                },
              }}
            />
          </Box>
        )}

        {activeTab === 1 && (
          <Box sx={{ height: '100%', overflow: 'auto', p: 2 }}>
            {logs.length === 0 ? (
              <Typography color="text.secondary">
                Chưa có logs. Chạy bot để xem logs.
              </Typography>
            ) : (
              logs.map((log, index) => (
                <Alert 
                  key={index} 
                  severity={log.type} 
                  sx={{ mb: 1 }}
                >
                  {log.message}
                </Alert>
              ))
            )}
          </Box>
        )}

        {activeTab === 2 && (
          <Box sx={{ height: '100%', overflow: 'auto', p: 2 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Hướng dẫn viết Bot
                </Typography>
                <Typography variant="body2" paragraph>
                  Bot của bạn có thể lắng nghe các sự kiện và phản hồi tin nhắn:
                </Typography>
                <Typography variant="body2" component="pre" sx={{ bgcolor: 'grey.100', p: 2, borderRadius: 1 }}>
{`// Lắng nghe tin nhắn mới
bot.on('messageCreate', (message) => {
  if (message.content === '!hello') {
    message.reply('Xin chào!');
  }
});

// Các lệnh có sẵn:
// message.reply(text) - Phản hồi tin nhắn
// message.author.username - Tên người gửi
// message.content - Nội dung tin nhắn
// message.channel.send(text) - Gửi tin nhắn mới`}
                </Typography>
              </CardContent>
            </Card>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default BotCodeEditor;
