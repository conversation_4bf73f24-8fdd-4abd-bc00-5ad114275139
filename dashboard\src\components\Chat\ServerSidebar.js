import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Avatar,
  IconButton,
  Collapse,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Menu,
  MenuItem,
  // Divider,
} from '@mui/material';
import {
  ExpandLess,
  ExpandMore,
  Tag as ChannelIcon,
  VolumeUp as VoiceIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  SmartToy as BotIcon,
  // Person as PersonIcon,
  // MoreVert as MoreIcon,
} from '@mui/icons-material';
import toast from 'react-hot-toast';

const ServerSidebar = ({ selectedServer, selectedChannel, onServerSelect, onChannelSelect }) => {
  // const { user } = useAuth();
  const [servers, setServers] = useState([]);
  const [channels, setChannels] = useState([]);
  const [expandedServers, setExpandedServers] = useState(new Set());
  const [showCreateServerDialog, setShowCreateServerDialog] = useState(false);
  const [showCreateChannelDialog, setShowCreateChannelDialog] = useState(false);
  const [newServerName, setNewServerName] = useState('');
  const [newChannelName, setNewChannelName] = useState('');
  const [contextMenu, setContextMenu] = useState(null);
  const [contextItem, setContextItem] = useState(null);

  const loadServers = useCallback(async () => {
    try {
      const response = await fetch('/api/servers', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setServers(data.servers || []);

        // Auto-select first server if none selected
        if (!selectedServer && data.servers.length > 0) {
          onServerSelect(data.servers[0]);
        }
      }
    } catch (error) {
      console.error('Error loading servers:', error);
    }
  }, [selectedServer, onServerSelect]);

  const loadChannels = useCallback(async (serverId) => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setChannels(data.channels || []);

        // Auto-select first channel if none selected
        if (!selectedChannel && data.channels.length > 0) {
          onChannelSelect(data.channels[0]);
        }
      }
    } catch (error) {
      console.error('Error loading channels:', error);
    }
  }, [selectedChannel, onChannelSelect]);

  useEffect(() => {
    loadServers();
  }, [loadServers]);

  useEffect(() => {
    if (selectedServer) {
      loadChannels(selectedServer.id);
      setExpandedServers(prev => new Set([...prev, selectedServer.id]));
    }
  }, [selectedServer, loadChannels]);

  const createServer = async () => {
    if (!newServerName.trim()) {
      toast.error('Tên server không được để trống');
      return;
    }

    console.log('Creating server with name:', newServerName.trim());
    console.log('Token:', localStorage.getItem('token'));

    try {
      const response = await fetch('/api/servers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: newServerName.trim(),
          description: `${newServerName.trim()} server`
        })
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (response.ok) {
        const data = await response.json();
        console.log('Server created successfully:', data);
        setServers(prev => [...prev, data.server]);
        setNewServerName('');
        setShowCreateServerDialog(false);
        toast.success('Tạo server thành công!');

        // Auto-select new server
        onServerSelect(data.server);
      } else {
        const errorData = await response.json();
        console.error('Server creation failed:', errorData);
        toast.error(errorData.error || 'Không thể tạo server');
      }
    } catch (error) {
      console.error('Error creating server:', error);
      toast.error('Lỗi kết nối khi tạo server');
    }
  };

  const createChannel = async () => {
    if (!newChannelName.trim() || !selectedServer) return;

    try {
      const response = await fetch(`/api/servers/${selectedServer.id}/channels`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: newChannelName.trim(),
          type: 'text'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setChannels(prev => [...prev, data.channel]);
        setNewChannelName('');
        setShowCreateChannelDialog(false);
        toast.success('Channel created successfully');
        
        // Auto-select new channel
        onChannelSelect(data.channel);
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to create channel');
      }
    } catch (error) {
      console.error('Error creating channel:', error);
      toast.error('Failed to create channel');
    }
  };

  const toggleServerExpansion = (serverId) => {
    setExpandedServers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(serverId)) {
        newSet.delete(serverId);
      } else {
        newSet.add(serverId);
      }
      return newSet;
    });
  };

  const handleContextMenu = (event, item, type) => {
    event.preventDefault();
    setContextMenu({ mouseX: event.clientX, mouseY: event.clientY });
    setContextItem({ ...item, type });
  };

  const closeContextMenu = () => {
    setContextMenu(null);
    setContextItem(null);
  };

  const getChannelIcon = (channel) => {
    switch (channel.type) {
      case 'voice':
        return <VoiceIcon />;
      default:
        return <ChannelIcon />;
    }
  };

  const getOnlineBotCount = (serverId) => {
    // This would be implemented with real-time data
    return Math.floor(Math.random() * 5);
  };

  return (
    <Box sx={{ width: 240, bgcolor: 'grey.100', height: '100%', overflow: 'auto' }}>
      {/* Server List Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Servers</Typography>
          <IconButton
            size="small"
            onClick={() => {
              console.log('Create server button clicked');
              setShowCreateServerDialog(true);
            }}
          >
            <AddIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Servers and Channels */}
      <List dense>
        {servers.map((server) => (
          <React.Fragment key={server.id}>
            {/* Server Item */}
            <ListItem disablePadding>
              <ListItemButton
                selected={selectedServer?.id === server.id}
                onClick={() => {
                  onServerSelect(server);
                  toggleServerExpansion(server.id);
                }}
                onContextMenu={(e) => handleContextMenu(e, server, 'server')}
              >
                <ListItemIcon>
                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                    {server.name.charAt(0).toUpperCase()}
                  </Avatar>
                </ListItemIcon>
                <ListItemText 
                  primary={server.name}
                  secondary={
                    <Box display="flex" gap={1} mt={0.5}>
                      <Chip 
                        label={`${getOnlineBotCount(server.id)} bots`}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </Box>
                  }
                />
                {expandedServers.has(server.id) ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>

            {/* Channels */}
            <Collapse in={expandedServers.has(server.id)} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {/* Channel Header */}
                <ListItem sx={{ pl: 4 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="center" width="100%">
                    <Typography variant="caption" color="text.secondary" fontWeight="bold">
                      TEXT CHANNELS
                    </Typography>
                    <IconButton 
                      size="small" 
                      onClick={() => setShowCreateChannelDialog(true)}
                      disabled={selectedServer?.id !== server.id}
                    >
                      <AddIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </ListItem>

                {/* Channel List */}
                {selectedServer?.id === server.id && channels
                  .filter(channel => channel.type === 'text')
                  .map((channel) => (
                    <ListItem key={channel.id} disablePadding>
                      <ListItemButton
                        sx={{ pl: 6 }}
                        selected={selectedChannel?.id === channel.id}
                        onClick={() => onChannelSelect(channel)}
                        onContextMenu={(e) => handleContextMenu(e, channel, 'channel')}
                      >
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {getChannelIcon(channel)}
                        </ListItemIcon>
                        <ListItemText 
                          primary={channel.name}
                          primaryTypographyProps={{ variant: 'body2' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}

                {/* Voice Channels */}
                {selectedServer?.id === server.id && channels.some(c => c.type === 'voice') && (
                  <>
                    <ListItem sx={{ pl: 4 }}>
                      <Typography variant="caption" color="text.secondary" fontWeight="bold">
                        VOICE CHANNELS
                      </Typography>
                    </ListItem>
                    {channels
                      .filter(channel => channel.type === 'voice')
                      .map((channel) => (
                        <ListItem key={channel.id} disablePadding>
                          <ListItemButton
                            sx={{ pl: 6 }}
                            selected={selectedChannel?.id === channel.id}
                            onClick={() => onChannelSelect(channel)}
                          >
                            <ListItemIcon sx={{ minWidth: 32 }}>
                              {getChannelIcon(channel)}
                            </ListItemIcon>
                            <ListItemText 
                              primary={channel.name}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItemButton>
                        </ListItem>
                      ))}
                  </>
                )}
              </List>
            </Collapse>
          </React.Fragment>
        ))}
      </List>

      {/* Context Menu */}
      <Menu
        open={contextMenu !== null}
        onClose={closeContextMenu}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
      >
        {contextItem?.type === 'server' && [
          <MenuItem key="settings" onClick={closeContextMenu}>
            <SettingsIcon sx={{ mr: 1 }} />
            Server Settings
          </MenuItem>,
          <MenuItem key="invite" onClick={closeContextMenu}>
            <AddIcon sx={{ mr: 1 }} />
            Invite Bot
          </MenuItem>
        ]}
        {contextItem?.type === 'channel' && [
          <MenuItem key="settings" onClick={closeContextMenu}>
            <SettingsIcon sx={{ mr: 1 }} />
            Channel Settings
          </MenuItem>,
          <MenuItem key="invite" onClick={closeContextMenu}>
            <BotIcon sx={{ mr: 1 }} />
            Manage Bots
          </MenuItem>
        ]}
      </Menu>

      {/* Create Server Dialog */}
      <Dialog open={showCreateServerDialog} onClose={() => setShowCreateServerDialog(false)}>
        <DialogTitle>Create New Server</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Server Name"
            fullWidth
            variant="outlined"
            value={newServerName}
            onChange={(e) => setNewServerName(e.target.value)}
            placeholder="My Awesome Server"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateServerDialog(false)}>Cancel</Button>
          <Button
            onClick={() => {
              console.log('Create button clicked in dialog');
              createServer();
            }}
            variant="contained"
            disabled={!newServerName.trim()}
          >
            Tạo Server
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Channel Dialog */}
      <Dialog open={showCreateChannelDialog} onClose={() => setShowCreateChannelDialog(false)}>
        <DialogTitle>Create New Channel</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Channel Name"
            fullWidth
            variant="outlined"
            value={newChannelName}
            onChange={(e) => setNewChannelName(e.target.value)}
            placeholder="general"
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateChannelDialog(false)}>Cancel</Button>
          <Button onClick={createChannel} variant="contained" disabled={!newChannelName.trim()}>
            Create
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ServerSidebar;
