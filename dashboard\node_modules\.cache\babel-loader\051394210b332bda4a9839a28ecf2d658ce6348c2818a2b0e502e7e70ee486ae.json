{"ast": null, "code": "/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n  constructor() {\n    this.ws = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n    this.eventHandlers = new Map();\n    this.messageQueue = [];\n    this.heartbeatInterval = null;\n    this.lastHeartbeat = null;\n  }\n\n  /**\n   * Connect to WebSocket server\n   * @param {string} url - WebSocket URL\n   * @param {string} token - Authentication token\n   */\n  connect(url = 'ws://localhost:3003/chat', token = null) {\n    try {\n      console.log('🔌 Connecting to WebSocket:', url);\n      this.ws = new WebSocket(url);\n      this.ws.onopen = () => {\n        console.log('✅ WebSocket connected');\n        this.isConnected = true;\n        this.reconnectAttempts = 0;\n\n        // Send authentication if token provided\n        if (token) {\n          this.authenticate(token);\n        }\n\n        // Start heartbeat\n        this.startHeartbeat();\n\n        // Send queued messages\n        this.flushMessageQueue();\n        this.emit('connected');\n      };\n      this.ws.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('📨 WebSocket message:', data);\n\n          // Handle different message types\n          switch (data.op) {\n            case 0:\n              // Dispatch\n              this.handleDispatch(data);\n              break;\n            case 1:\n              // Heartbeat\n              this.handleHeartbeat(data);\n              break;\n            case 9:\n              // Invalid Session\n              this.handleInvalidSession(data);\n              break;\n            case 10:\n              // Hello\n              this.handleHello(data);\n              break;\n            case 11:\n              // Heartbeat ACK\n              this.handleHeartbeatAck(data);\n              break;\n            default:\n              console.log('Unknown WebSocket opcode:', data.op);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      this.ws.onclose = event => {\n        console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n        this.isConnected = false;\n        this.stopHeartbeat();\n        this.emit('disconnected', {\n          code: event.code,\n          reason: event.reason\n        });\n\n        // Attempt to reconnect\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n          this.scheduleReconnect();\n        }\n      };\n      this.ws.onerror = error => {\n        console.error('❌ WebSocket error:', error);\n        this.emit('error', error);\n      };\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      this.emit('error', error);\n    }\n  }\n\n  /**\n   * Disconnect from WebSocket\n   */\n  disconnect() {\n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n    this.isConnected = false;\n    this.stopHeartbeat();\n  }\n\n  /**\n   * Send authentication message\n   * @param {string} token - Authentication token\n   */\n  authenticate(token) {\n    this.send({\n      op: 2,\n      // Identify\n      d: {\n        token: token,\n        properties: {\n          $os: 'web',\n          $browser: 'browser',\n          $device: 'web'\n        }\n      }\n    });\n  }\n\n  /**\n   * Send message to WebSocket\n   * @param {Object} data - Message data\n   */\n  send(data) {\n    if (this.isConnected && this.ws) {\n      try {\n        this.ws.send(JSON.stringify(data));\n      } catch (error) {\n        console.error('Error sending WebSocket message:', error);\n      }\n    } else {\n      // Queue message for later\n      this.messageQueue.push(data);\n    }\n  }\n\n  /**\n   * Handle dispatch messages (events)\n   * @param {Object} data - Message data\n   */\n  handleDispatch(data) {\n    const {\n      t: eventType,\n      d: eventData\n    } = data;\n    switch (eventType) {\n      case 'MESSAGE_CREATE':\n        this.emit('messageCreate', eventData);\n        break;\n      case 'MESSAGE_UPDATE':\n        this.emit('messageUpdate', eventData);\n        break;\n      case 'MESSAGE_DELETE':\n        this.emit('messageDelete', eventData);\n        break;\n      case 'CHANNEL_CREATE':\n        this.emit('channelCreate', eventData);\n        break;\n      case 'CHANNEL_UPDATE':\n        this.emit('channelUpdate', eventData);\n        break;\n      case 'CHANNEL_DELETE':\n        this.emit('channelDelete', eventData);\n        break;\n      case 'GUILD_CREATE':\n        this.emit('guildCreate', eventData);\n        break;\n      case 'GUILD_UPDATE':\n        this.emit('guildUpdate', eventData);\n        break;\n      case 'GUILD_DELETE':\n        this.emit('guildDelete', eventData);\n        break;\n      case 'READY':\n        this.emit('ready', eventData);\n        break;\n      default:\n        this.emit('event', {\n          type: eventType,\n          data: eventData\n        });\n    }\n  }\n\n  /**\n   * Handle heartbeat\n   * @param {Object} data - Message data\n   */\n  handleHeartbeat(data) {\n    // Send heartbeat response\n    this.send({\n      op: 1,\n      d: this.lastHeartbeat\n    });\n  }\n\n  /**\n   * Handle hello message\n   * @param {Object} data - Message data\n   */\n  handleHello(data) {\n    const {\n      heartbeat_interval\n    } = data.d;\n    this.heartbeatInterval = heartbeat_interval;\n    console.log('💓 Heartbeat interval:', heartbeat_interval);\n  }\n\n  /**\n   * Handle heartbeat acknowledgment\n   * @param {Object} data - Message data\n   */\n  handleHeartbeatAck(data) {\n    this.lastHeartbeat = Date.now();\n  }\n\n  /**\n   * Handle invalid session\n   * @param {Object} data - Message data\n   */\n  handleInvalidSession(data) {\n    console.error('Invalid WebSocket session');\n    this.emit('invalidSession', data);\n  }\n\n  /**\n   * Start heartbeat timer\n   */\n  startHeartbeat() {\n    if (this.heartbeatInterval) {\n      this.heartbeatTimer = setInterval(() => {\n        this.send({\n          op: 1,\n          d: this.lastHeartbeat\n        });\n      }, this.heartbeatInterval);\n    }\n  }\n\n  /**\n   * Stop heartbeat timer\n   */\n  stopHeartbeat() {\n    if (this.heartbeatTimer) {\n      clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = null;\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Flush queued messages\n   */\n  flushMessageQueue() {\n    while (this.messageQueue.length > 0) {\n      const message = this.messageQueue.shift();\n      this.send(message);\n    }\n  }\n\n  /**\n   * Register event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  on(event, handler) {\n    if (!this.eventHandlers.has(event)) {\n      this.eventHandlers.set(event, []);\n    }\n    this.eventHandlers.get(event).push(handler);\n  }\n\n  /**\n   * Remove event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  off(event, handler) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      const index = handlers.indexOf(handler);\n      if (index > -1) {\n        handlers.splice(index, 1);\n      }\n    }\n  }\n\n  /**\n   * Emit event to handlers\n   * @param {string} event - Event name\n   * @param {*} data - Event data\n   */\n  emit(event, data) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error(`Error in WebSocket event handler for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get connection status\n   * @returns {boolean} - Connection status\n   */\n  isConnectedToServer() {\n    return this.isConnected;\n  }\n\n  /**\n   * Get connection statistics\n   * @returns {Object} - Connection stats\n   */\n  getStats() {\n    return {\n      isConnected: this.isConnected,\n      reconnectAttempts: this.reconnectAttempts,\n      lastHeartbeat: this.lastHeartbeat,\n      queuedMessages: this.messageQueue.length\n    };\n  }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\nexport default websocketService;", "map": {"version": 3, "names": ["WebSocketService", "constructor", "ws", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "eventHandlers", "Map", "messageQueue", "heartbeatInterval", "lastHeartbeat", "connect", "url", "token", "console", "log", "WebSocket", "onopen", "authenticate", "startHeartbeat", "flushMessageQueue", "emit", "onmessage", "event", "data", "JSON", "parse", "op", "handleDispatch", "handleHeartbeat", "handleInvalidSession", "handleHello", "handleHeartbeatAck", "error", "onclose", "code", "reason", "stopHeartbeat", "scheduleReconnect", "onerror", "disconnect", "close", "send", "d", "properties", "$os", "$browser", "$device", "stringify", "push", "t", "eventType", "eventData", "type", "heartbeat_interval", "Date", "now", "heartbeatTimer", "setInterval", "clearInterval", "delay", "Math", "pow", "setTimeout", "length", "message", "shift", "on", "handler", "has", "set", "get", "off", "handlers", "index", "indexOf", "splice", "for<PERSON>ach", "isConnectedToServer", "getStats", "queuedMessages", "websocketService"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/services/websocket.js"], "sourcesContent": ["/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n    constructor() {\n        this.ws = null;\n        this.isConnected = false;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectDelay = 1000;\n        this.eventHandlers = new Map();\n        this.messageQueue = [];\n        this.heartbeatInterval = null;\n        this.lastHeartbeat = null;\n    }\n\n    /**\n     * Connect to WebSocket server\n     * @param {string} url - WebSocket URL\n     * @param {string} token - Authentication token\n     */\n    connect(url = 'ws://localhost:3003/chat', token = null) {\n        try {\n            console.log('🔌 Connecting to WebSocket:', url);\n            \n            this.ws = new WebSocket(url);\n            \n            this.ws.onopen = () => {\n                console.log('✅ WebSocket connected');\n                this.isConnected = true;\n                this.reconnectAttempts = 0;\n                \n                // Send authentication if token provided\n                if (token) {\n                    this.authenticate(token);\n                }\n                \n                // Start heartbeat\n                this.startHeartbeat();\n                \n                // Send queued messages\n                this.flushMessageQueue();\n                \n                this.emit('connected');\n            };\n\n            this.ws.onmessage = (event) => {\n                try {\n                    const data = JSON.parse(event.data);\n                    console.log('📨 WebSocket message:', data);\n                    \n                    // Handle different message types\n                    switch (data.op) {\n                        case 0: // Dispatch\n                            this.handleDispatch(data);\n                            break;\n                        case 1: // Heartbeat\n                            this.handleHeartbeat(data);\n                            break;\n                        case 9: // Invalid Session\n                            this.handleInvalidSession(data);\n                            break;\n                        case 10: // Hello\n                            this.handleHello(data);\n                            break;\n                        case 11: // Heartbeat ACK\n                            this.handleHeartbeatAck(data);\n                            break;\n                        default:\n                            console.log('Unknown WebSocket opcode:', data.op);\n                    }\n                } catch (error) {\n                    console.error('Error parsing WebSocket message:', error);\n                }\n            };\n\n            this.ws.onclose = (event) => {\n                console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n                this.isConnected = false;\n                this.stopHeartbeat();\n                \n                this.emit('disconnected', { code: event.code, reason: event.reason });\n                \n                // Attempt to reconnect\n                if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                    this.scheduleReconnect();\n                }\n            };\n\n            this.ws.onerror = (error) => {\n                console.error('❌ WebSocket error:', error);\n                this.emit('error', error);\n            };\n\n        } catch (error) {\n            console.error('Failed to connect to WebSocket:', error);\n            this.emit('error', error);\n        }\n    }\n\n    /**\n     * Disconnect from WebSocket\n     */\n    disconnect() {\n        if (this.ws) {\n            this.ws.close(1000, 'Client disconnect');\n            this.ws = null;\n        }\n        this.isConnected = false;\n        this.stopHeartbeat();\n    }\n\n    /**\n     * Send authentication message\n     * @param {string} token - Authentication token\n     */\n    authenticate(token) {\n        this.send({\n            op: 2, // Identify\n            d: {\n                token: token,\n                properties: {\n                    $os: 'web',\n                    $browser: 'browser',\n                    $device: 'web'\n                }\n            }\n        });\n    }\n\n    /**\n     * Send message to WebSocket\n     * @param {Object} data - Message data\n     */\n    send(data) {\n        if (this.isConnected && this.ws) {\n            try {\n                this.ws.send(JSON.stringify(data));\n            } catch (error) {\n                console.error('Error sending WebSocket message:', error);\n            }\n        } else {\n            // Queue message for later\n            this.messageQueue.push(data);\n        }\n    }\n\n    /**\n     * Handle dispatch messages (events)\n     * @param {Object} data - Message data\n     */\n    handleDispatch(data) {\n        const { t: eventType, d: eventData } = data;\n        \n        switch (eventType) {\n            case 'MESSAGE_CREATE':\n                this.emit('messageCreate', eventData);\n                break;\n            case 'MESSAGE_UPDATE':\n                this.emit('messageUpdate', eventData);\n                break;\n            case 'MESSAGE_DELETE':\n                this.emit('messageDelete', eventData);\n                break;\n            case 'CHANNEL_CREATE':\n                this.emit('channelCreate', eventData);\n                break;\n            case 'CHANNEL_UPDATE':\n                this.emit('channelUpdate', eventData);\n                break;\n            case 'CHANNEL_DELETE':\n                this.emit('channelDelete', eventData);\n                break;\n            case 'GUILD_CREATE':\n                this.emit('guildCreate', eventData);\n                break;\n            case 'GUILD_UPDATE':\n                this.emit('guildUpdate', eventData);\n                break;\n            case 'GUILD_DELETE':\n                this.emit('guildDelete', eventData);\n                break;\n            case 'READY':\n                this.emit('ready', eventData);\n                break;\n            default:\n                this.emit('event', { type: eventType, data: eventData });\n        }\n    }\n\n    /**\n     * Handle heartbeat\n     * @param {Object} data - Message data\n     */\n    handleHeartbeat(data) {\n        // Send heartbeat response\n        this.send({\n            op: 1,\n            d: this.lastHeartbeat\n        });\n    }\n\n    /**\n     * Handle hello message\n     * @param {Object} data - Message data\n     */\n    handleHello(data) {\n        const { heartbeat_interval } = data.d;\n        this.heartbeatInterval = heartbeat_interval;\n        console.log('💓 Heartbeat interval:', heartbeat_interval);\n    }\n\n    /**\n     * Handle heartbeat acknowledgment\n     * @param {Object} data - Message data\n     */\n    handleHeartbeatAck(data) {\n        this.lastHeartbeat = Date.now();\n    }\n\n    /**\n     * Handle invalid session\n     * @param {Object} data - Message data\n     */\n    handleInvalidSession(data) {\n        console.error('Invalid WebSocket session');\n        this.emit('invalidSession', data);\n    }\n\n    /**\n     * Start heartbeat timer\n     */\n    startHeartbeat() {\n        if (this.heartbeatInterval) {\n            this.heartbeatTimer = setInterval(() => {\n                this.send({\n                    op: 1,\n                    d: this.lastHeartbeat\n                });\n            }, this.heartbeatInterval);\n        }\n    }\n\n    /**\n     * Stop heartbeat timer\n     */\n    stopHeartbeat() {\n        if (this.heartbeatTimer) {\n            clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = null;\n        }\n    }\n\n    /**\n     * Schedule reconnection attempt\n     */\n    scheduleReconnect() {\n        this.reconnectAttempts++;\n        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n        \n        console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        \n        setTimeout(() => {\n            if (!this.isConnected) {\n                this.connect();\n            }\n        }, delay);\n    }\n\n    /**\n     * Flush queued messages\n     */\n    flushMessageQueue() {\n        while (this.messageQueue.length > 0) {\n            const message = this.messageQueue.shift();\n            this.send(message);\n        }\n    }\n\n    /**\n     * Register event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    on(event, handler) {\n        if (!this.eventHandlers.has(event)) {\n            this.eventHandlers.set(event, []);\n        }\n        this.eventHandlers.get(event).push(handler);\n    }\n\n    /**\n     * Remove event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    off(event, handler) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            const index = handlers.indexOf(handler);\n            if (index > -1) {\n                handlers.splice(index, 1);\n            }\n        }\n    }\n\n    /**\n     * Emit event to handlers\n     * @param {string} event - Event name\n     * @param {*} data - Event data\n     */\n    emit(event, data) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            handlers.forEach(handler => {\n                try {\n                    handler(data);\n                } catch (error) {\n                    console.error(`Error in WebSocket event handler for ${event}:`, error);\n                }\n            });\n        }\n    }\n\n    /**\n     * Get connection status\n     * @returns {boolean} - Connection status\n     */\n    isConnectedToServer() {\n        return this.isConnected;\n    }\n\n    /**\n     * Get connection statistics\n     * @returns {Object} - Connection stats\n     */\n    getStats() {\n        return {\n            isConnected: this.isConnected,\n            reconnectAttempts: this.reconnectAttempts,\n            lastHeartbeat: this.lastHeartbeat,\n            queuedMessages: this.messageQueue.length\n        };\n    }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\n\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;;EAEA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAACC,GAAG,GAAG,0BAA0B,EAAEC,KAAK,GAAG,IAAI,EAAE;IACpD,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,GAAG,CAAC;MAE/C,IAAI,CAACX,EAAE,GAAG,IAAIe,SAAS,CAACJ,GAAG,CAAC;MAE5B,IAAI,CAACX,EAAE,CAACgB,MAAM,GAAG,MAAM;QACnBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC,IAAI,CAACb,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;;QAE1B;QACA,IAAIU,KAAK,EAAE;UACP,IAAI,CAACK,YAAY,CAACL,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,CAACM,cAAc,CAAC,CAAC;;QAErB;QACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAExB,IAAI,CAACC,IAAI,CAAC,WAAW,CAAC;MAC1B,CAAC;MAED,IAAI,CAACpB,EAAE,CAACqB,SAAS,GAAIC,KAAK,IAAK;QAC3B,IAAI;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,IAAI,CAAC;;UAE1C;UACA,QAAQA,IAAI,CAACG,EAAE;YACX,KAAK,CAAC;cAAE;cACJ,IAAI,CAACC,cAAc,CAACJ,IAAI,CAAC;cACzB;YACJ,KAAK,CAAC;cAAE;cACJ,IAAI,CAACK,eAAe,CAACL,IAAI,CAAC;cAC1B;YACJ,KAAK,CAAC;cAAE;cACJ,IAAI,CAACM,oBAAoB,CAACN,IAAI,CAAC;cAC/B;YACJ,KAAK,EAAE;cAAE;cACL,IAAI,CAACO,WAAW,CAACP,IAAI,CAAC;cACtB;YACJ,KAAK,EAAE;cAAE;cACL,IAAI,CAACQ,kBAAkB,CAACR,IAAI,CAAC;cAC7B;YACJ;cACIV,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAES,IAAI,CAACG,EAAE,CAAC;UACzD;QACJ,CAAC,CAAC,OAAOM,KAAK,EAAE;UACZnB,OAAO,CAACmB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D;MACJ,CAAC;MAED,IAAI,CAAChC,EAAE,CAACiC,OAAO,GAAIX,KAAK,IAAK;QACzBT,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEQ,KAAK,CAACY,IAAI,EAAEZ,KAAK,CAACa,MAAM,CAAC;QACnE,IAAI,CAAClC,WAAW,GAAG,KAAK;QACxB,IAAI,CAACmC,aAAa,CAAC,CAAC;QAEpB,IAAI,CAAChB,IAAI,CAAC,cAAc,EAAE;UAAEc,IAAI,EAAEZ,KAAK,CAACY,IAAI;UAAEC,MAAM,EAAEb,KAAK,CAACa;QAAO,CAAC,CAAC;;QAErE;QACA,IAAI,IAAI,CAACjC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;UACpD,IAAI,CAACkC,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MAED,IAAI,CAACrC,EAAE,CAACsC,OAAO,GAAIN,KAAK,IAAK;QACzBnB,OAAO,CAACmB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,IAAI,CAACZ,IAAI,CAAC,OAAO,EAAEY,KAAK,CAAC;MAC7B,CAAC;IAEL,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZnB,OAAO,CAACmB,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAACZ,IAAI,CAAC,OAAO,EAAEY,KAAK,CAAC;IAC7B;EACJ;;EAEA;AACJ;AACA;EACIO,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACvC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACwC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MACxC,IAAI,CAACxC,EAAE,GAAG,IAAI;IAClB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACmC,aAAa,CAAC,CAAC;EACxB;;EAEA;AACJ;AACA;AACA;EACInB,YAAYA,CAACL,KAAK,EAAE;IAChB,IAAI,CAAC6B,IAAI,CAAC;MACNf,EAAE,EAAE,CAAC;MAAE;MACPgB,CAAC,EAAE;QACC9B,KAAK,EAAEA,KAAK;QACZ+B,UAAU,EAAE;UACRC,GAAG,EAAE,KAAK;UACVC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE;QACb;MACJ;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIL,IAAIA,CAAClB,IAAI,EAAE;IACP,IAAI,IAAI,CAACtB,WAAW,IAAI,IAAI,CAACD,EAAE,EAAE;MAC7B,IAAI;QACA,IAAI,CAACA,EAAE,CAACyC,IAAI,CAACjB,IAAI,CAACuB,SAAS,CAACxB,IAAI,CAAC,CAAC;MACtC,CAAC,CAAC,OAAOS,KAAK,EAAE;QACZnB,OAAO,CAACmB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC5D;IACJ,CAAC,MAAM;MACH;MACA,IAAI,CAACzB,YAAY,CAACyC,IAAI,CAACzB,IAAI,CAAC;IAChC;EACJ;;EAEA;AACJ;AACA;AACA;EACII,cAAcA,CAACJ,IAAI,EAAE;IACjB,MAAM;MAAE0B,CAAC,EAAEC,SAAS;MAAER,CAAC,EAAES;IAAU,CAAC,GAAG5B,IAAI;IAE3C,QAAQ2B,SAAS;MACb,KAAK,gBAAgB;QACjB,IAAI,CAAC9B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAC/B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAC/B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAC/B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAC/B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAC/B,IAAI,CAAC,eAAe,EAAE+B,SAAS,CAAC;QACrC;MACJ,KAAK,cAAc;QACf,IAAI,CAAC/B,IAAI,CAAC,aAAa,EAAE+B,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAAC/B,IAAI,CAAC,aAAa,EAAE+B,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAAC/B,IAAI,CAAC,aAAa,EAAE+B,SAAS,CAAC;QACnC;MACJ,KAAK,OAAO;QACR,IAAI,CAAC/B,IAAI,CAAC,OAAO,EAAE+B,SAAS,CAAC;QAC7B;MACJ;QACI,IAAI,CAAC/B,IAAI,CAAC,OAAO,EAAE;UAAEgC,IAAI,EAAEF,SAAS;UAAE3B,IAAI,EAAE4B;QAAU,CAAC,CAAC;IAChE;EACJ;;EAEA;AACJ;AACA;AACA;EACIvB,eAAeA,CAACL,IAAI,EAAE;IAClB;IACA,IAAI,CAACkB,IAAI,CAAC;MACNf,EAAE,EAAE,CAAC;MACLgB,CAAC,EAAE,IAAI,CAACjC;IACZ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIqB,WAAWA,CAACP,IAAI,EAAE;IACd,MAAM;MAAE8B;IAAmB,CAAC,GAAG9B,IAAI,CAACmB,CAAC;IACrC,IAAI,CAAClC,iBAAiB,GAAG6C,kBAAkB;IAC3CxC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuC,kBAAkB,CAAC;EAC7D;;EAEA;AACJ;AACA;AACA;EACItB,kBAAkBA,CAACR,IAAI,EAAE;IACrB,IAAI,CAACd,aAAa,GAAG6C,IAAI,CAACC,GAAG,CAAC,CAAC;EACnC;;EAEA;AACJ;AACA;AACA;EACI1B,oBAAoBA,CAACN,IAAI,EAAE;IACvBV,OAAO,CAACmB,KAAK,CAAC,2BAA2B,CAAC;IAC1C,IAAI,CAACZ,IAAI,CAAC,gBAAgB,EAAEG,IAAI,CAAC;EACrC;;EAEA;AACJ;AACA;EACIL,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACV,iBAAiB,EAAE;MACxB,IAAI,CAACgD,cAAc,GAAGC,WAAW,CAAC,MAAM;QACpC,IAAI,CAAChB,IAAI,CAAC;UACNf,EAAE,EAAE,CAAC;UACLgB,CAAC,EAAE,IAAI,CAACjC;QACZ,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAACD,iBAAiB,CAAC;IAC9B;EACJ;;EAEA;AACJ;AACA;EACI4B,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACoB,cAAc,EAAE;MACrBE,aAAa,CAAC,IAAI,CAACF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;;EAEA;AACJ;AACA;EACInB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnC,iBAAiB,EAAE;IACxB,MAAMyD,KAAK,GAAG,IAAI,CAACvD,cAAc,GAAGwD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC3D,iBAAiB,GAAG,CAAC,CAAC;IAE3EW,OAAO,CAACC,GAAG,CAAC,sBAAsB6C,KAAK,eAAe,IAAI,CAACzD,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAE7G2D,UAAU,CAAC,MAAM;MACb,IAAI,CAAC,IAAI,CAAC7D,WAAW,EAAE;QACnB,IAAI,CAACS,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,EAAEiD,KAAK,CAAC;EACb;;EAEA;AACJ;AACA;EACIxC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACZ,YAAY,CAACwD,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMC,OAAO,GAAG,IAAI,CAACzD,YAAY,CAAC0D,KAAK,CAAC,CAAC;MACzC,IAAI,CAACxB,IAAI,CAACuB,OAAO,CAAC;IACtB;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIE,EAAEA,CAAC5C,KAAK,EAAE6C,OAAO,EAAE;IACf,IAAI,CAAC,IAAI,CAAC9D,aAAa,CAAC+D,GAAG,CAAC9C,KAAK,CAAC,EAAE;MAChC,IAAI,CAACjB,aAAa,CAACgE,GAAG,CAAC/C,KAAK,EAAE,EAAE,CAAC;IACrC;IACA,IAAI,CAACjB,aAAa,CAACiE,GAAG,CAAChD,KAAK,CAAC,CAAC0B,IAAI,CAACmB,OAAO,CAAC;EAC/C;;EAEA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACjD,KAAK,EAAE6C,OAAO,EAAE;IAChB,MAAMK,QAAQ,GAAG,IAAI,CAACnE,aAAa,CAACiE,GAAG,CAAChD,KAAK,CAAC;IAC9C,IAAIkD,QAAQ,EAAE;MACV,MAAMC,KAAK,GAAGD,QAAQ,CAACE,OAAO,CAACP,OAAO,CAAC;MACvC,IAAIM,KAAK,GAAG,CAAC,CAAC,EAAE;QACZD,QAAQ,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC7B;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIrD,IAAIA,CAACE,KAAK,EAAEC,IAAI,EAAE;IACd,MAAMiD,QAAQ,GAAG,IAAI,CAACnE,aAAa,CAACiE,GAAG,CAAChD,KAAK,CAAC;IAC9C,IAAIkD,QAAQ,EAAE;MACVA,QAAQ,CAACI,OAAO,CAACT,OAAO,IAAI;QACxB,IAAI;UACAA,OAAO,CAAC5C,IAAI,CAAC;QACjB,CAAC,CAAC,OAAOS,KAAK,EAAE;UACZnB,OAAO,CAACmB,KAAK,CAAC,wCAAwCV,KAAK,GAAG,EAAEU,KAAK,CAAC;QAC1E;MACJ,CAAC,CAAC;IACN;EACJ;;EAEA;AACJ;AACA;AACA;EACI6C,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC5E,WAAW;EAC3B;;EAEA;AACJ;AACA;AACA;EACI6E,QAAQA,CAAA,EAAG;IACP,OAAO;MACH7E,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCO,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCsE,cAAc,EAAE,IAAI,CAACxE,YAAY,CAACwD;IACtC,CAAC;EACL;AACJ;;AAEA;AACA,MAAMiB,gBAAgB,GAAG,IAAIlF,gBAAgB,CAAC,CAAC;AAE/C,eAAekF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}