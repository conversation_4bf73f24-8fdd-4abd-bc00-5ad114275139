{"ast": null, "code": "/**\n * The base implementation of `_.findIndex` and `_.findLastIndex` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {number} fromIndex The index to search from.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length,\n    index = fromIndex + (fromRight ? 1 : -1);\n  while (fromRight ? index-- : ++index < length) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\nmodule.exports = baseFindIndex;", "map": {"version": 3, "names": ["baseFindIndex", "array", "predicate", "fromIndex", "fromRight", "length", "index", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_baseFindIndex.js"], "sourcesContent": ["/**\n * The base implementation of `_.findIndex` and `_.findLastIndex` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} predicate The function invoked per iteration.\n * @param {number} fromIndex The index to search from.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseFindIndex(array, predicate, fromIndex, fromRight) {\n  var length = array.length,\n      index = fromIndex + (fromRight ? 1 : -1);\n\n  while ((fromRight ? index-- : ++index < length)) {\n    if (predicate(array[index], index, array)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = baseFindIndex;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAACC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC7D,IAAIC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,KAAK,GAAGH,SAAS,IAAIC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAE5C,OAAQA,SAAS,GAAGE,KAAK,EAAE,GAAG,EAAEA,KAAK,GAAGD,MAAM,EAAG;IAC/C,IAAIH,SAAS,CAACD,KAAK,CAACK,KAAK,CAAC,EAAEA,KAAK,EAAEL,KAAK,CAAC,EAAE;MACzC,OAAOK,KAAK;IACd;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEAC,MAAM,CAACC,OAAO,GAAGR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}