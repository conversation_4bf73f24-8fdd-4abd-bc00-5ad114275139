{"ast": null, "code": "var epsilon2 = 1e-12;\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\nexport default (function zoomRho(rho, rho2, rho4) {\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0],\n      uy0 = p0[1],\n      w0 = p0[2],\n      ux1 = p1[0],\n      uy1 = p1[1],\n      w1 = p1[2],\n      dx = ux1 - ux0,\n      dy = uy1 - uy0,\n      d2 = dx * dx + dy * dy,\n      i,\n      S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function (t) {\n        return [ux0 + t * dx, uy0 + t * dy, w0 * Math.exp(rho * t * S)];\n      };\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n        b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n        b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n        r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n        r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function (t) {\n        var s = t * S,\n          coshr0 = cosh(r0),\n          u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [ux0 + u * dx, uy0 + u * dy, w0 * coshr0 / cosh(rho * s + r0)];\n      };\n    }\n    i.duration = S * 1000 * rho / Math.SQRT2;\n    return i;\n  }\n  zoom.rho = function (_) {\n    var _1 = Math.max(1e-3, +_),\n      _2 = _1 * _1,\n      _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n  return zoom;\n})(Math.SQRT2, 2, 4);", "map": {"version": 3, "names": ["epsilon2", "cosh", "x", "Math", "exp", "sinh", "tanh", "zoomRho", "rho", "rho2", "rho4", "zoom", "p0", "p1", "ux0", "uy0", "w0", "ux1", "uy1", "w1", "dx", "dy", "d2", "i", "S", "log", "t", "d1", "sqrt", "b0", "b1", "r0", "r1", "s", "coshr0", "u", "duration", "SQRT2", "_", "_1", "max", "_2", "_4"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-interpolate/src/zoom.js"], "sourcesContent": ["var epsilon2 = 1e-12;\n\nfunction cosh(x) {\n  return ((x = Math.exp(x)) + 1 / x) / 2;\n}\n\nfunction sinh(x) {\n  return ((x = Math.exp(x)) - 1 / x) / 2;\n}\n\nfunction tanh(x) {\n  return ((x = Math.exp(2 * x)) - 1) / (x + 1);\n}\n\nexport default (function zoomRho(rho, rho2, rho4) {\n\n  // p0 = [ux0, uy0, w0]\n  // p1 = [ux1, uy1, w1]\n  function zoom(p0, p1) {\n    var ux0 = p0[0], uy0 = p0[1], w0 = p0[2],\n        ux1 = p1[0], uy1 = p1[1], w1 = p1[2],\n        dx = ux1 - ux0,\n        dy = uy1 - uy0,\n        d2 = dx * dx + dy * dy,\n        i,\n        S;\n\n    // Special case for u0 ≅ u1.\n    if (d2 < epsilon2) {\n      S = Math.log(w1 / w0) / rho;\n      i = function(t) {\n        return [\n          ux0 + t * dx,\n          uy0 + t * dy,\n          w0 * Math.exp(rho * t * S)\n        ];\n      }\n    }\n\n    // General case.\n    else {\n      var d1 = Math.sqrt(d2),\n          b0 = (w1 * w1 - w0 * w0 + rho4 * d2) / (2 * w0 * rho2 * d1),\n          b1 = (w1 * w1 - w0 * w0 - rho4 * d2) / (2 * w1 * rho2 * d1),\n          r0 = Math.log(Math.sqrt(b0 * b0 + 1) - b0),\n          r1 = Math.log(Math.sqrt(b1 * b1 + 1) - b1);\n      S = (r1 - r0) / rho;\n      i = function(t) {\n        var s = t * S,\n            coshr0 = cosh(r0),\n            u = w0 / (rho2 * d1) * (coshr0 * tanh(rho * s + r0) - sinh(r0));\n        return [\n          ux0 + u * dx,\n          uy0 + u * dy,\n          w0 * coshr0 / cosh(rho * s + r0)\n        ];\n      }\n    }\n\n    i.duration = S * 1000 * rho / Math.SQRT2;\n\n    return i;\n  }\n\n  zoom.rho = function(_) {\n    var _1 = Math.max(1e-3, +_), _2 = _1 * _1, _4 = _2 * _2;\n    return zoomRho(_1, _2, _4);\n  };\n\n  return zoom;\n})(Math.SQRT2, 2, 4);\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,KAAK;AAEpB,SAASC,IAAIA,CAACC,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,CAAC;AACxC;AAEA,SAASG,IAAIA,CAACH,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,IAAI,CAAC;AACxC;AAEA,SAASI,IAAIA,CAACJ,CAAC,EAAE;EACf,OAAO,CAAC,CAACA,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;AAC9C;AAEA,eAAe,CAAC,SAASK,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAEhD;EACA;EACA,SAASC,IAAIA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACpB,IAAIC,GAAG,GAAGF,EAAE,CAAC,CAAC,CAAC;MAAEG,GAAG,GAAGH,EAAE,CAAC,CAAC,CAAC;MAAEI,EAAE,GAAGJ,EAAE,CAAC,CAAC,CAAC;MACpCK,GAAG,GAAGJ,EAAE,CAAC,CAAC,CAAC;MAAEK,GAAG,GAAGL,EAAE,CAAC,CAAC,CAAC;MAAEM,EAAE,GAAGN,EAAE,CAAC,CAAC,CAAC;MACpCO,EAAE,GAAGH,GAAG,GAAGH,GAAG;MACdO,EAAE,GAAGH,GAAG,GAAGH,GAAG;MACdO,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;MACtBE,CAAC;MACDC,CAAC;;IAEL;IACA,IAAIF,EAAE,GAAGtB,QAAQ,EAAE;MACjBwB,CAAC,GAAGrB,IAAI,CAACsB,GAAG,CAACN,EAAE,GAAGH,EAAE,CAAC,GAAGR,GAAG;MAC3Be,CAAC,GAAG,SAAAA,CAASG,CAAC,EAAE;QACd,OAAO,CACLZ,GAAG,GAAGY,CAAC,GAAGN,EAAE,EACZL,GAAG,GAAGW,CAAC,GAAGL,EAAE,EACZL,EAAE,GAAGb,IAAI,CAACC,GAAG,CAACI,GAAG,GAAGkB,CAAC,GAAGF,CAAC,CAAC,CAC3B;MACH,CAAC;IACH;;IAEA;IAAA,KACK;MACH,IAAIG,EAAE,GAAGxB,IAAI,CAACyB,IAAI,CAACN,EAAE,CAAC;QAClBO,EAAE,GAAG,CAACV,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGN,IAAI,GAAGY,EAAE,KAAK,CAAC,GAAGN,EAAE,GAAGP,IAAI,GAAGkB,EAAE,CAAC;QAC3DG,EAAE,GAAG,CAACX,EAAE,GAAGA,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGN,IAAI,GAAGY,EAAE,KAAK,CAAC,GAAGH,EAAE,GAAGV,IAAI,GAAGkB,EAAE,CAAC;QAC3DI,EAAE,GAAG5B,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACyB,IAAI,CAACC,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,CAAC;QAC1CG,EAAE,GAAG7B,IAAI,CAACsB,GAAG,CAACtB,IAAI,CAACyB,IAAI,CAACE,EAAE,GAAGA,EAAE,GAAG,CAAC,CAAC,GAAGA,EAAE,CAAC;MAC9CN,CAAC,GAAG,CAACQ,EAAE,GAAGD,EAAE,IAAIvB,GAAG;MACnBe,CAAC,GAAG,SAAAA,CAASG,CAAC,EAAE;QACd,IAAIO,CAAC,GAAGP,CAAC,GAAGF,CAAC;UACTU,MAAM,GAAGjC,IAAI,CAAC8B,EAAE,CAAC;UACjBI,CAAC,GAAGnB,EAAE,IAAIP,IAAI,GAAGkB,EAAE,CAAC,IAAIO,MAAM,GAAG5B,IAAI,CAACE,GAAG,GAAGyB,CAAC,GAAGF,EAAE,CAAC,GAAG1B,IAAI,CAAC0B,EAAE,CAAC,CAAC;QACnE,OAAO,CACLjB,GAAG,GAAGqB,CAAC,GAAGf,EAAE,EACZL,GAAG,GAAGoB,CAAC,GAAGd,EAAE,EACZL,EAAE,GAAGkB,MAAM,GAAGjC,IAAI,CAACO,GAAG,GAAGyB,CAAC,GAAGF,EAAE,CAAC,CACjC;MACH,CAAC;IACH;IAEAR,CAAC,CAACa,QAAQ,GAAGZ,CAAC,GAAG,IAAI,GAAGhB,GAAG,GAAGL,IAAI,CAACkC,KAAK;IAExC,OAAOd,CAAC;EACV;EAEAZ,IAAI,CAACH,GAAG,GAAG,UAAS8B,CAAC,EAAE;IACrB,IAAIC,EAAE,GAAGpC,IAAI,CAACqC,GAAG,CAAC,IAAI,EAAE,CAACF,CAAC,CAAC;MAAEG,EAAE,GAAGF,EAAE,GAAGA,EAAE;MAAEG,EAAE,GAAGD,EAAE,GAAGA,EAAE;IACvD,OAAOlC,OAAO,CAACgC,EAAE,EAAEE,EAAE,EAAEC,EAAE,CAAC;EAC5B,CAAC;EAED,OAAO/B,IAAI;AACb,CAAC,EAAER,IAAI,CAACkC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}