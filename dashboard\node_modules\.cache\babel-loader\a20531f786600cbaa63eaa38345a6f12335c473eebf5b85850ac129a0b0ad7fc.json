{"ast": null, "code": "var getOwnPropertyNames = Object.getOwnPropertyNames,\n  getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */\nfunction combineComparators(comparatorA, comparatorB) {\n  return function isEqual(a, b, state) {\n    return comparatorA(a, b, state) && comparatorB(a, b, state);\n  };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nfunction createIsCircular(areItemsEqual) {\n  return function isCircular(a, b, state) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, state);\n    }\n    var cache = state.cache;\n    var cachedA = cache.get(a);\n    var cachedB = cache.get(b);\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n    cache.set(a, b);\n    cache.set(b, a);\n    var result = areItemsEqual(a, b, state);\n    cache.delete(a);\n    cache.delete(b);\n    return result;\n  };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nfunction getStrictProperties(object) {\n  return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */\nvar hasOwn = Object.hasOwn || function (object, property) {\n  return hasOwnProperty.call(object, property);\n};\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nfunction sameValueZeroEqual(a, b) {\n  return a === b || !a && !b && a !== a && b !== b;\n}\nvar PREACT_VNODE = '__v';\nvar PREACT_OWNER = '__o';\nvar REACT_OWNER = '_owner';\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor,\n  keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */\nfunction areArraysEqual(a, b, state) {\n  var index = a.length;\n  if (b.length !== index) {\n    return false;\n  }\n  while (index-- > 0) {\n    if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */\nfunction areDatesEqual(a, b) {\n  return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the errors passed are equal in value.\n */\nfunction areErrorsEqual(a, b) {\n  return a.name === b.name && a.message === b.message && a.cause === b.cause && a.stack === b.stack;\n}\n/**\n * Whether the functions passed are equal in value.\n */\nfunction areFunctionsEqual(a, b) {\n  return a === b;\n}\n/**\n * Whether the `Map`s are equal in value.\n */\nfunction areMapsEqual(a, b, state) {\n  var size = a.size;\n  if (size !== b.size) {\n    return false;\n  }\n  if (!size) {\n    return true;\n  }\n  var matchedIndices = new Array(size);\n  var aIterable = a.entries();\n  var aResult;\n  var bResult;\n  var index = 0;\n  while (aResult = aIterable.next()) {\n    if (aResult.done) {\n      break;\n    }\n    var bIterable = b.entries();\n    var hasMatch = false;\n    var matchIndex = 0;\n    while (bResult = bIterable.next()) {\n      if (bResult.done) {\n        break;\n      }\n      if (matchedIndices[matchIndex]) {\n        matchIndex++;\n        continue;\n      }\n      var aEntry = aResult.value;\n      var bEntry = bResult.value;\n      if (state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) && state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n      matchIndex++;\n    }\n    if (!hasMatch) {\n      return false;\n    }\n    index++;\n  }\n  return true;\n}\n/**\n * Whether the numbers are equal in value.\n */\nvar areNumbersEqual = sameValueZeroEqual;\n/**\n * Whether the objects are equal in value.\n */\nfunction areObjectsEqual(a, b, state) {\n  var properties = keys(a);\n  var index = properties.length;\n  if (keys(b).length !== index) {\n    return false;\n  }\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isPropertyEqual(a, b, state, properties[index])) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nfunction areObjectsEqualStrict(a, b, state) {\n  var properties = getStrictProperties(a);\n  var index = properties.length;\n  if (getStrictProperties(b).length !== index) {\n    return false;\n  }\n  var property;\n  var descriptorA;\n  var descriptorB;\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    property = properties[index];\n    if (!isPropertyEqual(a, b, state, property)) {\n      return false;\n    }\n    descriptorA = getOwnPropertyDescriptor(a, property);\n    descriptorB = getOwnPropertyDescriptor(b, property);\n    if ((descriptorA || descriptorB) && (!descriptorA || !descriptorB || descriptorA.configurable !== descriptorB.configurable || descriptorA.enumerable !== descriptorB.enumerable || descriptorA.writable !== descriptorB.writable)) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nfunction arePrimitiveWrappersEqual(a, b) {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */\nfunction areRegExpsEqual(a, b) {\n  return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */\nfunction areSetsEqual(a, b, state) {\n  var size = a.size;\n  if (size !== b.size) {\n    return false;\n  }\n  if (!size) {\n    return true;\n  }\n  var matchedIndices = new Array(size);\n  var aIterable = a.values();\n  var aResult;\n  var bResult;\n  while (aResult = aIterable.next()) {\n    if (aResult.done) {\n      break;\n    }\n    var bIterable = b.values();\n    var hasMatch = false;\n    var matchIndex = 0;\n    while (bResult = bIterable.next()) {\n      if (bResult.done) {\n        break;\n      }\n      if (!matchedIndices[matchIndex] && state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state)) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n      matchIndex++;\n    }\n    if (!hasMatch) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */\nfunction areTypedArraysEqual(a, b) {\n  var index = a.length;\n  if (b.length !== index) {\n    return false;\n  }\n  while (index-- > 0) {\n    if (a[index] !== b[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Whether the URL instances are equal in value.\n */\nfunction areUrlsEqual(a, b) {\n  return a.hostname === b.hostname && a.pathname === b.pathname && a.protocol === b.protocol && a.port === b.port && a.hash === b.hash && a.username === b.username && a.password === b.password;\n}\nfunction isPropertyEqual(a, b, state, property) {\n  if ((property === REACT_OWNER || property === PREACT_OWNER || property === PREACT_VNODE) && (a.$$typeof || b.$$typeof)) {\n    return true;\n  }\n  return hasOwn(b, property) && state.equals(a[property], b[property], property, property, a, b, state);\n}\nvar ARGUMENTS_TAG = '[object Arguments]';\nvar BOOLEAN_TAG = '[object Boolean]';\nvar DATE_TAG = '[object Date]';\nvar ERROR_TAG = '[object Error]';\nvar MAP_TAG = '[object Map]';\nvar NUMBER_TAG = '[object Number]';\nvar OBJECT_TAG = '[object Object]';\nvar REG_EXP_TAG = '[object RegExp]';\nvar SET_TAG = '[object Set]';\nvar STRING_TAG = '[object String]';\nvar URL_TAG = '[object URL]';\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView ? ArrayBuffer.isView : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nfunction createEqualityComparator(_a) {\n  var areArraysEqual = _a.areArraysEqual,\n    areDatesEqual = _a.areDatesEqual,\n    areErrorsEqual = _a.areErrorsEqual,\n    areFunctionsEqual = _a.areFunctionsEqual,\n    areMapsEqual = _a.areMapsEqual,\n    areNumbersEqual = _a.areNumbersEqual,\n    areObjectsEqual = _a.areObjectsEqual,\n    arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual,\n    areRegExpsEqual = _a.areRegExpsEqual,\n    areSetsEqual = _a.areSetsEqual,\n    areTypedArraysEqual = _a.areTypedArraysEqual,\n    areUrlsEqual = _a.areUrlsEqual;\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  return function comparator(a, b, state) {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n    // If either of the items are nullish and fail the strictly equal check\n    // above, then they must be unequal.\n    if (a == null || b == null) {\n      return false;\n    }\n    var type = typeof a;\n    if (type !== typeof b) {\n      return false;\n    }\n    if (type !== 'object') {\n      if (type === 'number') {\n        return areNumbersEqual(a, b, state);\n      }\n      if (type === 'function') {\n        return areFunctionsEqual(a, b, state);\n      }\n      // If a primitive value that is not strictly equal, it must be unequal.\n      return false;\n    }\n    var constructor = a.constructor;\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n    // Constructors should match, otherwise there is potential for false positives\n    // between class and subclass or custom object and POJO.\n    if (constructor !== b.constructor) {\n      return false;\n    }\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid capturing the string tag.\n    if (constructor === Object) {\n      return areObjectsEqual(a, b, state);\n    }\n    // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n    // the string tag or doing an `instanceof` check.\n    if (isArray(a)) {\n      return areArraysEqual(a, b, state);\n    }\n    // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n    // capturing the string tag or comparing against all possible constructors.\n    if (isTypedArray != null && isTypedArray(a)) {\n      return areTypedArraysEqual(a, b, state);\n    }\n    // Try to fast-path equality checks for other complex object types in the\n    // same realm to avoid capturing the string tag. Strict equality is used\n    // instead of `instanceof` because it is more performant for the common\n    // use-case. If someone is subclassing a native class, it will be handled\n    // with the string tag comparison.\n    if (constructor === Date) {\n      return areDatesEqual(a, b, state);\n    }\n    if (constructor === RegExp) {\n      return areRegExpsEqual(a, b, state);\n    }\n    if (constructor === Map) {\n      return areMapsEqual(a, b, state);\n    }\n    if (constructor === Set) {\n      return areSetsEqual(a, b, state);\n    }\n    // Since this is a custom object, capture the string tag to determing its type.\n    // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n    var tag = getTag(a);\n    if (tag === DATE_TAG) {\n      return areDatesEqual(a, b, state);\n    }\n    // For RegExp, the properties are not enumerable, and therefore will give false positives if\n    // tested like a standard object.\n    if (tag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, state);\n    }\n    if (tag === MAP_TAG) {\n      return areMapsEqual(a, b, state);\n    }\n    if (tag === SET_TAG) {\n      return areSetsEqual(a, b, state);\n    }\n    if (tag === OBJECT_TAG) {\n      // The exception for value comparison is custom `Promise`-like class instances. These should\n      // be treated the same as standard `Promise` objects, which means strict equality, and if\n      // it reaches this point then that strict equality comparison has already failed.\n      return typeof a.then !== 'function' && typeof b.then !== 'function' && areObjectsEqual(a, b, state);\n    }\n    // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === URL_TAG) {\n      return areUrlsEqual(a, b, state);\n    }\n    // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === ERROR_TAG) {\n      return areErrorsEqual(a, b, state);\n    }\n    // If an arguments tag, it should be treated as a standard object.\n    if (tag === ARGUMENTS_TAG) {\n      return areObjectsEqual(a, b, state);\n    }\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n      return arePrimitiveWrappersEqual(a, b, state);\n    }\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  };\n}\n/**\n * Create the configuration object used for building comparators.\n */\nfunction createEqualityComparatorConfig(_a) {\n  var circular = _a.circular,\n    createCustomConfig = _a.createCustomConfig,\n    strict = _a.strict;\n  var config = {\n    areArraysEqual: strict ? areObjectsEqualStrict : areArraysEqual,\n    areDatesEqual: areDatesEqual,\n    areErrorsEqual: areErrorsEqual,\n    areFunctionsEqual: areFunctionsEqual,\n    areMapsEqual: strict ? combineComparators(areMapsEqual, areObjectsEqualStrict) : areMapsEqual,\n    areNumbersEqual: areNumbersEqual,\n    areObjectsEqual: strict ? areObjectsEqualStrict : areObjectsEqual,\n    arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n    areRegExpsEqual: areRegExpsEqual,\n    areSetsEqual: strict ? combineComparators(areSetsEqual, areObjectsEqualStrict) : areSetsEqual,\n    areTypedArraysEqual: strict ? areObjectsEqualStrict : areTypedArraysEqual,\n    areUrlsEqual: areUrlsEqual\n  };\n  if (createCustomConfig) {\n    config = assign({}, config, createCustomConfig(config));\n  }\n  if (circular) {\n    var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n    var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n    var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n    var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n    config = assign({}, config, {\n      areArraysEqual: areArraysEqual$1,\n      areMapsEqual: areMapsEqual$1,\n      areObjectsEqual: areObjectsEqual$1,\n      areSetsEqual: areSetsEqual$1\n    });\n  }\n  return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nfunction createInternalEqualityComparator(compare) {\n  return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n    return compare(a, b, state);\n  };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nfunction createIsEqual(_a) {\n  var circular = _a.circular,\n    comparator = _a.comparator,\n    createState = _a.createState,\n    equals = _a.equals,\n    strict = _a.strict;\n  if (createState) {\n    return function isEqual(a, b) {\n      var _a = createState(),\n        _b = _a.cache,\n        cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b,\n        meta = _a.meta;\n      return comparator(a, b, {\n        cache: cache,\n        equals: equals,\n        meta: meta,\n        strict: strict\n      });\n    };\n  }\n  if (circular) {\n    return function isEqual(a, b) {\n      return comparator(a, b, {\n        cache: new WeakMap(),\n        equals: equals,\n        meta: undefined,\n        strict: strict\n      });\n    };\n  }\n  var state = {\n    cache: undefined,\n    equals: equals,\n    meta: undefined,\n    strict: strict\n  };\n  return function isEqual(a, b) {\n    return comparator(a, b, state);\n  };\n}\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nvar deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nvar strictDeepEqual = createCustomEqual({\n  strict: true\n});\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nvar circularDeepEqual = createCustomEqual({\n  circular: true\n});\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularDeepEqual = createCustomEqual({\n  circular: true,\n  strict: true\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nvar shallowEqual = createCustomEqual({\n  createInternalComparator: function () {\n    return sameValueZeroEqual;\n  }\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nvar strictShallowEqual = createCustomEqual({\n  strict: true,\n  createInternalComparator: function () {\n    return sameValueZeroEqual;\n  }\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nvar circularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: function () {\n    return sameValueZeroEqual;\n  }\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: function () {\n    return sameValueZeroEqual;\n  },\n  strict: true\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nfunction createCustomEqual(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.circular,\n    circular = _a === void 0 ? false : _a,\n    createCustomInternalComparator = options.createInternalComparator,\n    createState = options.createState,\n    _b = options.strict,\n    strict = _b === void 0 ? false : _b;\n  var config = createEqualityComparatorConfig(options);\n  var comparator = createEqualityComparator(config);\n  var equals = createCustomInternalComparator ? createCustomInternalComparator(comparator) : createInternalEqualityComparator(comparator);\n  return createIsEqual({\n    circular: circular,\n    comparator: comparator,\n    createState: createState,\n    equals: equals,\n    strict: strict\n  });\n}\nexport { circularDeepEqual, circularShallowEqual, createCustomEqual, deepEqual, sameValueZeroEqual, shallowEqual, strictCircularDeepEqual, strictCircularShallowEqual, strictDeepEqual, strictShallowEqual };", "map": {"version": 3, "names": ["getOwnPropertyNames", "Object", "getOwnPropertySymbols", "hasOwnProperty", "prototype", "combineComparators", "comparatorA", "comparatorB", "isEqual", "a", "b", "state", "createIsCircular", "areItemsEqual", "isCircular", "cache", "cachedA", "get", "cachedB", "set", "result", "delete", "getStrictProperties", "object", "concat", "hasOwn", "property", "call", "sameValueZeroEqual", "PREACT_VNODE", "PREACT_OWNER", "REACT_OWNER", "getOwnPropertyDescriptor", "keys", "areArraysEqual", "index", "length", "equals", "areDatesEqual", "getTime", "areErrorsEqual", "name", "message", "cause", "stack", "areFunctionsEqual", "areMapsEqual", "size", "matchedIndices", "Array", "aIterable", "entries", "aResult", "bResult", "next", "done", "bIterable", "hasMatch", "matchIndex", "aEntry", "value", "b<PERSON><PERSON><PERSON>", "areNumbersEqual", "areObjectsEqual", "properties", "isPropertyEqual", "areObjectsEqualStrict", "descriptorA", "descriptorB", "configurable", "enumerable", "writable", "arePrimitiveWrappersEqual", "valueOf", "areRegExpsEqual", "source", "flags", "areSetsEqual", "values", "areTypedArraysEqual", "areUrlsEqual", "hostname", "pathname", "protocol", "port", "hash", "username", "password", "$$typeof", "ARGUMENTS_TAG", "BOOLEAN_TAG", "DATE_TAG", "ERROR_TAG", "MAP_TAG", "NUMBER_TAG", "OBJECT_TAG", "REG_EXP_TAG", "SET_TAG", "STRING_TAG", "URL_TAG", "isArray", "isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "assign", "getTag", "toString", "bind", "createEqualityComparator", "_a", "comparator", "type", "constructor", "Date", "RegExp", "Map", "Set", "tag", "then", "createEqualityComparatorConfig", "circular", "createCustomConfig", "strict", "config", "areArraysEqual$1", "areMapsEqual$1", "areObjectsEqual$1", "areSetsEqual$1", "createInternalEqualityComparator", "compare", "_indexOrKeyA", "_indexOrKeyB", "_parentA", "_parentB", "createIsEqual", "createState", "_b", "WeakMap", "undefined", "meta", "deepEqual", "createCustomEqual", "strictDeepEqual", "circularDeepEqual", "strictCircularDeepEqual", "shallowEqual", "createInternalComparator", "strictShallowEqual", "circularShallowEqual", "strictCircularShallowEqual", "options", "createCustomInternalComparator"], "sources": ["C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\node_modules\\fast-equals\\src\\utils.ts", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\node_modules\\fast-equals\\src\\equals.ts", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\node_modules\\fast-equals\\src\\comparator.ts", "C:\\Users\\<USER>\\Desktop\\bot_fake_discord\\dashboard\\node_modules\\fast-equals\\src\\index.ts"], "sourcesContent": ["import type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  Dictionary,\n  State,\n  TypeEqualityComparator,\n} from './internalTypes';\n\nconst { getOwnPropertyNames, getOwnPropertySymbols } = Object;\nconst { hasOwnProperty } = Object.prototype;\n\n/**\n * Combine two comparators into a single comparators.\n */\nexport function combineComparators<Meta>(\n  comparatorA: AnyEqualityComparator<Meta>,\n  comparatorB: AnyEqualityComparator<Meta>,\n) {\n  return function isEqual<A, B>(a: A, b: B, state: State<Meta>) {\n    return comparatorA(a, b, state) && comparatorB(a, b, state);\n  };\n}\n\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nexport function createIsCircular<\n  AreItemsEqual extends TypeEqualityComparator<any, any>,\n>(areItemsEqual: AreItemsEqual): AreItemsEqual {\n  return function isCircular(\n    a: any,\n    b: any,\n    state: CircularState<Cache<any, any>>,\n  ) {\n    if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n      return areItemsEqual(a, b, state);\n    }\n\n    const { cache } = state;\n\n    const cachedA = cache.get(a);\n    const cachedB = cache.get(b);\n\n    if (cachedA && cachedB) {\n      return cachedA === b && cachedB === a;\n    }\n\n    cache.set(a, b);\n    cache.set(b, a);\n\n    const result = areItemsEqual(a, b, state);\n\n    cache.delete(a);\n    cache.delete(b);\n\n    return result;\n  } as AreItemsEqual;\n}\n\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nexport function getStrictProperties(\n  object: Dictionary,\n): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object),\n  );\n}\n\n/**\n * Whether the object contains the property passed as an own property.\n */\nexport const hasOwn =\n  Object.hasOwn ||\n  ((object: Dictionary, property: number | string | symbol) =>\n    hasOwnProperty.call(object, property));\n\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nexport function sameValueZeroEqual(a: any, b: any): boolean {\n  return a === b || (!a && !b && a !== a && b !== b);\n}\n", "import { getStrictProperties, hasOwn, sameValueZeroEqual } from './utils';\nimport type {\n  Dictionary,\n  PrimitiveWrapper,\n  State,\n  TypedArray,\n} from './internalTypes';\n\nconst PREACT_VNODE = '__v';\nconst PREACT_OWNER = '__o';\nconst REACT_OWNER = '_owner';\n\nconst { getOwnPropertyDescriptor, keys } = Object;\n\n/**\n * Whether the arrays are equal in value.\n */\nexport function areArraysEqual(a: any[], b: any[], state: State<any>) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the dates passed are equal in value.\n */\nexport function areDatesEqual(a: Date, b: Date): boolean {\n  return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n\n/**\n * Whether the errors passed are equal in value.\n */\nexport function areErrorsEqual(a: Error, b: Error): boolean {\n  return (\n    a.name === b.name &&\n    a.message === b.message &&\n    a.cause === b.cause &&\n    a.stack === b.stack\n  );\n}\n\n/**\n * Whether the functions passed are equal in value.\n */\nexport function areFunctionsEqual(\n  a: (...args: any[]) => any,\n  b: (...args: any[]) => any,\n): boolean {\n  return a === b;\n}\n\n/**\n * Whether the `Map`s are equal in value.\n */\nexport function areMapsEqual(\n  a: Map<any, any>,\n  b: Map<any, any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.entries();\n\n  let aResult: IteratorResult<[any, any]>;\n  let bResult: IteratorResult<[any, any]>;\n  let index = 0;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.entries();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (matchedIndices[matchIndex]) {\n        matchIndex++;\n        continue;\n      }\n\n      const aEntry = aResult.value;\n      const bEntry = bResult.value;\n\n      if (\n        state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n        state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n\n    index++;\n  }\n\n  return true;\n}\n\n/**\n * Whether the numbers are equal in value.\n */\nexport const areNumbersEqual = sameValueZeroEqual;\n\n/**\n * Whether the objects are equal in value.\n */\nexport function areObjectsEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = keys(a);\n\n  let index = properties.length;\n\n  if (keys(b).length !== index) {\n    return false;\n  }\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    if (!isPropertyEqual(a, b, state, properties[index]!)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nexport function areObjectsEqualStrict(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n): boolean {\n  const properties = getStrictProperties(a);\n\n  let index = properties.length;\n\n  if (getStrictProperties(b).length !== index) {\n    return false;\n  }\n\n  let property: string | symbol;\n  let descriptorA: ReturnType<typeof getOwnPropertyDescriptor>;\n  let descriptorB: ReturnType<typeof getOwnPropertyDescriptor>;\n\n  // Decrementing `while` showed faster results than either incrementing or\n  // decrementing `for` loop and than an incrementing `while` loop. Declarative\n  // methods like `some` / `every` were not used to avoid incurring the garbage\n  // cost of anonymous callbacks.\n  while (index-- > 0) {\n    property = properties[index]!;\n\n    if (!isPropertyEqual(a, b, state, property)) {\n      return false;\n    }\n\n    descriptorA = getOwnPropertyDescriptor(a, property);\n    descriptorB = getOwnPropertyDescriptor(b, property);\n\n    if (\n      (descriptorA || descriptorB) &&\n      (!descriptorA ||\n        !descriptorB ||\n        descriptorA.configurable !== descriptorB.configurable ||\n        descriptorA.enumerable !== descriptorB.enumerable ||\n        descriptorA.writable !== descriptorB.writable)\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nexport function arePrimitiveWrappersEqual(\n  a: PrimitiveWrapper,\n  b: PrimitiveWrapper,\n): boolean {\n  return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n\n/**\n * Whether the regexps passed are equal in value.\n */\nexport function areRegExpsEqual(a: RegExp, b: RegExp): boolean {\n  return a.source === b.source && a.flags === b.flags;\n}\n\n/**\n * Whether the `Set`s are equal in value.\n */\nexport function areSetsEqual(\n  a: Set<any>,\n  b: Set<any>,\n  state: State<any>,\n): boolean {\n  const size = a.size;\n\n  if (size !== b.size) {\n    return false;\n  }\n\n  if (!size) {\n    return true;\n  }\n\n  const matchedIndices: Array<true | undefined> = new Array(size);\n  const aIterable = a.values();\n\n  let aResult: IteratorResult<any>;\n  let bResult: IteratorResult<any>;\n\n  while ((aResult = aIterable.next())) {\n    if (aResult.done) {\n      break;\n    }\n\n    const bIterable = b.values();\n\n    let hasMatch = false;\n    let matchIndex = 0;\n\n    while ((bResult = bIterable.next())) {\n      if (bResult.done) {\n        break;\n      }\n\n      if (\n        !matchedIndices[matchIndex] &&\n        state.equals(\n          aResult.value,\n          bResult.value,\n          aResult.value,\n          bResult.value,\n          a,\n          b,\n          state,\n        )\n      ) {\n        hasMatch = matchedIndices[matchIndex] = true;\n        break;\n      }\n\n      matchIndex++;\n    }\n\n    if (!hasMatch) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the TypedArray instances are equal in value.\n */\nexport function areTypedArraysEqual(a: TypedArray, b: TypedArray) {\n  let index = a.length;\n\n  if (b.length !== index) {\n    return false;\n  }\n\n  while (index-- > 0) {\n    if (a[index] !== b[index]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Whether the URL instances are equal in value.\n */\nexport function areUrlsEqual(a: URL, b: URL): boolean {\n  return (\n    a.hostname === b.hostname &&\n    a.pathname === b.pathname &&\n    a.protocol === b.protocol &&\n    a.port === b.port &&\n    a.hash === b.hash &&\n    a.username === b.username &&\n    a.password === b.password\n  );\n}\n\nfunction isPropertyEqual(\n  a: Dictionary,\n  b: Dictionary,\n  state: State<any>,\n  property: string | symbol,\n) {\n  if (\n    (property === REACT_OWNER ||\n      property === PREACT_OWNER ||\n      property === PREACT_VNODE) &&\n    (a.$$typeof || b.$$typeof)\n  ) {\n    return true;\n  }\n\n  return (\n    hasOwn(b, property) &&\n    state.equals(a[property], b[property], property, property, a, b, state)\n  );\n}\n", "import {\n  areArraysEqual as areArraysEqualDefault,\n  areDatesEqual as areDatesEqualDefault,\n  areErrorsEqual as areErrorsEqualDefault,\n  areFunctionsEqual as areFunctionsEqualDefault,\n  areMapsEqual as areMapsEqualDefault,\n  areNumbersEqual as areNumbersEqualDefault,\n  areObjectsEqual as areObjectsEqualDefault,\n  areObjectsEqualStrict as areObjectsEqualStrictDefault,\n  arePrimitiveWrappersEqual as arePrimitiveWrappersEqualDefault,\n  areRegExpsEqual as areRegExpsEqualDefault,\n  areSetsEqual as areSetsEqualDefault,\n  areTypedArraysEqual as areTypedArraysEqualDefault,\n  areUrlsEqual as areUrlsEqualDefault,\n} from './equals';\nimport { combineComparators, createIsCircular } from './utils';\nimport type {\n  ComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  EqualityComparator,\n  InternalEqualityComparator,\n  State,\n} from './internalTypes';\n\nconst ARGUMENTS_TAG = '[object Arguments]';\nconst BOOLEAN_TAG = '[object Boolean]';\nconst DATE_TAG = '[object Date]';\nconst ERROR_TAG = '[object Error]';\nconst MAP_TAG = '[object Map]';\nconst NUMBER_TAG = '[object Number]';\nconst OBJECT_TAG = '[object Object]';\nconst REG_EXP_TAG = '[object RegExp]';\nconst SET_TAG = '[object Set]';\nconst STRING_TAG = '[object String]';\nconst URL_TAG = '[object URL]';\n\nconst { isArray } = Array;\nconst isTypedArray =\n  typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nconst { assign } = Object;\nconst getTag = Object.prototype.toString.call.bind(\n  Object.prototype.toString,\n) as (a: object) => string;\n\ninterface CreateIsEqualOptions<Meta> {\n  circular: boolean;\n  comparator: EqualityComparator<Meta>;\n  createState: CreateState<Meta> | undefined;\n  equals: InternalEqualityComparator<Meta>;\n  strict: boolean;\n}\n\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nexport function createEqualityComparator<Meta>({\n  areArraysEqual,\n  areDatesEqual,\n  areErrorsEqual,\n  areFunctionsEqual,\n  areMapsEqual,\n  areNumbersEqual,\n  areObjectsEqual,\n  arePrimitiveWrappersEqual,\n  areRegExpsEqual,\n  areSetsEqual,\n  areTypedArraysEqual,\n  areUrlsEqual,\n}: ComparatorConfig<Meta>): EqualityComparator<Meta> {\n  /**\n   * compare the value of the two objects and return true if they are equivalent in values\n   */\n  return function comparator(a: any, b: any, state: State<Meta>): boolean {\n    // If the items are strictly equal, no need to do a value comparison.\n    if (a === b) {\n      return true;\n    }\n\n    // If either of the items are nullish and fail the strictly equal check\n    // above, then they must be unequal.\n    if (a == null || b == null) {\n      return false;\n    }\n\n    const type = typeof a;\n\n    if (type !== typeof b) {\n      return false;\n    }\n\n    if (type !== 'object') {\n      if (type === 'number') {\n        return areNumbersEqual(a, b, state);\n      }\n\n      if (type === 'function') {\n        return areFunctionsEqual(a, b, state);\n      }\n\n      // If a primitive value that is not strictly equal, it must be unequal.\n      return false;\n    }\n\n    const constructor = a.constructor;\n\n    // Checks are listed in order of commonality of use-case:\n    //   1. Common complex object types (plain object, array)\n    //   2. Common data values (date, regexp)\n    //   3. Less-common complex object types (map, set)\n    //   4. Less-common data values (promise, primitive wrappers)\n    // Inherently this is both subjective and assumptive, however\n    // when reviewing comparable libraries in the wild this order\n    // appears to be generally consistent.\n\n    // Constructors should match, otherwise there is potential for false positives\n    // between class and subclass or custom object and POJO.\n    if (constructor !== b.constructor) {\n      return false;\n    }\n\n    // `isPlainObject` only checks against the object's own realm. Cross-realm\n    // comparisons are rare, and will be handled in the ultimate fallback, so\n    // we can avoid capturing the string tag.\n    if (constructor === Object) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n    // the string tag or doing an `instanceof` check.\n    if (isArray(a)) {\n      return areArraysEqual(a, b, state);\n    }\n\n    // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n    // capturing the string tag or comparing against all possible constructors.\n    if (isTypedArray != null && isTypedArray(a)) {\n      return areTypedArraysEqual(a, b, state);\n    }\n\n    // Try to fast-path equality checks for other complex object types in the\n    // same realm to avoid capturing the string tag. Strict equality is used\n    // instead of `instanceof` because it is more performant for the common\n    // use-case. If someone is subclassing a native class, it will be handled\n    // with the string tag comparison.\n\n    if (constructor === Date) {\n      return areDatesEqual(a, b, state);\n    }\n\n    if (constructor === RegExp) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (constructor === Map) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (constructor === Set) {\n      return areSetsEqual(a, b, state);\n    }\n\n    // Since this is a custom object, capture the string tag to determing its type.\n    // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n    const tag = getTag(a);\n\n    if (tag === DATE_TAG) {\n      return areDatesEqual(a, b, state);\n    }\n\n    // For RegExp, the properties are not enumerable, and therefore will give false positives if\n    // tested like a standard object.\n    if (tag === REG_EXP_TAG) {\n      return areRegExpsEqual(a, b, state);\n    }\n\n    if (tag === MAP_TAG) {\n      return areMapsEqual(a, b, state);\n    }\n\n    if (tag === SET_TAG) {\n      return areSetsEqual(a, b, state);\n    }\n\n    if (tag === OBJECT_TAG) {\n      // The exception for value comparison is custom `Promise`-like class instances. These should\n      // be treated the same as standard `Promise` objects, which means strict equality, and if\n      // it reaches this point then that strict equality comparison has already failed.\n      return (\n        typeof a.then !== 'function' &&\n        typeof b.then !== 'function' &&\n        areObjectsEqual(a, b, state)\n      );\n    }\n\n    // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === URL_TAG) {\n      return areUrlsEqual(a, b, state);\n    }\n\n    // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n    // enumerable, and therefore will give false positives if tested like a standard object.\n    if (tag === ERROR_TAG) {\n      return areErrorsEqual(a, b, state);\n    }\n\n    // If an arguments tag, it should be treated as a standard object.\n    if (tag === ARGUMENTS_TAG) {\n      return areObjectsEqual(a, b, state);\n    }\n\n    // As the penultimate fallback, check if the values passed are primitive wrappers. This\n    // is very rare in modern JS, which is why it is deprioritized compared to all other object\n    // types.\n    if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n      return arePrimitiveWrappersEqual(a, b, state);\n    }\n\n    // If not matching any tags that require a specific type of comparison, then we hard-code false because\n    // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n    //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n    //     comparison that can be made.\n    //   - For types that can be introspected, but rarely have requirements to be compared\n    //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n    //     use-cases (may be included in a future release, if requested enough).\n    //   - For types that can be introspected but do not have an objective definition of what\n    //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n    // In all cases, these decisions should be reevaluated based on changes to the language and\n    // common development practices.\n    return false;\n  };\n}\n\n/**\n * Create the configuration object used for building comparators.\n */\nexport function createEqualityComparatorConfig<Meta>({\n  circular,\n  createCustomConfig,\n  strict,\n}: CustomEqualCreatorOptions<Meta>): ComparatorConfig<Meta> {\n  let config = {\n    areArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areArraysEqualDefault,\n    areDatesEqual: areDatesEqualDefault,\n    areErrorsEqual: areErrorsEqualDefault,\n    areFunctionsEqual: areFunctionsEqualDefault,\n    areMapsEqual: strict\n      ? combineComparators(areMapsEqualDefault, areObjectsEqualStrictDefault)\n      : areMapsEqualDefault,\n    areNumbersEqual: areNumbersEqualDefault,\n    areObjectsEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areObjectsEqualDefault,\n    arePrimitiveWrappersEqual: arePrimitiveWrappersEqualDefault,\n    areRegExpsEqual: areRegExpsEqualDefault,\n    areSetsEqual: strict\n      ? combineComparators(areSetsEqualDefault, areObjectsEqualStrictDefault)\n      : areSetsEqualDefault,\n    areTypedArraysEqual: strict\n      ? areObjectsEqualStrictDefault\n      : areTypedArraysEqualDefault,\n    areUrlsEqual: areUrlsEqualDefault,\n  };\n\n  if (createCustomConfig) {\n    config = assign({}, config, createCustomConfig(config));\n  }\n\n  if (circular) {\n    const areArraysEqual = createIsCircular(config.areArraysEqual);\n    const areMapsEqual = createIsCircular(config.areMapsEqual);\n    const areObjectsEqual = createIsCircular(config.areObjectsEqual);\n    const areSetsEqual = createIsCircular(config.areSetsEqual);\n\n    config = assign({}, config, {\n      areArraysEqual,\n      areMapsEqual,\n      areObjectsEqual,\n      areSetsEqual,\n    });\n  }\n\n  return config;\n}\n\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nexport function createInternalEqualityComparator<Meta>(\n  compare: EqualityComparator<Meta>,\n): InternalEqualityComparator<Meta> {\n  return function (\n    a: any,\n    b: any,\n    _indexOrKeyA: any,\n    _indexOrKeyB: any,\n    _parentA: any,\n    _parentB: any,\n    state: State<Meta>,\n  ) {\n    return compare(a, b, state);\n  };\n}\n\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nexport function createIsEqual<Meta>({\n  circular,\n  comparator,\n  createState,\n  equals,\n  strict,\n}: CreateIsEqualOptions<Meta>) {\n  if (createState) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      const { cache = circular ? new WeakMap() : undefined, meta } =\n        createState!();\n\n      return comparator(a, b, {\n        cache,\n        equals,\n        meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  if (circular) {\n    return function isEqual<A, B>(a: A, b: B): boolean {\n      return comparator(a, b, {\n        cache: new WeakMap(),\n        equals,\n        meta: undefined as Meta,\n        strict,\n      } as State<Meta>);\n    };\n  }\n\n  const state = {\n    cache: undefined,\n    equals,\n    meta: undefined,\n    strict,\n  } as State<Meta>;\n\n  return function isEqual<A, B>(a: A, b: B): boolean {\n    return comparator(a, b, state);\n  };\n}\n", "import {\n  createEqualityComparatorConfig,\n  createEqualityComparator,\n  createInternalEqualityComparator,\n  createIsEqual,\n} from './comparator';\nimport type { CustomEqualCreatorOptions } from './internalTypes';\nimport { sameValueZeroEqual } from './utils';\n\nexport { sameValueZeroEqual };\nexport type {\n  AnyEqualityComparator,\n  Cache,\n  CircularState,\n  ComparatorConfig,\n  CreateCustomComparatorConfig,\n  CreateState,\n  CustomEqualCreatorOptions,\n  DefaultState,\n  Dictionary,\n  EqualityComparator,\n  EqualityComparatorCreator,\n  InternalEqualityComparator,\n  PrimitiveWrapper,\n  State,\n  TypeEqualityComparator,\n  TypedArray,\n} from './internalTypes';\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nexport const deepEqual = createCustomEqual();\n\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nexport const strictDeepEqual = createCustomEqual({ strict: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nexport const circularDeepEqual = createCustomEqual({ circular: true });\n\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularDeepEqual = createCustomEqual({\n  circular: true,\n  strict: true,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nexport const shallowEqual = createCustomEqual({\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nexport const strictShallowEqual = createCustomEqual({\n  strict: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nexport const circularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n});\n\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nexport const strictCircularShallowEqual = createCustomEqual({\n  circular: true,\n  createInternalComparator: () => sameValueZeroEqual,\n  strict: true,\n});\n\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nexport function createCustomEqual<Meta = undefined>(\n  options: CustomEqualCreatorOptions<Meta> = {},\n) {\n  const {\n    circular = false,\n    createInternalComparator: createCustomInternalComparator,\n    createState,\n    strict = false,\n  } = options;\n\n  const config = createEqualityComparatorConfig<Meta>(options);\n  const comparator = createEqualityComparator(config);\n  const equals = createCustomInternalComparator\n    ? createCustomInternalComparator(comparator)\n    : createInternalEqualityComparator(comparator);\n\n  return createIsEqual({ circular, comparator, createState, equals, strict });\n}\n"], "mappings": "AASQ,IAAAA,mBAAmB,GAA4BC,MAAM,CAAAD,mBAAlC;EAAEE,qBAAqB,GAAKD,MAAM,CAAAC,qBAAX;AAC1C,IAAAC,cAAc,GAAKF,MAAM,CAACG,SAAS,CAAAD,cAArB;AAEtB;;AAEG;AACa,SAAAE,kBAAkBA,CAChCC,WAAwC,EACxCC,WAAwC;EAExC,OAAO,SAASC,OAAOA,CAAOC,CAAI,EAAEC,CAAI,EAAEC,KAAkB;IAC1D,OAAOL,WAAW,CAACG,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,IAAIJ,WAAW,CAACE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;EAC7D,CAAC;AACH;AAEA;;;;AAIG;AACG,SAAUC,gBAAgBA,CAE9BC,aAA4B;EAC5B,OAAO,SAASC,UAAUA,CACxBL,CAAM,EACNC,CAAM,EACNC,KAAqC;IAErC,IAAI,CAACF,CAAC,IAAI,CAACC,CAAC,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MAC9D,OAAOG,aAAa,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAClC;IAEO,IAAAI,KAAK,GAAKJ,KAAK,CAAAI,KAAV;IAEb,IAAMC,OAAO,GAAGD,KAAK,CAACE,GAAG,CAACR,CAAC,CAAC;IAC5B,IAAMS,OAAO,GAAGH,KAAK,CAACE,GAAG,CAACP,CAAC,CAAC;IAE5B,IAAIM,OAAO,IAAIE,OAAO,EAAE;MACtB,OAAOF,OAAO,KAAKN,CAAC,IAAIQ,OAAO,KAAKT,CAAC;IACtC;IAEDM,KAAK,CAACI,GAAG,CAACV,CAAC,EAAEC,CAAC,CAAC;IACfK,KAAK,CAACI,GAAG,CAACT,CAAC,EAAED,CAAC,CAAC;IAEf,IAAMW,MAAM,GAAGP,aAAa,CAACJ,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAEzCI,KAAK,CAACM,MAAM,CAACZ,CAAC,CAAC;IACfM,KAAK,CAACM,MAAM,CAACX,CAAC,CAAC;IAEf,OAAOU,MAAM;EACf,CAAkB;AACpB;AAEA;;;AAGG;AACG,SAAUE,mBAAmBA,CACjCC,MAAkB;EAElB,OAAQvB,mBAAmB,CAACuB,MAAM,CAA4B,CAACC,MAAM,CACnEtB,qBAAqB,CAACqB,MAAM,CAAC,CAC9B;AACH;AAEA;;AAEG;AACI,IAAME,MAAM,GACjBxB,MAAM,CAACwB,MAAM,IACZ,UAACF,MAAkB,EAAEG,QAAkC;EACtD,OAAAvB,cAAc,CAACwB,IAAI,CAACJ,MAAM,EAAEG,QAAQ,CAAC;AAArC,CAAsC;AAE1C;;AAEG;AACa,SAAAE,kBAAkBA,CAACnB,CAAM,EAAEC,CAAM;EAC/C,OAAOD,CAAC,KAAKC,CAAC,IAAK,CAACD,CAAC,IAAI,CAACC,CAAC,IAAID,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAE;AACpD;AC/EA,IAAMmB,YAAY,GAAG,KAAK;AAC1B,IAAMC,YAAY,GAAG,KAAK;AAC1B,IAAMC,WAAW,GAAG,QAAQ;AAEpB,IAAAC,wBAAwB,GAAW/B,MAAM,CAAA+B,wBAAjB;EAAEC,IAAI,GAAKhC,MAAM,CAAAgC,IAAX;AAEtC;;AAEG;SACaC,cAAcA,CAACzB,CAAQ,EAAEC,CAAQ,EAAEC,KAAiB;EAClE,IAAIwB,KAAK,GAAG1B,CAAC,CAAC2B,MAAM;EAEpB,IAAI1B,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;IACtB,OAAO,KAAK;EACb;EAED,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;IAClB,IAAI,CAACxB,KAAK,CAAC0B,MAAM,CAAC5B,CAAC,CAAC0B,KAAK,CAAC,EAAEzB,CAAC,CAACyB,KAAK,CAAC,EAAEA,KAAK,EAAEA,KAAK,EAAE1B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,EAAE;MAChE,OAAO,KAAK;IACb;EACF;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;AACa,SAAA2B,aAAaA,CAAC7B,CAAO,EAAEC,CAAO;EAC5C,OAAOkB,kBAAkB,CAACnB,CAAC,CAAC8B,OAAO,EAAE,EAAE7B,CAAC,CAAC6B,OAAO,EAAE,CAAC;AACrD;AAEA;;AAEG;AACa,SAAAC,cAAcA,CAAC/B,CAAQ,EAAEC,CAAQ;EAC/C,OACED,CAAC,CAACgC,IAAI,KAAK/B,CAAC,CAAC+B,IAAI,IACjBhC,CAAC,CAACiC,OAAO,KAAKhC,CAAC,CAACgC,OAAO,IACvBjC,CAAC,CAACkC,KAAK,KAAKjC,CAAC,CAACiC,KAAK,IACnBlC,CAAC,CAACmC,KAAK,KAAKlC,CAAC,CAACkC,KAAK;AAEvB;AAEA;;AAEG;AACa,SAAAC,iBAAiBA,CAC/BpC,CAA0B,EAC1BC,CAA0B;EAE1B,OAAOD,CAAC,KAAKC,CAAC;AAChB;AAEA;;AAEG;SACaoC,YAAYA,CAC1BrC,CAAgB,EAChBC,CAAgB,EAChBC,KAAiB;EAEjB,IAAMoC,IAAI,GAAGtC,CAAC,CAACsC,IAAI;EAEnB,IAAIA,IAAI,KAAKrC,CAAC,CAACqC,IAAI,EAAE;IACnB,OAAO,KAAK;EACb;EAED,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACZ;EAED,IAAMC,cAAc,GAA4B,IAAIC,KAAK,CAACF,IAAI,CAAC;EAC/D,IAAMG,SAAS,GAAGzC,CAAC,CAAC0C,OAAO,EAAE;EAE7B,IAAIC,OAAmC;EACvC,IAAIC,OAAmC;EACvC,IAAIlB,KAAK,GAAG,CAAC;EAEb,OAAQiB,OAAO,GAAGF,SAAS,CAACI,IAAI,EAAE,EAAG;IACnC,IAAIF,OAAO,CAACG,IAAI,EAAE;MAChB;IACD;IAED,IAAMC,SAAS,GAAG9C,CAAC,CAACyC,OAAO,EAAE;IAE7B,IAAIM,QAAQ,GAAG,KAAK;IACpB,IAAIC,UAAU,GAAG,CAAC;IAElB,OAAQL,OAAO,GAAGG,SAAS,CAACF,IAAI,EAAE,EAAG;MACnC,IAAID,OAAO,CAACE,IAAI,EAAE;QAChB;MACD;MAED,IAAIP,cAAc,CAACU,UAAU,CAAC,EAAE;QAC9BA,UAAU,EAAE;QACZ;MACD;MAED,IAAMC,MAAM,GAAGP,OAAO,CAACQ,KAAK;MAC5B,IAAMC,MAAM,GAAGR,OAAO,CAACO,KAAK;MAE5B,IACEjD,KAAK,CAAC0B,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAE1B,KAAK,EAAEuB,UAAU,EAAEjD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,IAClEA,KAAK,CAAC0B,MAAM,CAACsB,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC,CAAC,CAAC,EAAEpD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC,EACrE;QACA8C,QAAQ,GAAGT,cAAc,CAACU,UAAU,CAAC,GAAG,IAAI;QAC5C;MACD;MAEDA,UAAU,EAAE;IACb;IAED,IAAI,CAACD,QAAQ,EAAE;MACb,OAAO,KAAK;IACb;IAEDtB,KAAK,EAAE;EACR;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;AACI,IAAM2B,eAAe,GAAGlC,kBAAkB;AAEjD;;AAEG;SACamC,eAAeA,CAC7BtD,CAAa,EACbC,CAAa,EACbC,KAAiB;EAEjB,IAAMqD,UAAU,GAAG/B,IAAI,CAACxB,CAAC,CAAC;EAE1B,IAAI0B,KAAK,GAAG6B,UAAU,CAAC5B,MAAM;EAE7B,IAAIH,IAAI,CAACvB,CAAC,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;IAC5B,OAAO,KAAK;EACb;;;;;EAMD,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;IAClB,IAAI,CAAC8B,eAAe,CAACxD,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEqD,UAAU,CAAC7B,KAAK,CAAE,CAAC,EAAE;MACrD,OAAO,KAAK;IACb;EACF;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;SACa+B,qBAAqBA,CACnCzD,CAAa,EACbC,CAAa,EACbC,KAAiB;EAEjB,IAAMqD,UAAU,GAAG1C,mBAAmB,CAACb,CAAC,CAAC;EAEzC,IAAI0B,KAAK,GAAG6B,UAAU,CAAC5B,MAAM;EAE7B,IAAId,mBAAmB,CAACZ,CAAC,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;IAC3C,OAAO,KAAK;EACb;EAED,IAAIT,QAAyB;EAC7B,IAAIyC,WAAwD;EAC5D,IAAIC,WAAwD;;;;;EAM5D,OAAOjC,KAAK,EAAE,GAAG,CAAC,EAAE;IAClBT,QAAQ,GAAGsC,UAAU,CAAC7B,KAAK,CAAE;IAE7B,IAAI,CAAC8B,eAAe,CAACxD,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEe,QAAQ,CAAC,EAAE;MAC3C,OAAO,KAAK;IACb;IAEDyC,WAAW,GAAGnC,wBAAwB,CAACvB,CAAC,EAAEiB,QAAQ,CAAC;IACnD0C,WAAW,GAAGpC,wBAAwB,CAACtB,CAAC,EAAEgB,QAAQ,CAAC;IAEnD,IACE,CAACyC,WAAW,IAAIC,WAAW,MAC1B,CAACD,WAAW,IACX,CAACC,WAAW,IACZD,WAAW,CAACE,YAAY,KAAKD,WAAW,CAACC,YAAY,IACrDF,WAAW,CAACG,UAAU,KAAKF,WAAW,CAACE,UAAU,IACjDH,WAAW,CAACI,QAAQ,KAAKH,WAAW,CAACG,QAAQ,CAAC,EAChD;MACA,OAAO,KAAK;IACb;EACF;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;AACa,SAAAC,yBAAyBA,CACvC/D,CAAmB,EACnBC,CAAmB;EAEnB,OAAOkB,kBAAkB,CAACnB,CAAC,CAACgE,OAAO,EAAE,EAAE/D,CAAC,CAAC+D,OAAO,EAAE,CAAC;AACrD;AAEA;;AAEG;AACa,SAAAC,eAAeA,CAACjE,CAAS,EAAEC,CAAS;EAClD,OAAOD,CAAC,CAACkE,MAAM,KAAKjE,CAAC,CAACiE,MAAM,IAAIlE,CAAC,CAACmE,KAAK,KAAKlE,CAAC,CAACkE,KAAK;AACrD;AAEA;;AAEG;SACaC,YAAYA,CAC1BpE,CAAW,EACXC,CAAW,EACXC,KAAiB;EAEjB,IAAMoC,IAAI,GAAGtC,CAAC,CAACsC,IAAI;EAEnB,IAAIA,IAAI,KAAKrC,CAAC,CAACqC,IAAI,EAAE;IACnB,OAAO,KAAK;EACb;EAED,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,IAAI;EACZ;EAED,IAAMC,cAAc,GAA4B,IAAIC,KAAK,CAACF,IAAI,CAAC;EAC/D,IAAMG,SAAS,GAAGzC,CAAC,CAACqE,MAAM,EAAE;EAE5B,IAAI1B,OAA4B;EAChC,IAAIC,OAA4B;EAEhC,OAAQD,OAAO,GAAGF,SAAS,CAACI,IAAI,EAAE,EAAG;IACnC,IAAIF,OAAO,CAACG,IAAI,EAAE;MAChB;IACD;IAED,IAAMC,SAAS,GAAG9C,CAAC,CAACoE,MAAM,EAAE;IAE5B,IAAIrB,QAAQ,GAAG,KAAK;IACpB,IAAIC,UAAU,GAAG,CAAC;IAElB,OAAQL,OAAO,GAAGG,SAAS,CAACF,IAAI,EAAE,EAAG;MACnC,IAAID,OAAO,CAACE,IAAI,EAAE;QAChB;MACD;MAED,IACE,CAACP,cAAc,CAACU,UAAU,CAAC,IAC3B/C,KAAK,CAAC0B,MAAM,CACVe,OAAO,CAACQ,KAAK,EACbP,OAAO,CAACO,KAAK,EACbR,OAAO,CAACQ,KAAK,EACbP,OAAO,CAACO,KAAK,EACbnD,CAAC,EACDC,CAAC,EACDC,KAAK,CACN,EACD;QACA8C,QAAQ,GAAGT,cAAc,CAACU,UAAU,CAAC,GAAG,IAAI;QAC5C;MACD;MAEDA,UAAU,EAAE;IACb;IAED,IAAI,CAACD,QAAQ,EAAE;MACb,OAAO,KAAK;IACb;EACF;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;AACa,SAAAsB,mBAAmBA,CAACtE,CAAa,EAAEC,CAAa;EAC9D,IAAIyB,KAAK,GAAG1B,CAAC,CAAC2B,MAAM;EAEpB,IAAI1B,CAAC,CAAC0B,MAAM,KAAKD,KAAK,EAAE;IACtB,OAAO,KAAK;EACb;EAED,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;IAClB,IAAI1B,CAAC,CAAC0B,KAAK,CAAC,KAAKzB,CAAC,CAACyB,KAAK,CAAC,EAAE;MACzB,OAAO,KAAK;IACb;EACF;EAED,OAAO,IAAI;AACb;AAEA;;AAEG;AACa,SAAA6C,YAAYA,CAACvE,CAAM,EAAEC,CAAM;EACzC,OACED,CAAC,CAACwE,QAAQ,KAAKvE,CAAC,CAACuE,QAAQ,IACzBxE,CAAC,CAACyE,QAAQ,KAAKxE,CAAC,CAACwE,QAAQ,IACzBzE,CAAC,CAAC0E,QAAQ,KAAKzE,CAAC,CAACyE,QAAQ,IACzB1E,CAAC,CAAC2E,IAAI,KAAK1E,CAAC,CAAC0E,IAAI,IACjB3E,CAAC,CAAC4E,IAAI,KAAK3E,CAAC,CAAC2E,IAAI,IACjB5E,CAAC,CAAC6E,QAAQ,KAAK5E,CAAC,CAAC4E,QAAQ,IACzB7E,CAAC,CAAC8E,QAAQ,KAAK7E,CAAC,CAAC6E,QAAQ;AAE7B;AAEA,SAAStB,eAAeA,CACtBxD,CAAa,EACbC,CAAa,EACbC,KAAiB,EACjBe,QAAyB;EAEzB,IACE,CAACA,QAAQ,KAAKK,WAAW,IACvBL,QAAQ,KAAKI,YAAY,IACzBJ,QAAQ,KAAKG,YAAY,MAC1BpB,CAAC,CAAC+E,QAAQ,IAAI9E,CAAC,CAAC8E,QAAQ,CAAC,EAC1B;IACA,OAAO,IAAI;EACZ;EAED,OACE/D,MAAM,CAACf,CAAC,EAAEgB,QAAQ,CAAC,IACnBf,KAAK,CAAC0B,MAAM,CAAC5B,CAAC,CAACiB,QAAQ,CAAC,EAAEhB,CAAC,CAACgB,QAAQ,CAAC,EAAEA,QAAQ,EAAEA,QAAQ,EAAEjB,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;AAE3E;ACrUA,IAAM8E,aAAa,GAAG,oBAAoB;AAC1C,IAAMC,WAAW,GAAG,kBAAkB;AACtC,IAAMC,QAAQ,GAAG,eAAe;AAChC,IAAMC,SAAS,GAAG,gBAAgB;AAClC,IAAMC,OAAO,GAAG,cAAc;AAC9B,IAAMC,UAAU,GAAG,iBAAiB;AACpC,IAAMC,UAAU,GAAG,iBAAiB;AACpC,IAAMC,WAAW,GAAG,iBAAiB;AACrC,IAAMC,OAAO,GAAG,cAAc;AAC9B,IAAMC,UAAU,GAAG,iBAAiB;AACpC,IAAMC,OAAO,GAAG,cAAc;AAEtB,IAAAC,OAAO,GAAKnD,KAAK,CAAAmD,OAAV;AACf,IAAMC,YAAY,GAChB,OAAOC,WAAW,KAAK,UAAU,IAAIA,WAAW,CAACC,MAAM,GACnDD,WAAW,CAACC,MAAM,GAClB,IAAI;AACF,IAAAC,MAAM,GAAKvG,MAAM,CAAAuG,MAAX;AACd,IAAMC,MAAM,GAAGxG,MAAM,CAACG,SAAS,CAACsG,QAAQ,CAAC/E,IAAI,CAACgF,IAAI,CAChD1G,MAAM,CAACG,SAAS,CAACsG,QAAQ,CACD;AAU1B;;AAEG;AACG,SAAUE,wBAAwBA,CAAOC,EAatB;EAZvB,IAAA3E,cAAc,GAAA2E,EAAA,CAAA3E,cAAA;IACdI,aAAa,GAAAuE,EAAA,CAAAvE,aAAA;IACbE,cAAc,GAAAqE,EAAA,CAAArE,cAAA;IACdK,iBAAiB,GAAAgE,EAAA,CAAAhE,iBAAA;IACjBC,YAAY,GAAA+D,EAAA,CAAA/D,YAAA;IACZgB,eAAe,GAAA+C,EAAA,CAAA/C,eAAA;IACfC,eAAe,GAAA8C,EAAA,CAAA9C,eAAA;IACfS,yBAAyB,GAAAqC,EAAA,CAAArC,yBAAA;IACzBE,eAAe,GAAAmC,EAAA,CAAAnC,eAAA;IACfG,YAAY,GAAAgC,EAAA,CAAAhC,YAAA;IACZE,mBAAmB,GAAA8B,EAAA,CAAA9B,mBAAA;IACnBC,YAAY,GAAA6B,EAAA,CAAA7B,YAAA;EAEZ;;AAEG;EACH,OAAO,SAAS8B,UAAUA,CAACrG,CAAM,EAAEC,CAAM,EAAEC,KAAkB;;IAE3D,IAAIF,CAAC,KAAKC,CAAC,EAAE;MACX,OAAO,IAAI;IACZ;;;IAID,IAAID,CAAC,IAAI,IAAI,IAAIC,CAAC,IAAI,IAAI,EAAE;MAC1B,OAAO,KAAK;IACb;IAED,IAAMqG,IAAI,GAAG,OAAOtG,CAAC;IAErB,IAAIsG,IAAI,KAAK,OAAOrG,CAAC,EAAE;MACrB,OAAO,KAAK;IACb;IAED,IAAIqG,IAAI,KAAK,QAAQ,EAAE;MACrB,IAAIA,IAAI,KAAK,QAAQ,EAAE;QACrB,OAAOjD,eAAe,CAACrD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACpC;MAED,IAAIoG,IAAI,KAAK,UAAU,EAAE;QACvB,OAAOlE,iBAAiB,CAACpC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;MACtC;;MAGD,OAAO,KAAK;IACb;IAED,IAAMqG,WAAW,GAAGvG,CAAC,CAACuG,WAAW;;;;;;;;;;;IAajC,IAAIA,WAAW,KAAKtG,CAAC,CAACsG,WAAW,EAAE;MACjC,OAAO,KAAK;IACb;;;;IAKD,IAAIA,WAAW,KAAK/G,MAAM,EAAE;MAC1B,OAAO8D,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACpC;;;IAID,IAAIyF,OAAO,CAAC3F,CAAC,CAAC,EAAE;MACd,OAAOyB,cAAc,CAACzB,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACnC;;;IAID,IAAI0F,YAAY,IAAI,IAAI,IAAIA,YAAY,CAAC5F,CAAC,CAAC,EAAE;MAC3C,OAAOsE,mBAAmB,CAACtE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACxC;;;;;;IAQD,IAAIqG,WAAW,KAAKC,IAAI,EAAE;MACxB,OAAO3E,aAAa,CAAC7B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAClC;IAED,IAAIqG,WAAW,KAAKE,MAAM,EAAE;MAC1B,OAAOxC,eAAe,CAACjE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACpC;IAED,IAAIqG,WAAW,KAAKG,GAAG,EAAE;MACvB,OAAOrE,YAAY,CAACrC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACjC;IAED,IAAIqG,WAAW,KAAKI,GAAG,EAAE;MACvB,OAAOvC,YAAY,CAACpE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACjC;;;IAID,IAAM0G,GAAG,GAAGZ,MAAM,CAAChG,CAAC,CAAC;IAErB,IAAI4G,GAAG,KAAK1B,QAAQ,EAAE;MACpB,OAAOrD,aAAa,CAAC7B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAClC;;;IAID,IAAI0G,GAAG,KAAKrB,WAAW,EAAE;MACvB,OAAOtB,eAAe,CAACjE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACpC;IAED,IAAI0G,GAAG,KAAKxB,OAAO,EAAE;MACnB,OAAO/C,YAAY,CAACrC,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACjC;IAED,IAAI0G,GAAG,KAAKpB,OAAO,EAAE;MACnB,OAAOpB,YAAY,CAACpE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACjC;IAED,IAAI0G,GAAG,KAAKtB,UAAU,EAAE;;;;MAItB,OACE,OAAOtF,CAAC,CAAC6G,IAAI,KAAK,UAAU,IAC5B,OAAO5G,CAAC,CAAC4G,IAAI,KAAK,UAAU,IAC5BvD,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAE/B;;;IAID,IAAI0G,GAAG,KAAKlB,OAAO,EAAE;MACnB,OAAOnB,YAAY,CAACvE,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACjC;;;IAID,IAAI0G,GAAG,KAAKzB,SAAS,EAAE;MACrB,OAAOpD,cAAc,CAAC/B,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACnC;;IAGD,IAAI0G,GAAG,KAAK5B,aAAa,EAAE;MACzB,OAAO1B,eAAe,CAACtD,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IACpC;;;;IAKD,IAAI0G,GAAG,KAAK3B,WAAW,IAAI2B,GAAG,KAAKvB,UAAU,IAAIuB,GAAG,KAAKnB,UAAU,EAAE;MACnE,OAAO1B,yBAAyB,CAAC/D,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;IAC9C;;;;;;;;;;;;IAaD,OAAO,KAAK;EACd,CAAC;AACH;AAEA;;AAEG;AACG,SAAU4G,8BAA8BA,CAAOV,EAInB;EAHhC,IAAAW,QAAQ,GAAAX,EAAA,CAAAW,QAAA;IACRC,kBAAkB,GAAAZ,EAAA,CAAAY,kBAAA;IAClBC,MAAM,GAAAb,EAAA,CAAAa,MAAA;EAEN,IAAIC,MAAM,GAAG;IACXzF,cAAc,EAAEwF,MAAM,GAClBxD,qBAA4B,GAC5BhC,cAAqB;IACzBI,aAAa,EAAEA,aAAoB;IACnCE,cAAc,EAAEA,cAAqB;IACrCK,iBAAiB,EAAEA,iBAAwB;IAC3CC,YAAY,EAAE4E,MAAM,GAChBrH,kBAAkB,CAACyC,YAAmB,EAAEoB,qBAA4B,CAAC,GACrEpB,YAAmB;IACvBgB,eAAe,EAAEA,eAAsB;IACvCC,eAAe,EAAE2D,MAAM,GACnBxD,qBAA4B,GAC5BH,eAAsB;IAC1BS,yBAAyB,EAAEA,yBAAgC;IAC3DE,eAAe,EAAEA,eAAsB;IACvCG,YAAY,EAAE6C,MAAM,GAChBrH,kBAAkB,CAACwE,YAAmB,EAAEX,qBAA4B,CAAC,GACrEW,YAAmB;IACvBE,mBAAmB,EAAE2C,MAAM,GACvBxD,qBAA4B,GAC5Ba,mBAA0B;IAC9BC,YAAY,EAAEA;GACf;EAED,IAAIyC,kBAAkB,EAAE;IACtBE,MAAM,GAAGnB,MAAM,CAAC,EAAE,EAAEmB,MAAM,EAAEF,kBAAkB,CAACE,MAAM,CAAC,CAAC;EACxD;EAED,IAAIH,QAAQ,EAAE;IACZ,IAAMI,gBAAc,GAAGhH,gBAAgB,CAAC+G,MAAM,CAACzF,cAAc,CAAC;IAC9D,IAAM2F,cAAY,GAAGjH,gBAAgB,CAAC+G,MAAM,CAAC7E,YAAY,CAAC;IAC1D,IAAMgF,iBAAe,GAAGlH,gBAAgB,CAAC+G,MAAM,CAAC5D,eAAe,CAAC;IAChE,IAAMgE,cAAY,GAAGnH,gBAAgB,CAAC+G,MAAM,CAAC9C,YAAY,CAAC;IAE1D8C,MAAM,GAAGnB,MAAM,CAAC,EAAE,EAAEmB,MAAM,EAAE;MAC1BzF,cAAc,EAAA0F,gBAAA;MACd9E,YAAY,EAAA+E,cAAA;MACZ9D,eAAe,EAAA+D,iBAAA;MACfjD,YAAY,EAAAkD;IACb,EAAC;EACH;EAED,OAAOJ,MAAM;AACf;AAEA;;;AAGG;AACG,SAAUK,gCAAgCA,CAC9CC,OAAiC;EAEjC,OAAO,UACLxH,CAAM,EACNC,CAAM,EACNwH,YAAiB,EACjBC,YAAiB,EACjBC,QAAa,EACbC,QAAa,EACb1H,KAAkB;IAElB,OAAOsH,OAAO,CAACxH,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;EAC7B,CAAC;AACH;AAEA;;AAEG;AACG,SAAU2H,aAAaA,CAAOzB,EAMP;EAL3B,IAAAW,QAAQ,GAAAX,EAAA,CAAAW,QAAA;IACRV,UAAU,GAAAD,EAAA,CAAAC,UAAA;IACVyB,WAAW,GAAA1B,EAAA,CAAA0B,WAAA;IACXlG,MAAM,GAAAwE,EAAA,CAAAxE,MAAA;IACNqF,MAAM,GAAAb,EAAA,CAAAa,MAAA;EAEN,IAAIa,WAAW,EAAE;IACf,OAAO,SAAS/H,OAAOA,CAAOC,CAAI,EAAEC,CAAI;MAChC,IAAAmG,EAAA,GACJ0B,WAAY,EAAE;QADRC,EAAA,GAAA3B,EAAA,CAAA9F,KAA4C;QAA5CA,KAAK,GAAGyH,EAAA,cAAAhB,QAAQ,GAAG,IAAIiB,OAAO,EAAE,GAAGC,SAAS,GAAAF,EAAA;QAAEG,IAAI,GAAA9B,EAAA,CAAA8B,IAC1C;MAEhB,OAAO7B,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAE;QACtBK,KAAK,EAAAA,KAAA;QACLsB,MAAM,EAAAA,MAAA;QACNsG,IAAI,EAAAA,IAAA;QACJjB,MAAM,EAAAA;MACQ,EAAC;IACnB,CAAC;EACF;EAED,IAAIF,QAAQ,EAAE;IACZ,OAAO,SAAShH,OAAOA,CAAOC,CAAI,EAAEC,CAAI;MACtC,OAAOoG,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAE;QACtBK,KAAK,EAAE,IAAI0H,OAAO,EAAE;QACpBpG,MAAM,EAAAA,MAAA;QACNsG,IAAI,EAAED,SAAiB;QACvBhB,MAAM,EAAAA;MACQ,EAAC;IACnB,CAAC;EACF;EAED,IAAM/G,KAAK,GAAG;IACZI,KAAK,EAAE2H,SAAS;IAChBrG,MAAM,EAAAA,MAAA;IACNsG,IAAI,EAAED,SAAS;IACfhB,MAAM,EAAAA;GACQ;EAEhB,OAAO,SAASlH,OAAOA,CAAOC,CAAI,EAAEC,CAAI;IACtC,OAAOoG,UAAU,CAACrG,CAAC,EAAEC,CAAC,EAAEC,KAAK,CAAC;EAChC,CAAC;AACH;;ACtUA;;AAEG;AACU,IAAAiI,SAAS,GAAGC,iBAAiB;AAE1C;;AAEG;AACI,IAAMC,eAAe,GAAGD,iBAAiB,CAAC;EAAEnB,MAAM,EAAE;AAAI,CAAE;AAEjE;;AAEG;AACI,IAAMqB,iBAAiB,GAAGF,iBAAiB,CAAC;EAAErB,QAAQ,EAAE;AAAI,CAAE;AAErE;;;AAGG;AACI,IAAMwB,uBAAuB,GAAGH,iBAAiB,CAAC;EACvDrB,QAAQ,EAAE,IAAI;EACdE,MAAM,EAAE;AACT;AAED;;AAEG;AACI,IAAMuB,YAAY,GAAGJ,iBAAiB,CAAC;EAC5CK,wBAAwB,EAAE,SAAAA,CAAA;IAAM,OAAAtH,kBAAkB;EAAA;AACnD;AAED;;AAEG;AACI,IAAMuH,kBAAkB,GAAGN,iBAAiB,CAAC;EAClDnB,MAAM,EAAE,IAAI;EACZwB,wBAAwB,EAAE,SAAAA,CAAA;IAAM,OAAAtH,kBAAkB;EAAA;AACnD;AAED;;AAEG;AACI,IAAMwH,oBAAoB,GAAGP,iBAAiB,CAAC;EACpDrB,QAAQ,EAAE,IAAI;EACd0B,wBAAwB,EAAE,SAAAA,CAAA;IAAM,OAAAtH,kBAAkB;EAAA;AACnD;AAED;;;AAGG;AACI,IAAMyH,0BAA0B,GAAGR,iBAAiB,CAAC;EAC1DrB,QAAQ,EAAE,IAAI;EACd0B,wBAAwB,EAAE,SAAAA,CAAA;IAAM,OAAAtH,kBAAkB;EAAA;EAClD8F,MAAM,EAAE;AACT;AAED;;;;;;;AAOG;AACG,SAAUmB,iBAAiBA,CAC/BS,OAA6C;EAA7C,IAAAA,OAAA;IAAAA,OAA6C;EAAA;EAG3C,IAAAzC,EAAA,GAIEyC,OAAO,CAAA9B,QAJO;IAAhBA,QAAQ,GAAGX,EAAA,mBAAK,GAAAA,EAAA;IACU0C,8BAA8B,GAGtDD,OAAO,CAAAJ,wBAH+C;IACxDX,WAAW,GAETe,OAAO,CAFEf,WAAA;IACXC,EACE,GAAAc,OAAO,CADK5B,MAAA;IAAdA,MAAM,GAAAc,EAAA,cAAG,KAAK,GAAAA,EAAA;EAGhB,IAAMb,MAAM,GAAGJ,8BAA8B,CAAO+B,OAAO,CAAC;EAC5D,IAAMxC,UAAU,GAAGF,wBAAwB,CAACe,MAAM,CAAC;EACnD,IAAMtF,MAAM,GAAGkH,8BAA8B,GACzCA,8BAA8B,CAACzC,UAAU,CAAC,GAC1CkB,gCAAgC,CAAClB,UAAU,CAAC;EAEhD,OAAOwB,aAAa,CAAC;IAAEd,QAAQ,EAAAA,QAAA;IAAEV,UAAU,EAAAA,UAAA;IAAEyB,WAAW,EAAAA,WAAA;IAAElG,MAAM,EAAAA,MAAA;IAAEqF,MAAM,EAAAA;EAAA,CAAE,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}