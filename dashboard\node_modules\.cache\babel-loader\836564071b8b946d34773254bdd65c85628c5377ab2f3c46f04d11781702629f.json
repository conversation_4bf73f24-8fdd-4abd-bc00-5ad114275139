{"ast": null, "code": "import { rgb as colorRgb } from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, { gamma } from \"./color.js\";\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n      g = color(start.g, end.g),\n      b = color(start.b, end.b),\n      opacity = nogamma(start.opacity, end.opacity);\n    return function (t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n  rgb.gamma = rgbGamma;\n  return rgb;\n})(1);\nfunction rgbSpline(spline) {\n  return function (colors) {\n    var n = colors.length,\n      r = new Array(n),\n      g = new Array(n),\n      b = new Array(n),\n      i,\n      color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function (t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);", "map": {"version": 3, "names": ["rgb", "colorRgb", "basis", "basisClosed", "nogamma", "gamma", "rgbGamma", "y", "color", "start", "end", "r", "g", "b", "opacity", "t", "rgbSpline", "spline", "colors", "n", "length", "Array", "i", "rgbBasis", "rgbBasisClosed"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-interpolate/src/rgb.js"], "sourcesContent": ["import {rgb as colorRgb} from \"d3-color\";\nimport basis from \"./basis.js\";\nimport basisClosed from \"./basisClosed.js\";\nimport nogamma, {gamma} from \"./color.js\";\n\nexport default (function rgbGamma(y) {\n  var color = gamma(y);\n\n  function rgb(start, end) {\n    var r = color((start = colorRgb(start)).r, (end = colorRgb(end)).r),\n        g = color(start.g, end.g),\n        b = color(start.b, end.b),\n        opacity = nogamma(start.opacity, end.opacity);\n    return function(t) {\n      start.r = r(t);\n      start.g = g(t);\n      start.b = b(t);\n      start.opacity = opacity(t);\n      return start + \"\";\n    };\n  }\n\n  rgb.gamma = rgbGamma;\n\n  return rgb;\n})(1);\n\nfunction rgbSpline(spline) {\n  return function(colors) {\n    var n = colors.length,\n        r = new Array(n),\n        g = new Array(n),\n        b = new Array(n),\n        i, color;\n    for (i = 0; i < n; ++i) {\n      color = colorRgb(colors[i]);\n      r[i] = color.r || 0;\n      g[i] = color.g || 0;\n      b[i] = color.b || 0;\n    }\n    r = spline(r);\n    g = spline(g);\n    b = spline(b);\n    color.opacity = 1;\n    return function(t) {\n      color.r = r(t);\n      color.g = g(t);\n      color.b = b(t);\n      return color + \"\";\n    };\n  };\n}\n\nexport var rgbBasis = rgbSpline(basis);\nexport var rgbBasisClosed = rgbSpline(basisClosed);\n"], "mappings": "AAAA,SAAQA,GAAG,IAAIC,QAAQ,QAAO,UAAU;AACxC,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,OAAO,IAAGC,KAAK,QAAO,YAAY;AAEzC,eAAe,CAAC,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnC,IAAIC,KAAK,GAAGH,KAAK,CAACE,CAAC,CAAC;EAEpB,SAASP,GAAGA,CAACS,KAAK,EAAEC,GAAG,EAAE;IACvB,IAAIC,CAAC,GAAGH,KAAK,CAAC,CAACC,KAAK,GAAGR,QAAQ,CAACQ,KAAK,CAAC,EAAEE,CAAC,EAAE,CAACD,GAAG,GAAGT,QAAQ,CAACS,GAAG,CAAC,EAAEC,CAAC,CAAC;MAC/DC,CAAC,GAAGJ,KAAK,CAACC,KAAK,CAACG,CAAC,EAAEF,GAAG,CAACE,CAAC,CAAC;MACzBC,CAAC,GAAGL,KAAK,CAACC,KAAK,CAACI,CAAC,EAAEH,GAAG,CAACG,CAAC,CAAC;MACzBC,OAAO,GAAGV,OAAO,CAACK,KAAK,CAACK,OAAO,EAAEJ,GAAG,CAACI,OAAO,CAAC;IACjD,OAAO,UAASC,CAAC,EAAE;MACjBN,KAAK,CAACE,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;MACdN,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;MACdN,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;MACdN,KAAK,CAACK,OAAO,GAAGA,OAAO,CAACC,CAAC,CAAC;MAC1B,OAAON,KAAK,GAAG,EAAE;IACnB,CAAC;EACH;EAEAT,GAAG,CAACK,KAAK,GAAGC,QAAQ;EAEpB,OAAON,GAAG;AACZ,CAAC,EAAE,CAAC,CAAC;AAEL,SAASgB,SAASA,CAACC,MAAM,EAAE;EACzB,OAAO,UAASC,MAAM,EAAE;IACtB,IAAIC,CAAC,GAAGD,MAAM,CAACE,MAAM;MACjBT,CAAC,GAAG,IAAIU,KAAK,CAACF,CAAC,CAAC;MAChBP,CAAC,GAAG,IAAIS,KAAK,CAACF,CAAC,CAAC;MAChBN,CAAC,GAAG,IAAIQ,KAAK,CAACF,CAAC,CAAC;MAChBG,CAAC;MAAEd,KAAK;IACZ,KAAKc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;MACtBd,KAAK,GAAGP,QAAQ,CAACiB,MAAM,CAACI,CAAC,CAAC,CAAC;MAC3BX,CAAC,CAACW,CAAC,CAAC,GAAGd,KAAK,CAACG,CAAC,IAAI,CAAC;MACnBC,CAAC,CAACU,CAAC,CAAC,GAAGd,KAAK,CAACI,CAAC,IAAI,CAAC;MACnBC,CAAC,CAACS,CAAC,CAAC,GAAGd,KAAK,CAACK,CAAC,IAAI,CAAC;IACrB;IACAF,CAAC,GAAGM,MAAM,CAACN,CAAC,CAAC;IACbC,CAAC,GAAGK,MAAM,CAACL,CAAC,CAAC;IACbC,CAAC,GAAGI,MAAM,CAACJ,CAAC,CAAC;IACbL,KAAK,CAACM,OAAO,GAAG,CAAC;IACjB,OAAO,UAASC,CAAC,EAAE;MACjBP,KAAK,CAACG,CAAC,GAAGA,CAAC,CAACI,CAAC,CAAC;MACdP,KAAK,CAACI,CAAC,GAAGA,CAAC,CAACG,CAAC,CAAC;MACdP,KAAK,CAACK,CAAC,GAAGA,CAAC,CAACE,CAAC,CAAC;MACd,OAAOP,KAAK,GAAG,EAAE;IACnB,CAAC;EACH,CAAC;AACH;AAEA,OAAO,IAAIe,QAAQ,GAAGP,SAAS,CAACd,KAAK,CAAC;AACtC,OAAO,IAAIsB,cAAc,GAAGR,SAAS,CAACb,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}