#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:3002/api';

async function simpleTest() {
    console.log('🧪 Simple API Test\n');

    try {
        // Test 1: Register user
        console.log('=== Test 1: Register User ===');
        const registerResult = await axios.post(`${API_BASE}/auth/register`, {
            username: 'testuser2',
            email: '<EMAIL>',
            password: 'password123'
        });

        if (registerResult.status === 201) {
            console.log('✅ User registered successfully');
            const token = registerResult.data.token;
            
            // Test 2: Create server
            console.log('\n=== Test 2: Create Server ===');
            const serverResult = await axios.post(`${API_BASE}/servers`, {
                name: 'Test Server',
                description: 'A test server'
            }, {
                headers: { Authorization: `Bearer ${token}` }
            });

            if (serverResult.status === 201) {
                console.log('✅ Server created successfully');
                const server = serverResult.data.server;
                console.log(`   Server ID: ${server.id}`);
                console.log(`   Server Name: ${server.name}`);
                
                // Test 3: Get channels
                console.log('\n=== Test 3: Get Channels ===');
                const channelsResult = await axios.get(`${API_BASE}/servers/${server.id}/channels`, {
                    headers: { Authorization: `Bearer ${token}` }
                });

                if (channelsResult.status === 200) {
                    console.log('✅ Channels retrieved successfully');
                    const channels = channelsResult.data.channels;
                    console.log(`   Found ${channels.length} channels`);
                    
                    if (channels.length > 0) {
                        const channel = channels[0];
                        console.log(`   Channel: #${channel.name} (${channel.id})`);
                        
                        // Test 4: Send message
                        console.log('\n=== Test 4: Send Message ===');
                        const messageResult = await axios.post(`${API_BASE}/servers/${server.id}/channels/${channel.id}/messages`, {
                            content: 'Hello, this is a test message!'
                        }, {
                            headers: { Authorization: `Bearer ${token}` }
                        });

                        if (messageResult.status === 201) {
                            console.log('✅ Message sent successfully');
                            
                            // Test 5: Get messages
                            console.log('\n=== Test 5: Get Messages ===');
                            const messagesResult = await axios.get(`${API_BASE}/servers/${server.id}/channels/${channel.id}/messages`, {
                                headers: { Authorization: `Bearer ${token}` }
                            });

                            if (messagesResult.status === 200) {
                                console.log('✅ Messages retrieved successfully');
                                const messages = messagesResult.data.messages;
                                console.log(`   Found ${messages.length} messages`);
                                
                                messages.forEach(msg => {
                                    console.log(`   - ${msg.author.username}: ${msg.content}`);
                                });
                            }
                        }
                    }
                }
            }
        }

        console.log('\n🎉 All tests passed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
        }
    }
}

// Run the test
simpleTest();
