{"ast": null, "code": "var getNative = require('./_getNative'),\n  root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\nmodule.exports = Promise;", "map": {"version": 3, "names": ["getNative", "require", "root", "Promise", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_Promise.js"], "sourcesContent": ["var getNative = require('./_getNative'),\n    root = require('./_root');\n\n/* Built-in method references that are verified to be native. */\nvar Promise = getNative(root, 'Promise');\n\nmodule.exports = Promise;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIE,OAAO,GAAGH,SAAS,CAACE,IAAI,EAAE,SAAS,CAAC;AAExCE,MAAM,CAACC,OAAO,GAAGF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}