{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bot_fake_discord\\\\dashboard\\\\src\\\\components\\\\Chat\\\\ChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, TextField, IconButton, Avatar, Paper, List, ListItem, ListItemText, ListItemAvatar, Divider, Chip, Button, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Send as SendIcon, Add as AddIcon, Settings as SettingsIcon, SmartToy as BotIcon, Person as PersonIcon, MoreVert as MoreIcon } from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { WebSocketService } from '../';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInterface = ({\n  serverId,\n  channelId,\n  onBotInvite\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isConnected, setIsConnected] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [bots, setBots] = useState([]);\n  const [showBotMenu, setShowBotMenu] = useState(false);\n  const [botMenuAnchor, setBotMenuAnchor] = useState(null);\n  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);\n  const [availableBots, setAvailableBots] = useState([]);\n  const messagesEndRef = useRef(null);\n  const wsRef = useRef(null);\n  useEffect(() => {\n    connectToChat();\n    loadMessages();\n    loadChannelBots();\n    return () => {\n      if (wsRef.current) {\n        wsRef.current.disconnect();\n      }\n    };\n  }, [serverId, channelId]);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const connectToChat = () => {\n    if (wsRef.current) {\n      wsRef.current.disconnect();\n    }\n    wsRef.current = new WebSocketService();\n    wsRef.current.on('connected', () => {\n      setIsConnected(true);\n      toast.success('Connected to chat');\n    });\n    wsRef.current.on('disconnected', () => {\n      setIsConnected(false);\n      toast.error('Disconnected from chat');\n    });\n    wsRef.current.on('messageCreate', message => {\n      setMessages(prev => [...prev, message]);\n    });\n    wsRef.current.on('userJoin', userData => {\n      setOnlineUsers(prev => [...prev, userData]);\n    });\n    wsRef.current.on('userLeave', userData => {\n      setOnlineUsers(prev => prev.filter(u => u.id !== userData.id));\n    });\n    wsRef.current.on('botJoin', botData => {\n      setBots(prev => [...prev, botData]);\n      toast.success(`Bot ${botData.name} joined the channel`);\n    });\n    wsRef.current.connect(user.token);\n  };\n  const loadMessages = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages || []);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n  const loadChannelBots = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots || []);\n      }\n    } catch (error) {\n      console.error('Error loading channel bots:', error);\n    }\n  };\n  const loadAvailableBots = async () => {\n    try {\n      const response = await fetch('/api/applications', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setAvailableBots(data.applications.filter(app => app.status === 'active'));\n      }\n    } catch (error) {\n      console.error('Error loading available bots:', error);\n    }\n  };\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !isConnected) return;\n    try {\n      const messageData = {\n        content: newMessage,\n        channel_id: channelId,\n        server_id: serverId\n      };\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(messageData)\n      });\n      if (response.ok) {\n        setNewMessage('');\n      } else {\n        toast.error('Failed to send message');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      toast.error('Failed to send message');\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  const inviteBot = async botId => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          bot_id: botId\n        })\n      });\n      if (response.ok) {\n        toast.success('Bot invited successfully');\n        loadChannelBots();\n        setShowInviteBotDialog(false);\n\n        // Trigger bot invite callback for code editor integration\n        if (onBotInvite) {\n          const botData = availableBots.find(bot => bot.id === botId);\n          onBotInvite(botData);\n        }\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to invite bot');\n      }\n    } catch (error) {\n      console.error('Error inviting bot:', error);\n      toast.error('Failed to invite bot');\n    }\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const getMessageTime = timestamp => {\n    return formatDistanceToNow(new Date(timestamp), {\n      addSuffix: true\n    });\n  };\n  const isBot = author => {\n    return author.bot || bots.some(bot => bot.id === author.id);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: 1,\n        borderColor: 'divider',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"# general-chat\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: isConnected ? 'Connected' : 'Disconnected',\n          color: isConnected ? 'success' : 'error',\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            loadAvailableBots();\n            setShowInviteBotDialog(true);\n          },\n          size: \"small\",\n          children: \"Invite Bot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: e => {\n            setBotMenuAnchor(e.currentTarget);\n            setShowBotMenu(true);\n          },\n          children: /*#__PURE__*/_jsxDEV(MoreIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        p: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(List, {\n        children: messages.map((message, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          alignItems: \"flex-start\",\n          children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: isBot(message.author) ? 'primary.main' : 'secondary.main'\n              },\n              children: isBot(message.author) ? /*#__PURE__*/_jsxDEV(BotIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 44\n              }, this) : /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 58\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                fontWeight: \"bold\",\n                children: message.author.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 21\n              }, this), isBot(message.author) && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"BOT\",\n                size: \"small\",\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                color: \"text.secondary\",\n                children: getMessageTime(message.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 19\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                mt: 0.5,\n                whiteSpace: 'pre-wrap'\n              },\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, message.id || index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderTop: 1,\n        borderColor: 'divider'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          maxRows: 4,\n          placeholder: \"Type a message...\",\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          disabled: !isConnected\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: sendMessage,\n          disabled: !newMessage.trim() || !isConnected,\n          color: \"primary\",\n          children: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: botMenuAnchor,\n      open: showBotMenu,\n      onClose: () => setShowBotMenu(false),\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setShowInviteBotDialog(true),\n        children: [/*#__PURE__*/_jsxDEV(AddIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), \"Invite Bot\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), \"Channel Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showInviteBotDialog,\n      onClose: () => setShowInviteBotDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Invite Bot to Channel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: availableBots.map(bot => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                children: /*#__PURE__*/_jsxDEV(BotIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: bot.name,\n              secondary: bot.description || 'No description'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"small\",\n              onClick: () => inviteBot(bot.id),\n              disabled: bots.some(b => b.id === bot.id),\n              children: bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this)]\n          }, bot.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowInviteBotDialog(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"WV2D1Xj+KCaCF5rmGlxJQ7v7J4M=\", false, function () {\n  return [useAuth];\n});\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "TextField", "IconButton", "Avatar", "Paper", "List", "ListItem", "ListItemText", "ListItemAvatar", "Divider", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Send", "SendIcon", "Add", "AddIcon", "Settings", "SettingsIcon", "SmartToy", "BotIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreIcon", "formatDistanceToNow", "useAuth", "WebSocketService", "toast", "jsxDEV", "_jsxDEV", "ChatInterface", "serverId", "channelId", "onBotInvite", "_s", "user", "messages", "setMessages", "newMessage", "setNewMessage", "isConnected", "setIsConnected", "onlineUsers", "setOnlineUsers", "bots", "setBots", "showBotMenu", "setShowBotMenu", "botMenuAnchor", "setBotMenuAnchor", "showInviteBotDialog", "setShowInviteBotDialog", "availableBots", "setAvailableBots", "messagesEndRef", "wsRef", "connectToChat", "loadMessages", "loadChannelBots", "current", "disconnect", "scrollToBottom", "on", "success", "error", "message", "prev", "userData", "filter", "u", "id", "botData", "name", "connect", "token", "response", "fetch", "headers", "localStorage", "getItem", "ok", "data", "json", "console", "loadAvailableBots", "applications", "app", "status", "sendMessage", "trim", "messageData", "content", "channel_id", "server_id", "method", "body", "JSON", "stringify", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "inviteBot", "botId", "bot_id", "find", "bot", "errorData", "_messagesEndRef$curre", "scrollIntoView", "behavior", "getMessageTime", "timestamp", "Date", "addSuffix", "isBot", "author", "some", "sx", "height", "display", "flexDirection", "children", "p", "borderBottom", "borderColor", "justifyContent", "alignItems", "gap", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "color", "size", "startIcon", "onClick", "currentTarget", "flex", "overflow", "map", "index", "bgcolor", "primary", "fontWeight", "username", "created_at", "secondary", "mt", "whiteSpace", "ref", "borderTop", "fullWidth", "multiline", "maxRows", "placeholder", "value", "onChange", "target", "onKeyPress", "disabled", "anchorEl", "open", "onClose", "mr", "max<PERSON><PERSON><PERSON>", "description", "b", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/components/Chat/ChatInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  IconButton,\n  Avatar,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Divider,\n  Chip,\n  Button,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n} from '@mui/material';\nimport {\n  Send as SendIcon,\n  Add as AddIcon,\n  Settings as SettingsIcon,\n  SmartToy as BotIcon,\n  Person as PersonIcon,\n  MoreVert as MoreIcon,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { WebSocketService } from '../';\nimport toast from 'react-hot-toast';\n\nconst ChatInterface = ({ serverId, channelId, onBotInvite }) => {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isConnected, setIsConnected] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState([]);\n  const [bots, setBots] = useState([]);\n  const [showBotMenu, setShowBotMenu] = useState(false);\n  const [botMenuAnchor, setBotMenuAnchor] = useState(null);\n  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);\n  const [availableBots, setAvailableBots] = useState([]);\n  \n  const messagesEndRef = useRef(null);\n  const wsRef = useRef(null);\n\n  useEffect(() => {\n    connectToChat();\n    loadMessages();\n    loadChannelBots();\n    \n    return () => {\n      if (wsRef.current) {\n        wsRef.current.disconnect();\n      }\n    };\n  }, [serverId, channelId]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const connectToChat = () => {\n    if (wsRef.current) {\n      wsRef.current.disconnect();\n    }\n\n    wsRef.current = new WebSocketService();\n    \n    wsRef.current.on('connected', () => {\n      setIsConnected(true);\n      toast.success('Connected to chat');\n    });\n\n    wsRef.current.on('disconnected', () => {\n      setIsConnected(false);\n      toast.error('Disconnected from chat');\n    });\n\n    wsRef.current.on('messageCreate', (message) => {\n      setMessages(prev => [...prev, message]);\n    });\n\n    wsRef.current.on('userJoin', (userData) => {\n      setOnlineUsers(prev => [...prev, userData]);\n    });\n\n    wsRef.current.on('userLeave', (userData) => {\n      setOnlineUsers(prev => prev.filter(u => u.id !== userData.id));\n    });\n\n    wsRef.current.on('botJoin', (botData) => {\n      setBots(prev => [...prev, botData]);\n      toast.success(`Bot ${botData.name} joined the channel`);\n    });\n\n    wsRef.current.connect(user.token);\n  };\n\n  const loadMessages = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages || []);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n\n  const loadChannelBots = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots || []);\n      }\n    } catch (error) {\n      console.error('Error loading channel bots:', error);\n    }\n  };\n\n  const loadAvailableBots = async () => {\n    try {\n      const response = await fetch('/api/applications', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setAvailableBots(data.applications.filter(app => app.status === 'active'));\n      }\n    } catch (error) {\n      console.error('Error loading available bots:', error);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !isConnected) return;\n\n    try {\n      const messageData = {\n        content: newMessage,\n        channel_id: channelId,\n        server_id: serverId\n      };\n\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify(messageData)\n      });\n\n      if (response.ok) {\n        setNewMessage('');\n      } else {\n        toast.error('Failed to send message');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      toast.error('Failed to send message');\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const inviteBot = async (botId) => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({ bot_id: botId })\n      });\n\n      if (response.ok) {\n        toast.success('Bot invited successfully');\n        loadChannelBots();\n        setShowInviteBotDialog(false);\n        \n        // Trigger bot invite callback for code editor integration\n        if (onBotInvite) {\n          const botData = availableBots.find(bot => bot.id === botId);\n          onBotInvite(botData);\n        }\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to invite bot');\n      }\n    } catch (error) {\n      console.error('Error inviting bot:', error);\n      toast.error('Failed to invite bot');\n    }\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const getMessageTime = (timestamp) => {\n    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });\n  };\n\n  const isBot = (author) => {\n    return author.bot || bots.some(bot => bot.id === author.id);\n  };\n\n  return (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Chat Header */}\n      <Box \n        sx={{ \n          p: 2, \n          borderBottom: 1, \n          borderColor: 'divider',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}\n      >\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <Typography variant=\"h6\">\n            # general-chat\n          </Typography>\n          <Chip \n            label={isConnected ? 'Connected' : 'Disconnected'}\n            color={isConnected ? 'success' : 'error'}\n            size=\"small\"\n          />\n        </Box>\n        \n        <Box display=\"flex\" gap={1}>\n          <Button\n            startIcon={<AddIcon />}\n            onClick={() => {\n              loadAvailableBots();\n              setShowInviteBotDialog(true);\n            }}\n            size=\"small\"\n          >\n            Invite Bot\n          </Button>\n          \n          <IconButton \n            onClick={(e) => {\n              setBotMenuAnchor(e.currentTarget);\n              setShowBotMenu(true);\n            }}\n          >\n            <MoreIcon />\n          </IconButton>\n        </Box>\n      </Box>\n\n      {/* Messages Area */}\n      <Box sx={{ flex: 1, overflow: 'auto', p: 1 }}>\n        <List>\n          {messages.map((message, index) => (\n            <ListItem key={message.id || index} alignItems=\"flex-start\">\n              <ListItemAvatar>\n                <Avatar sx={{ bgcolor: isBot(message.author) ? 'primary.main' : 'secondary.main' }}>\n                  {isBot(message.author) ? <BotIcon /> : <PersonIcon />}\n                </Avatar>\n              </ListItemAvatar>\n              <ListItemText\n                primary={\n                  <Box display=\"flex\" alignItems=\"center\" gap={1}>\n                    <Typography variant=\"subtitle2\" fontWeight=\"bold\">\n                      {message.author.username}\n                    </Typography>\n                    {isBot(message.author) && (\n                      <Chip label=\"BOT\" size=\"small\" color=\"primary\" />\n                    )}\n                    <Typography variant=\"caption\" color=\"text.secondary\">\n                      {getMessageTime(message.created_at)}\n                    </Typography>\n                  </Box>\n                }\n                secondary={\n                  <Typography variant=\"body2\" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>\n                    {message.content}\n                  </Typography>\n                }\n              />\n            </ListItem>\n          ))}\n        </List>\n        <div ref={messagesEndRef} />\n      </Box>\n\n      {/* Message Input */}\n      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>\n        <Box display=\"flex\" gap={1}>\n          <TextField\n            fullWidth\n            multiline\n            maxRows={4}\n            placeholder=\"Type a message...\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            disabled={!isConnected}\n          />\n          <IconButton \n            onClick={sendMessage}\n            disabled={!newMessage.trim() || !isConnected}\n            color=\"primary\"\n          >\n            <SendIcon />\n          </IconButton>\n        </Box>\n      </Box>\n\n      {/* Bot Menu */}\n      <Menu\n        anchorEl={botMenuAnchor}\n        open={showBotMenu}\n        onClose={() => setShowBotMenu(false)}\n      >\n        <MenuItem onClick={() => setShowInviteBotDialog(true)}>\n          <AddIcon sx={{ mr: 1 }} />\n          Invite Bot\n        </MenuItem>\n        <MenuItem>\n          <SettingsIcon sx={{ mr: 1 }} />\n          Channel Settings\n        </MenuItem>\n      </Menu>\n\n      {/* Invite Bot Dialog */}\n      <Dialog \n        open={showInviteBotDialog} \n        onClose={() => setShowInviteBotDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>Invite Bot to Channel</DialogTitle>\n        <DialogContent>\n          <List>\n            {availableBots.map((bot) => (\n              <ListItem key={bot.id}>\n                <ListItemAvatar>\n                  <Avatar>\n                    <BotIcon />\n                  </Avatar>\n                </ListItemAvatar>\n                <ListItemText\n                  primary={bot.name}\n                  secondary={bot.description || 'No description'}\n                />\n                <Button\n                  variant=\"contained\"\n                  size=\"small\"\n                  onClick={() => inviteBot(bot.id)}\n                  disabled={bots.some(b => b.id === bot.id)}\n                >\n                  {bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'}\n                </Button>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowInviteBotDialog(false)}>\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ChatInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,OAAO,EACPC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,OAAO,EACnBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,QAAQ,QACf,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,gBAAgB,QAAQ,KAAK;AACtC,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM;IAAEC;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMgE,cAAc,GAAG9D,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+D,KAAK,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAE1BD,SAAS,CAAC,MAAM;IACdiE,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;IACdC,eAAe,CAAC,CAAC;IAEjB,OAAO,MAAM;MACX,IAAIH,KAAK,CAACI,OAAO,EAAE;QACjBJ,KAAK,CAACI,OAAO,CAACC,UAAU,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAAC7B,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAEzBzC,SAAS,CAAC,MAAM;IACdsE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACzB,QAAQ,CAAC,CAAC;EAEd,MAAMoB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAID,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAACC,UAAU,CAAC,CAAC;IAC5B;IAEAL,KAAK,CAACI,OAAO,GAAG,IAAIjC,gBAAgB,CAAC,CAAC;IAEtC6B,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,WAAW,EAAE,MAAM;MAClCrB,cAAc,CAAC,IAAI,CAAC;MACpBd,KAAK,CAACoC,OAAO,CAAC,mBAAmB,CAAC;IACpC,CAAC,CAAC;IAEFR,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,cAAc,EAAE,MAAM;MACrCrB,cAAc,CAAC,KAAK,CAAC;MACrBd,KAAK,CAACqC,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,CAAC;IAEFT,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,eAAe,EAAGG,OAAO,IAAK;MAC7C5B,WAAW,CAAC6B,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC,CAAC;IACzC,CAAC,CAAC;IAEFV,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,UAAU,EAAGK,QAAQ,IAAK;MACzCxB,cAAc,CAACuB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEC,QAAQ,CAAC,CAAC;IAC7C,CAAC,CAAC;IAEFZ,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,WAAW,EAAGK,QAAQ,IAAK;MAC1CxB,cAAc,CAACuB,IAAI,IAAIA,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKH,QAAQ,CAACG,EAAE,CAAC,CAAC;IAChE,CAAC,CAAC;IAEFf,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,SAAS,EAAGS,OAAO,IAAK;MACvC1B,OAAO,CAACqB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEK,OAAO,CAAC,CAAC;MACnC5C,KAAK,CAACoC,OAAO,CAAC,OAAOQ,OAAO,CAACC,IAAI,qBAAqB,CAAC;IACzD,CAAC,CAAC;IAEFjB,KAAK,CAACI,OAAO,CAACc,OAAO,CAACtC,IAAI,CAACuC,KAAK,CAAC;EACnC,CAAC;EAED,MAAMjB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgB7C,QAAQ,aAAaC,SAAS,WAAW,EAAE;QACtF6C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClC7C,WAAW,CAAC4C,IAAI,CAAC7C,QAAQ,IAAI,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMN,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMiB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgB7C,QAAQ,aAAaC,SAAS,OAAO,EAAE;QAClF6C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCrC,OAAO,CAACoC,IAAI,CAACrC,IAAI,IAAI,EAAE,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMoB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;QAChDC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIJ,QAAQ,CAACK,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClC7B,gBAAgB,CAAC4B,IAAI,CAACI,YAAY,CAACjB,MAAM,CAACkB,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,QAAQ,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMwB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAClD,UAAU,CAACmD,IAAI,CAAC,CAAC,IAAI,CAACjD,WAAW,EAAE;IAExC,IAAI;MACF,MAAMkD,WAAW,GAAG;QAClBC,OAAO,EAAErD,UAAU;QACnBsD,UAAU,EAAE5D,SAAS;QACrB6D,SAAS,EAAE9D;MACb,CAAC;MAED,MAAM4C,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgB7C,QAAQ,aAAaC,SAAS,WAAW,EAAE;QACtF8D,MAAM,EAAE,MAAM;QACdjB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,WAAW;MAClC,CAAC,CAAC;MAEF,IAAIf,QAAQ,CAACK,EAAE,EAAE;QACfzC,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACLZ,KAAK,CAACqC,KAAK,CAAC,wBAAwB,CAAC;MACvC;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CrC,KAAK,CAACqC,KAAK,CAAC,wBAAwB,CAAC;IACvC;EACF,CAAC;EAED,MAAMkC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBd,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMe,SAAS,GAAG,MAAOC,KAAK,IAAK;IACjC,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgB7C,QAAQ,aAAaC,SAAS,OAAO,EAAE;QAClF8D,MAAM,EAAE,MAAM;QACdjB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDgB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEQ,MAAM,EAAED;QAAM,CAAC;MACxC,CAAC,CAAC;MAEF,IAAI7B,QAAQ,CAACK,EAAE,EAAE;QACfrD,KAAK,CAACoC,OAAO,CAAC,0BAA0B,CAAC;QACzCL,eAAe,CAAC,CAAC;QACjBP,sBAAsB,CAAC,KAAK,CAAC;;QAE7B;QACA,IAAIlB,WAAW,EAAE;UACf,MAAMsC,OAAO,GAAGnB,aAAa,CAACsD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACrC,EAAE,KAAKkC,KAAK,CAAC;UAC3DvE,WAAW,CAACsC,OAAO,CAAC;QACtB;MACF,CAAC,MAAM;QACL,MAAMqC,SAAS,GAAG,MAAMjC,QAAQ,CAACO,IAAI,CAAC,CAAC;QACvCvD,KAAK,CAACqC,KAAK,CAAC4C,SAAS,CAAC5C,KAAK,IAAI,sBAAsB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdmB,OAAO,CAACnB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3CrC,KAAK,CAACqC,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EAED,MAAMH,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAgD,qBAAA;IAC3B,CAAAA,qBAAA,GAAAvD,cAAc,CAACK,OAAO,cAAAkD,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,cAAc,GAAIC,SAAS,IAAK;IACpC,OAAOzF,mBAAmB,CAAC,IAAI0F,IAAI,CAACD,SAAS,CAAC,EAAE;MAAEE,SAAS,EAAE;IAAK,CAAC,CAAC;EACtE,CAAC;EAED,MAAMC,KAAK,GAAIC,MAAM,IAAK;IACxB,OAAOA,MAAM,CAACV,GAAG,IAAI/D,IAAI,CAAC0E,IAAI,CAACX,GAAG,IAAIA,GAAG,CAACrC,EAAE,KAAK+C,MAAM,CAAC/C,EAAE,CAAC;EAC7D,CAAC;EAED,oBACEzC,OAAA,CAACpC,GAAG;IAAC8H,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpE9F,OAAA,CAACpC,GAAG;MACF8H,EAAE,EAAE;QACFK,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,SAAS;QACtBL,OAAO,EAAE,MAAM;QACfM,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEF9F,OAAA,CAACpC,GAAG;QAACgI,OAAO,EAAC,MAAM;QAACO,UAAU,EAAC,QAAQ;QAACC,GAAG,EAAE,CAAE;QAAAN,QAAA,gBAC7C9F,OAAA,CAACnC,UAAU;UAACwI,OAAO,EAAC,IAAI;UAAAP,QAAA,EAAC;QAEzB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbzG,OAAA,CAACzB,IAAI;UACHmI,KAAK,EAAE/F,WAAW,GAAG,WAAW,GAAG,cAAe;UAClDgG,KAAK,EAAEhG,WAAW,GAAG,SAAS,GAAG,OAAQ;UACzCiG,IAAI,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENzG,OAAA,CAACpC,GAAG;QAACgI,OAAO,EAAC,MAAM;QAACQ,GAAG,EAAE,CAAE;QAAAN,QAAA,gBACzB9F,OAAA,CAACxB,MAAM;UACLqI,SAAS,eAAE7G,OAAA,CAACd,OAAO;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBK,OAAO,EAAEA,CAAA,KAAM;YACbvD,iBAAiB,CAAC,CAAC;YACnBjC,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACFsF,IAAI,EAAC,OAAO;UAAAd,QAAA,EACb;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETzG,OAAA,CAACjC,UAAU;UACT+I,OAAO,EAAGxC,CAAC,IAAK;YACdlD,gBAAgB,CAACkD,CAAC,CAACyC,aAAa,CAAC;YACjC7F,cAAc,CAAC,IAAI,CAAC;UACtB,CAAE;UAAA4E,QAAA,eAEF9F,OAAA,CAACN,QAAQ;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA,CAACpC,GAAG;MAAC8H,EAAE,EAAE;QAAEsB,IAAI,EAAE,CAAC;QAAEC,QAAQ,EAAE,MAAM;QAAElB,CAAC,EAAE;MAAE,CAAE;MAAAD,QAAA,gBAC3C9F,OAAA,CAAC9B,IAAI;QAAA4H,QAAA,EACFvF,QAAQ,CAAC2G,GAAG,CAAC,CAAC9E,OAAO,EAAE+E,KAAK,kBAC3BnH,OAAA,CAAC7B,QAAQ;UAA2BgI,UAAU,EAAC,YAAY;UAAAL,QAAA,gBACzD9F,OAAA,CAAC3B,cAAc;YAAAyH,QAAA,eACb9F,OAAA,CAAChC,MAAM;cAAC0H,EAAE,EAAE;gBAAE0B,OAAO,EAAE7B,KAAK,CAACnD,OAAO,CAACoD,MAAM,CAAC,GAAG,cAAc,GAAG;cAAiB,CAAE;cAAAM,QAAA,EAChFP,KAAK,CAACnD,OAAO,CAACoD,MAAM,CAAC,gBAAGxF,OAAA,CAACV,OAAO;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGzG,OAAA,CAACR,UAAU;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACjBzG,OAAA,CAAC5B,YAAY;YACXiJ,OAAO,eACLrH,OAAA,CAACpC,GAAG;cAACgI,OAAO,EAAC,MAAM;cAACO,UAAU,EAAC,QAAQ;cAACC,GAAG,EAAE,CAAE;cAAAN,QAAA,gBAC7C9F,OAAA,CAACnC,UAAU;gBAACwI,OAAO,EAAC,WAAW;gBAACiB,UAAU,EAAC,MAAM;gBAAAxB,QAAA,EAC9C1D,OAAO,CAACoD,MAAM,CAAC+B;cAAQ;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,EACZlB,KAAK,CAACnD,OAAO,CAACoD,MAAM,CAAC,iBACpBxF,OAAA,CAACzB,IAAI;gBAACmI,KAAK,EAAC,KAAK;gBAACE,IAAI,EAAC,OAAO;gBAACD,KAAK,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACjD,eACDzG,OAAA,CAACnC,UAAU;gBAACwI,OAAO,EAAC,SAAS;gBAACM,KAAK,EAAC,gBAAgB;gBAAAb,QAAA,EACjDX,cAAc,CAAC/C,OAAO,CAACoF,UAAU;cAAC;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;YACDgB,SAAS,eACPzH,OAAA,CAACnC,UAAU;cAACwI,OAAO,EAAC,OAAO;cAACX,EAAE,EAAE;gBAAEgC,EAAE,EAAE,GAAG;gBAAEC,UAAU,EAAE;cAAW,CAAE;cAAA7B,QAAA,EACjE1D,OAAO,CAAC0B;YAAO;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GAzBWrE,OAAO,CAACK,EAAE,IAAI0E,KAAK;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA0BxB,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACPzG,OAAA;QAAK4H,GAAG,EAAEnG;MAAe;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNzG,OAAA,CAACpC,GAAG;MAAC8H,EAAE,EAAE;QAAEK,CAAC,EAAE,CAAC;QAAE8B,SAAS,EAAE,CAAC;QAAE5B,WAAW,EAAE;MAAU,CAAE;MAAAH,QAAA,eACtD9F,OAAA,CAACpC,GAAG;QAACgI,OAAO,EAAC,MAAM;QAACQ,GAAG,EAAE,CAAE;QAAAN,QAAA,gBACzB9F,OAAA,CAAClC,SAAS;UACRgK,SAAS;UACTC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,WAAW,EAAC,mBAAmB;UAC/BC,KAAK,EAAEzH,UAAW;UAClB0H,QAAQ,EAAG7D,CAAC,IAAK5D,aAAa,CAAC4D,CAAC,CAAC8D,MAAM,CAACF,KAAK,CAAE;UAC/CG,UAAU,EAAEhE,cAAe;UAC3BiE,QAAQ,EAAE,CAAC3H;QAAY;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACFzG,OAAA,CAACjC,UAAU;UACT+I,OAAO,EAAEnD,WAAY;UACrB2E,QAAQ,EAAE,CAAC7H,UAAU,CAACmD,IAAI,CAAC,CAAC,IAAI,CAACjD,WAAY;UAC7CgG,KAAK,EAAC,SAAS;UAAAb,QAAA,eAEf9F,OAAA,CAAChB,QAAQ;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA,CAACvB,IAAI;MACH8J,QAAQ,EAAEpH,aAAc;MACxBqH,IAAI,EAAEvH,WAAY;MAClBwH,OAAO,EAAEA,CAAA,KAAMvH,cAAc,CAAC,KAAK,CAAE;MAAA4E,QAAA,gBAErC9F,OAAA,CAACtB,QAAQ;QAACoI,OAAO,EAAEA,CAAA,KAAMxF,sBAAsB,CAAC,IAAI,CAAE;QAAAwE,QAAA,gBACpD9F,OAAA,CAACd,OAAO;UAACwG,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,cAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXzG,OAAA,CAACtB,QAAQ;QAAAoH,QAAA,gBACP9F,OAAA,CAACZ,YAAY;UAACsG,EAAE,EAAE;YAAEgD,EAAE,EAAE;UAAE;QAAE;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPzG,OAAA,CAACrB,MAAM;MACL6J,IAAI,EAAEnH,mBAAoB;MAC1BoH,OAAO,EAAEA,CAAA,KAAMnH,sBAAsB,CAAC,KAAK,CAAE;MAC7CqH,QAAQ,EAAC,IAAI;MACbb,SAAS;MAAAhC,QAAA,gBAET9F,OAAA,CAACpB,WAAW;QAAAkH,QAAA,EAAC;MAAqB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChDzG,OAAA,CAACnB,aAAa;QAAAiH,QAAA,eACZ9F,OAAA,CAAC9B,IAAI;UAAA4H,QAAA,EACFvE,aAAa,CAAC2F,GAAG,CAAEpC,GAAG,iBACrB9E,OAAA,CAAC7B,QAAQ;YAAA2H,QAAA,gBACP9F,OAAA,CAAC3B,cAAc;cAAAyH,QAAA,eACb9F,OAAA,CAAChC,MAAM;gBAAA8H,QAAA,eACL9F,OAAA,CAACV,OAAO;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACjBzG,OAAA,CAAC5B,YAAY;cACXiJ,OAAO,EAAEvC,GAAG,CAACnC,IAAK;cAClB8E,SAAS,EAAE3C,GAAG,CAAC8D,WAAW,IAAI;YAAiB;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACFzG,OAAA,CAACxB,MAAM;cACL6H,OAAO,EAAC,WAAW;cACnBO,IAAI,EAAC,OAAO;cACZE,OAAO,EAAEA,CAAA,KAAMpC,SAAS,CAACI,GAAG,CAACrC,EAAE,CAAE;cACjC6F,QAAQ,EAAEvH,IAAI,CAAC0E,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAACpG,EAAE,KAAKqC,GAAG,CAACrC,EAAE,CAAE;cAAAqD,QAAA,EAEzC/E,IAAI,CAAC0E,IAAI,CAACoD,CAAC,IAAIA,CAAC,CAACpG,EAAE,KAAKqC,GAAG,CAACrC,EAAE,CAAC,GAAG,OAAO,GAAG;YAAQ;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA,GAjBI3B,GAAG,CAACrC,EAAE;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBzG,OAAA,CAAClB,aAAa;QAAAgH,QAAA,eACZ9F,OAAA,CAACxB,MAAM;UAACsI,OAAO,EAAEA,CAAA,KAAMxF,sBAAsB,CAAC,KAAK,CAAE;UAAAwE,QAAA,EAAC;QAEtD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpG,EAAA,CA1WIJ,aAAa;EAAA,QACAL,OAAO;AAAA;AAAAkJ,EAAA,GADpB7I,aAAa;AA4WnB,eAAeA,aAAa;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}