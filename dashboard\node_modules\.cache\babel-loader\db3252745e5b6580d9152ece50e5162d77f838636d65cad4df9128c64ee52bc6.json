{"ast": null, "code": "/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n  constructor() {\n    this.ws = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n    this.eventHandlers = new Map();\n    this.messageQueue = [];\n    this.heartbeatInterval = null;\n    this.lastHeartbeat = null;\n  }\n\n  /**\n   * Connect to WebSocket server\n   * @param {string} url - WebSocket URL\n   * @param {string} token - Authentication token\n   */\n  connect(url = 'ws://localhost:3003/chat', token = null) {\n    try {\n      console.log('🔌 Connecting to WebSocket:', url);\n      this.ws = new WebSocket(url);\n      this.ws.onopen = () => {\n        console.log('✅ WebSocket connected');\n        this.isConnected = true;\n        this.reconnectAttempts = 0;\n\n        // Send authentication if token provided\n        if (token) {\n          this.authenticate(token);\n        }\n\n        // Send queued messages\n        this.flushMessageQueue();\n        this.emit('connected');\n      };\n      this.ws.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('📨 WebSocket message:', data);\n\n          // Handle different message types for chat server\n          switch (data.type) {\n            case 'hello':\n              this.emit('connected');\n              break;\n            case 'authenticated':\n              this.emit('authenticated', data.data);\n              break;\n            case 'auth_error':\n              this.emit('authError', data.data);\n              break;\n            case 'channel_joined':\n              this.emit('channelJoined', data.data);\n              break;\n            case 'message_create':\n              this.emit('messageCreate', data.data);\n              break;\n            case 'pong':\n              this.lastHeartbeat = Date.now();\n              break;\n            case 'error':\n              this.emit('error', data.data);\n              break;\n            default:\n              console.log('Unknown WebSocket message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      this.ws.onclose = event => {\n        console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n        this.isConnected = false;\n        this.stopHeartbeat();\n        this.emit('disconnected', {\n          code: event.code,\n          reason: event.reason\n        });\n\n        // Attempt to reconnect\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n          this.scheduleReconnect();\n        }\n      };\n      this.ws.onerror = error => {\n        console.error('❌ WebSocket error:', error);\n        this.emit('error', error);\n      };\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      this.emit('error', error);\n    }\n  }\n\n  /**\n   * Disconnect from WebSocket\n   */\n  disconnect() {\n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n    this.isConnected = false;\n    this.stopHeartbeat();\n  }\n\n  /**\n   * Send authentication message\n   * @param {string} token - Authentication token\n   */\n  authenticate(token) {\n    this.send({\n      type: 'authenticate',\n      data: {\n        token: token\n      }\n    });\n  }\n\n  /**\n   * Send message to WebSocket\n   * @param {Object} data - Message data\n   */\n  send(data) {\n    if (this.isConnected && this.ws) {\n      try {\n        this.ws.send(JSON.stringify(data));\n      } catch (error) {\n        console.error('Error sending WebSocket message:', error);\n      }\n    } else {\n      // Queue message for later\n      this.messageQueue.push(data);\n    }\n  }\n\n  /**\n   * Join a channel\n   * @param {string} serverId - Server ID\n   * @param {string} channelId - Channel ID\n   */\n  joinChannel(serverId, channelId) {\n    this.send({\n      type: 'join_channel',\n      data: {\n        serverId,\n        channelId\n      }\n    });\n  }\n\n  /**\n   * Send a chat message\n   * @param {string} serverId - Server ID\n   * @param {string} channelId - Channel ID\n   * @param {string} content - Message content\n   */\n  sendChatMessage(serverId, channelId, content) {\n    this.send({\n      type: 'send_message',\n      data: {\n        serverId,\n        channelId,\n        content\n      }\n    });\n  }\n\n  /**\n   * Send ping to keep connection alive\n   */\n  ping() {\n    this.send({\n      type: 'ping',\n      data: {\n        timestamp: Date.now()\n      }\n    });\n  }\n\n  /**\n   * Handle dispatch messages (events)\n   * @param {Object} data - Message data\n   */\n  handleDispatch(data) {\n    const {\n      t: eventType,\n      d: eventData\n    } = data;\n    switch (eventType) {\n      case 'MESSAGE_CREATE':\n        this.emit('messageCreate', eventData);\n        break;\n      case 'MESSAGE_UPDATE':\n        this.emit('messageUpdate', eventData);\n        break;\n      case 'MESSAGE_DELETE':\n        this.emit('messageDelete', eventData);\n        break;\n      case 'CHANNEL_CREATE':\n        this.emit('channelCreate', eventData);\n        break;\n      case 'CHANNEL_UPDATE':\n        this.emit('channelUpdate', eventData);\n        break;\n      case 'CHANNEL_DELETE':\n        this.emit('channelDelete', eventData);\n        break;\n      case 'GUILD_CREATE':\n        this.emit('guildCreate', eventData);\n        break;\n      case 'GUILD_UPDATE':\n        this.emit('guildUpdate', eventData);\n        break;\n      case 'GUILD_DELETE':\n        this.emit('guildDelete', eventData);\n        break;\n      case 'READY':\n        this.emit('ready', eventData);\n        break;\n      default:\n        this.emit('event', {\n          type: eventType,\n          data: eventData\n        });\n    }\n  }\n\n  /**\n   * Handle heartbeat\n   * @param {Object} data - Message data\n   */\n  handleHeartbeat(data) {\n    // Send heartbeat response\n    this.send({\n      op: 1,\n      d: this.lastHeartbeat\n    });\n  }\n\n  /**\n   * Handle hello message\n   * @param {Object} data - Message data\n   */\n  handleHello(data) {\n    const {\n      heartbeat_interval\n    } = data.d;\n    this.heartbeatInterval = heartbeat_interval;\n    console.log('💓 Heartbeat interval:', heartbeat_interval);\n  }\n\n  /**\n   * Handle heartbeat acknowledgment\n   * @param {Object} data - Message data\n   */\n  handleHeartbeatAck(data) {\n    this.lastHeartbeat = Date.now();\n  }\n\n  /**\n   * Handle invalid session\n   * @param {Object} data - Message data\n   */\n  handleInvalidSession(data) {\n    console.error('Invalid WebSocket session');\n    this.emit('invalidSession', data);\n  }\n\n  /**\n   * Start heartbeat timer\n   */\n  startHeartbeat() {\n    if (this.heartbeatInterval) {\n      this.heartbeatTimer = setInterval(() => {\n        this.send({\n          op: 1,\n          d: this.lastHeartbeat\n        });\n      }, this.heartbeatInterval);\n    }\n  }\n\n  /**\n   * Stop heartbeat timer\n   */\n  stopHeartbeat() {\n    if (this.heartbeatTimer) {\n      clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = null;\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Flush queued messages\n   */\n  flushMessageQueue() {\n    while (this.messageQueue.length > 0) {\n      const message = this.messageQueue.shift();\n      this.send(message);\n    }\n  }\n\n  /**\n   * Register event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  on(event, handler) {\n    if (!this.eventHandlers.has(event)) {\n      this.eventHandlers.set(event, []);\n    }\n    this.eventHandlers.get(event).push(handler);\n  }\n\n  /**\n   * Remove event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  off(event, handler) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      const index = handlers.indexOf(handler);\n      if (index > -1) {\n        handlers.splice(index, 1);\n      }\n    }\n  }\n\n  /**\n   * Emit event to handlers\n   * @param {string} event - Event name\n   * @param {*} data - Event data\n   */\n  emit(event, data) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error(`Error in WebSocket event handler for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get connection status\n   * @returns {boolean} - Connection status\n   */\n  isConnectedToServer() {\n    return this.isConnected;\n  }\n\n  /**\n   * Get connection statistics\n   * @returns {Object} - Connection stats\n   */\n  getStats() {\n    return {\n      isConnected: this.isConnected,\n      reconnectAttempts: this.reconnectAttempts,\n      lastHeartbeat: this.lastHeartbeat,\n      queuedMessages: this.messageQueue.length\n    };\n  }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\nexport default websocketService;", "map": {"version": 3, "names": ["WebSocketService", "constructor", "ws", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "eventHandlers", "Map", "messageQueue", "heartbeatInterval", "lastHeartbeat", "connect", "url", "token", "console", "log", "WebSocket", "onopen", "authenticate", "flushMessageQueue", "emit", "onmessage", "event", "data", "JSON", "parse", "type", "Date", "now", "error", "onclose", "code", "reason", "stopHeartbeat", "scheduleReconnect", "onerror", "disconnect", "close", "send", "stringify", "push", "joinChannel", "serverId", "channelId", "sendChatMessage", "content", "ping", "timestamp", "handleDispatch", "t", "eventType", "d", "eventData", "handleHeartbeat", "op", "handleHello", "heartbeat_interval", "handleHeartbeatAck", "handleInvalidSession", "startHeartbeat", "heartbeatTimer", "setInterval", "clearInterval", "delay", "Math", "pow", "setTimeout", "length", "message", "shift", "on", "handler", "has", "set", "get", "off", "handlers", "index", "indexOf", "splice", "for<PERSON>ach", "isConnectedToServer", "getStats", "queuedMessages", "websocketService"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/services/websocket.js"], "sourcesContent": ["/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n    constructor() {\n        this.ws = null;\n        this.isConnected = false;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectDelay = 1000;\n        this.eventHandlers = new Map();\n        this.messageQueue = [];\n        this.heartbeatInterval = null;\n        this.lastHeartbeat = null;\n    }\n\n    /**\n     * Connect to WebSocket server\n     * @param {string} url - WebSocket URL\n     * @param {string} token - Authentication token\n     */\n    connect(url = 'ws://localhost:3003/chat', token = null) {\n        try {\n            console.log('🔌 Connecting to WebSocket:', url);\n            \n            this.ws = new WebSocket(url);\n            \n            this.ws.onopen = () => {\n                console.log('✅ WebSocket connected');\n                this.isConnected = true;\n                this.reconnectAttempts = 0;\n\n                // Send authentication if token provided\n                if (token) {\n                    this.authenticate(token);\n                }\n\n                // Send queued messages\n                this.flushMessageQueue();\n\n                this.emit('connected');\n            };\n\n            this.ws.onmessage = (event) => {\n                try {\n                    const data = JSON.parse(event.data);\n                    console.log('📨 WebSocket message:', data);\n\n                    // Handle different message types for chat server\n                    switch (data.type) {\n                        case 'hello':\n                            this.emit('connected');\n                            break;\n                        case 'authenticated':\n                            this.emit('authenticated', data.data);\n                            break;\n                        case 'auth_error':\n                            this.emit('authError', data.data);\n                            break;\n                        case 'channel_joined':\n                            this.emit('channelJoined', data.data);\n                            break;\n                        case 'message_create':\n                            this.emit('messageCreate', data.data);\n                            break;\n                        case 'pong':\n                            this.lastHeartbeat = Date.now();\n                            break;\n                        case 'error':\n                            this.emit('error', data.data);\n                            break;\n                        default:\n                            console.log('Unknown WebSocket message type:', data.type);\n                    }\n                } catch (error) {\n                    console.error('Error parsing WebSocket message:', error);\n                }\n            };\n\n            this.ws.onclose = (event) => {\n                console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n                this.isConnected = false;\n                this.stopHeartbeat();\n                \n                this.emit('disconnected', { code: event.code, reason: event.reason });\n                \n                // Attempt to reconnect\n                if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                    this.scheduleReconnect();\n                }\n            };\n\n            this.ws.onerror = (error) => {\n                console.error('❌ WebSocket error:', error);\n                this.emit('error', error);\n            };\n\n        } catch (error) {\n            console.error('Failed to connect to WebSocket:', error);\n            this.emit('error', error);\n        }\n    }\n\n    /**\n     * Disconnect from WebSocket\n     */\n    disconnect() {\n        if (this.ws) {\n            this.ws.close(1000, 'Client disconnect');\n            this.ws = null;\n        }\n        this.isConnected = false;\n        this.stopHeartbeat();\n    }\n\n    /**\n     * Send authentication message\n     * @param {string} token - Authentication token\n     */\n    authenticate(token) {\n        this.send({\n            type: 'authenticate',\n            data: {\n                token: token\n            }\n        });\n    }\n\n    /**\n     * Send message to WebSocket\n     * @param {Object} data - Message data\n     */\n    send(data) {\n        if (this.isConnected && this.ws) {\n            try {\n                this.ws.send(JSON.stringify(data));\n            } catch (error) {\n                console.error('Error sending WebSocket message:', error);\n            }\n        } else {\n            // Queue message for later\n            this.messageQueue.push(data);\n        }\n    }\n\n    /**\n     * Join a channel\n     * @param {string} serverId - Server ID\n     * @param {string} channelId - Channel ID\n     */\n    joinChannel(serverId, channelId) {\n        this.send({\n            type: 'join_channel',\n            data: {\n                serverId,\n                channelId\n            }\n        });\n    }\n\n    /**\n     * Send a chat message\n     * @param {string} serverId - Server ID\n     * @param {string} channelId - Channel ID\n     * @param {string} content - Message content\n     */\n    sendChatMessage(serverId, channelId, content) {\n        this.send({\n            type: 'send_message',\n            data: {\n                serverId,\n                channelId,\n                content\n            }\n        });\n    }\n\n    /**\n     * Send ping to keep connection alive\n     */\n    ping() {\n        this.send({\n            type: 'ping',\n            data: {\n                timestamp: Date.now()\n            }\n        });\n    }\n\n    /**\n     * Handle dispatch messages (events)\n     * @param {Object} data - Message data\n     */\n    handleDispatch(data) {\n        const { t: eventType, d: eventData } = data;\n        \n        switch (eventType) {\n            case 'MESSAGE_CREATE':\n                this.emit('messageCreate', eventData);\n                break;\n            case 'MESSAGE_UPDATE':\n                this.emit('messageUpdate', eventData);\n                break;\n            case 'MESSAGE_DELETE':\n                this.emit('messageDelete', eventData);\n                break;\n            case 'CHANNEL_CREATE':\n                this.emit('channelCreate', eventData);\n                break;\n            case 'CHANNEL_UPDATE':\n                this.emit('channelUpdate', eventData);\n                break;\n            case 'CHANNEL_DELETE':\n                this.emit('channelDelete', eventData);\n                break;\n            case 'GUILD_CREATE':\n                this.emit('guildCreate', eventData);\n                break;\n            case 'GUILD_UPDATE':\n                this.emit('guildUpdate', eventData);\n                break;\n            case 'GUILD_DELETE':\n                this.emit('guildDelete', eventData);\n                break;\n            case 'READY':\n                this.emit('ready', eventData);\n                break;\n            default:\n                this.emit('event', { type: eventType, data: eventData });\n        }\n    }\n\n    /**\n     * Handle heartbeat\n     * @param {Object} data - Message data\n     */\n    handleHeartbeat(data) {\n        // Send heartbeat response\n        this.send({\n            op: 1,\n            d: this.lastHeartbeat\n        });\n    }\n\n    /**\n     * Handle hello message\n     * @param {Object} data - Message data\n     */\n    handleHello(data) {\n        const { heartbeat_interval } = data.d;\n        this.heartbeatInterval = heartbeat_interval;\n        console.log('💓 Heartbeat interval:', heartbeat_interval);\n    }\n\n    /**\n     * Handle heartbeat acknowledgment\n     * @param {Object} data - Message data\n     */\n    handleHeartbeatAck(data) {\n        this.lastHeartbeat = Date.now();\n    }\n\n    /**\n     * Handle invalid session\n     * @param {Object} data - Message data\n     */\n    handleInvalidSession(data) {\n        console.error('Invalid WebSocket session');\n        this.emit('invalidSession', data);\n    }\n\n    /**\n     * Start heartbeat timer\n     */\n    startHeartbeat() {\n        if (this.heartbeatInterval) {\n            this.heartbeatTimer = setInterval(() => {\n                this.send({\n                    op: 1,\n                    d: this.lastHeartbeat\n                });\n            }, this.heartbeatInterval);\n        }\n    }\n\n    /**\n     * Stop heartbeat timer\n     */\n    stopHeartbeat() {\n        if (this.heartbeatTimer) {\n            clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = null;\n        }\n    }\n\n    /**\n     * Schedule reconnection attempt\n     */\n    scheduleReconnect() {\n        this.reconnectAttempts++;\n        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n        \n        console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        \n        setTimeout(() => {\n            if (!this.isConnected) {\n                this.connect();\n            }\n        }, delay);\n    }\n\n    /**\n     * Flush queued messages\n     */\n    flushMessageQueue() {\n        while (this.messageQueue.length > 0) {\n            const message = this.messageQueue.shift();\n            this.send(message);\n        }\n    }\n\n    /**\n     * Register event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    on(event, handler) {\n        if (!this.eventHandlers.has(event)) {\n            this.eventHandlers.set(event, []);\n        }\n        this.eventHandlers.get(event).push(handler);\n    }\n\n    /**\n     * Remove event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    off(event, handler) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            const index = handlers.indexOf(handler);\n            if (index > -1) {\n                handlers.splice(index, 1);\n            }\n        }\n    }\n\n    /**\n     * Emit event to handlers\n     * @param {string} event - Event name\n     * @param {*} data - Event data\n     */\n    emit(event, data) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            handlers.forEach(handler => {\n                try {\n                    handler(data);\n                } catch (error) {\n                    console.error(`Error in WebSocket event handler for ${event}:`, error);\n                }\n            });\n        }\n    }\n\n    /**\n     * Get connection status\n     * @returns {boolean} - Connection status\n     */\n    isConnectedToServer() {\n        return this.isConnected;\n    }\n\n    /**\n     * Get connection statistics\n     * @returns {Object} - Connection stats\n     */\n    getStats() {\n        return {\n            isConnected: this.isConnected,\n            reconnectAttempts: this.reconnectAttempts,\n            lastHeartbeat: this.lastHeartbeat,\n            queuedMessages: this.messageQueue.length\n        };\n    }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\n\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;;EAEA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAACC,GAAG,GAAG,0BAA0B,EAAEC,KAAK,GAAG,IAAI,EAAE;IACpD,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,GAAG,CAAC;MAE/C,IAAI,CAACX,EAAE,GAAG,IAAIe,SAAS,CAACJ,GAAG,CAAC;MAE5B,IAAI,CAACX,EAAE,CAACgB,MAAM,GAAG,MAAM;QACnBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC,IAAI,CAACb,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;;QAE1B;QACA,IAAIU,KAAK,EAAE;UACP,IAAI,CAACK,YAAY,CAACL,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,CAACM,iBAAiB,CAAC,CAAC;QAExB,IAAI,CAACC,IAAI,CAAC,WAAW,CAAC;MAC1B,CAAC;MAED,IAAI,CAACnB,EAAE,CAACoB,SAAS,GAAIC,KAAK,IAAK;QAC3B,IAAI;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCT,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEQ,IAAI,CAAC;;UAE1C;UACA,QAAQA,IAAI,CAACG,IAAI;YACb,KAAK,OAAO;cACR,IAAI,CAACN,IAAI,CAAC,WAAW,CAAC;cACtB;YACJ,KAAK,eAAe;cAChB,IAAI,CAACA,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,YAAY;cACb,IAAI,CAACH,IAAI,CAAC,WAAW,EAAEG,IAAI,CAACA,IAAI,CAAC;cACjC;YACJ,KAAK,gBAAgB;cACjB,IAAI,CAACH,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,gBAAgB;cACjB,IAAI,CAACH,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,MAAM;cACP,IAAI,CAACb,aAAa,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC;cAC/B;YACJ,KAAK,OAAO;cACR,IAAI,CAACR,IAAI,CAAC,OAAO,EAAEG,IAAI,CAACA,IAAI,CAAC;cAC7B;YACJ;cACIT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,IAAI,CAACG,IAAI,CAAC;UACjE;QACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;UACZf,OAAO,CAACe,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D;MACJ,CAAC;MAED,IAAI,CAAC5B,EAAE,CAAC6B,OAAO,GAAIR,KAAK,IAAK;QACzBR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,KAAK,CAACS,IAAI,EAAET,KAAK,CAACU,MAAM,CAAC;QACnE,IAAI,CAAC9B,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC+B,aAAa,CAAC,CAAC;QAEpB,IAAI,CAACb,IAAI,CAAC,cAAc,EAAE;UAAEW,IAAI,EAAET,KAAK,CAACS,IAAI;UAAEC,MAAM,EAAEV,KAAK,CAACU;QAAO,CAAC,CAAC;;QAErE;QACA,IAAI,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;UACpD,IAAI,CAAC8B,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MAED,IAAI,CAACjC,EAAE,CAACkC,OAAO,GAAIN,KAAK,IAAK;QACzBf,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,IAAI,CAACT,IAAI,CAAC,OAAO,EAAES,KAAK,CAAC;MAC7B,CAAC;IAEL,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZf,OAAO,CAACe,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAACT,IAAI,CAAC,OAAO,EAAES,KAAK,CAAC;IAC7B;EACJ;;EAEA;AACJ;AACA;EACIO,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACnC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACoC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MACxC,IAAI,CAACpC,EAAE,GAAG,IAAI;IAClB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC+B,aAAa,CAAC,CAAC;EACxB;;EAEA;AACJ;AACA;AACA;EACIf,YAAYA,CAACL,KAAK,EAAE;IAChB,IAAI,CAACyB,IAAI,CAAC;MACNZ,IAAI,EAAE,cAAc;MACpBH,IAAI,EAAE;QACFV,KAAK,EAAEA;MACX;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIyB,IAAIA,CAACf,IAAI,EAAE;IACP,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACD,EAAE,EAAE;MAC7B,IAAI;QACA,IAAI,CAACA,EAAE,CAACqC,IAAI,CAACd,IAAI,CAACe,SAAS,CAAChB,IAAI,CAAC,CAAC;MACtC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACZf,OAAO,CAACe,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC5D;IACJ,CAAC,MAAM;MACH;MACA,IAAI,CAACrB,YAAY,CAACgC,IAAI,CAACjB,IAAI,CAAC;IAChC;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIkB,WAAWA,CAACC,QAAQ,EAAEC,SAAS,EAAE;IAC7B,IAAI,CAACL,IAAI,CAAC;MACNZ,IAAI,EAAE,cAAc;MACpBH,IAAI,EAAE;QACFmB,QAAQ;QACRC;MACJ;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACF,QAAQ,EAAEC,SAAS,EAAEE,OAAO,EAAE;IAC1C,IAAI,CAACP,IAAI,CAAC;MACNZ,IAAI,EAAE,cAAc;MACpBH,IAAI,EAAE;QACFmB,QAAQ;QACRC,SAAS;QACTE;MACJ;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;EACIC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACR,IAAI,CAAC;MACNZ,IAAI,EAAE,MAAM;MACZH,IAAI,EAAE;QACFwB,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIoB,cAAcA,CAACzB,IAAI,EAAE;IACjB,MAAM;MAAE0B,CAAC,EAAEC,SAAS;MAAEC,CAAC,EAAEC;IAAU,CAAC,GAAG7B,IAAI;IAE3C,QAAQ2B,SAAS;MACb,KAAK,gBAAgB;QACjB,IAAI,CAAC9B,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAChC,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAChC,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAChC,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAChC,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAAChC,IAAI,CAAC,eAAe,EAAEgC,SAAS,CAAC;QACrC;MACJ,KAAK,cAAc;QACf,IAAI,CAAChC,IAAI,CAAC,aAAa,EAAEgC,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAAChC,IAAI,CAAC,aAAa,EAAEgC,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAAChC,IAAI,CAAC,aAAa,EAAEgC,SAAS,CAAC;QACnC;MACJ,KAAK,OAAO;QACR,IAAI,CAAChC,IAAI,CAAC,OAAO,EAAEgC,SAAS,CAAC;QAC7B;MACJ;QACI,IAAI,CAAChC,IAAI,CAAC,OAAO,EAAE;UAAEM,IAAI,EAAEwB,SAAS;UAAE3B,IAAI,EAAE6B;QAAU,CAAC,CAAC;IAChE;EACJ;;EAEA;AACJ;AACA;AACA;EACIC,eAAeA,CAAC9B,IAAI,EAAE;IAClB;IACA,IAAI,CAACe,IAAI,CAAC;MACNgB,EAAE,EAAE,CAAC;MACLH,CAAC,EAAE,IAAI,CAACzC;IACZ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACI6C,WAAWA,CAAChC,IAAI,EAAE;IACd,MAAM;MAAEiC;IAAmB,CAAC,GAAGjC,IAAI,CAAC4B,CAAC;IACrC,IAAI,CAAC1C,iBAAiB,GAAG+C,kBAAkB;IAC3C1C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEyC,kBAAkB,CAAC;EAC7D;;EAEA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAAClC,IAAI,EAAE;IACrB,IAAI,CAACb,aAAa,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC;EACnC;;EAEA;AACJ;AACA;AACA;EACI8B,oBAAoBA,CAACnC,IAAI,EAAE;IACvBT,OAAO,CAACe,KAAK,CAAC,2BAA2B,CAAC;IAC1C,IAAI,CAACT,IAAI,CAAC,gBAAgB,EAAEG,IAAI,CAAC;EACrC;;EAEA;AACJ;AACA;EACIoC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAClD,iBAAiB,EAAE;MACxB,IAAI,CAACmD,cAAc,GAAGC,WAAW,CAAC,MAAM;QACpC,IAAI,CAACvB,IAAI,CAAC;UACNgB,EAAE,EAAE,CAAC;UACLH,CAAC,EAAE,IAAI,CAACzC;QACZ,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAACD,iBAAiB,CAAC;IAC9B;EACJ;;EAEA;AACJ;AACA;EACIwB,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAC2B,cAAc,EAAE;MACrBE,aAAa,CAAC,IAAI,CAACF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;;EAEA;AACJ;AACA;EACI1B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC/B,iBAAiB,EAAE;IACxB,MAAM4D,KAAK,GAAG,IAAI,CAAC1D,cAAc,GAAG2D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9D,iBAAiB,GAAG,CAAC,CAAC;IAE3EW,OAAO,CAACC,GAAG,CAAC,sBAAsBgD,KAAK,eAAe,IAAI,CAAC5D,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAE7G8D,UAAU,CAAC,MAAM;MACb,IAAI,CAAC,IAAI,CAAChE,WAAW,EAAE;QACnB,IAAI,CAACS,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,EAAEoD,KAAK,CAAC;EACb;;EAEA;AACJ;AACA;EACI5C,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACX,YAAY,CAAC2D,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMC,OAAO,GAAG,IAAI,CAAC5D,YAAY,CAAC6D,KAAK,CAAC,CAAC;MACzC,IAAI,CAAC/B,IAAI,CAAC8B,OAAO,CAAC;IACtB;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIE,EAAEA,CAAChD,KAAK,EAAEiD,OAAO,EAAE;IACf,IAAI,CAAC,IAAI,CAACjE,aAAa,CAACkE,GAAG,CAAClD,KAAK,CAAC,EAAE;MAChC,IAAI,CAAChB,aAAa,CAACmE,GAAG,CAACnD,KAAK,EAAE,EAAE,CAAC;IACrC;IACA,IAAI,CAAChB,aAAa,CAACoE,GAAG,CAACpD,KAAK,CAAC,CAACkB,IAAI,CAAC+B,OAAO,CAAC;EAC/C;;EAEA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAACrD,KAAK,EAAEiD,OAAO,EAAE;IAChB,MAAMK,QAAQ,GAAG,IAAI,CAACtE,aAAa,CAACoE,GAAG,CAACpD,KAAK,CAAC;IAC9C,IAAIsD,QAAQ,EAAE;MACV,MAAMC,KAAK,GAAGD,QAAQ,CAACE,OAAO,CAACP,OAAO,CAAC;MACvC,IAAIM,KAAK,GAAG,CAAC,CAAC,EAAE;QACZD,QAAQ,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC7B;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIzD,IAAIA,CAACE,KAAK,EAAEC,IAAI,EAAE;IACd,MAAMqD,QAAQ,GAAG,IAAI,CAACtE,aAAa,CAACoE,GAAG,CAACpD,KAAK,CAAC;IAC9C,IAAIsD,QAAQ,EAAE;MACVA,QAAQ,CAACI,OAAO,CAACT,OAAO,IAAI;QACxB,IAAI;UACAA,OAAO,CAAChD,IAAI,CAAC;QACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;UACZf,OAAO,CAACe,KAAK,CAAC,wCAAwCP,KAAK,GAAG,EAAEO,KAAK,CAAC;QAC1E;MACJ,CAAC,CAAC;IACN;EACJ;;EAEA;AACJ;AACA;AACA;EACIoD,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC/E,WAAW;EAC3B;;EAEA;AACJ;AACA;AACA;EACIgF,QAAQA,CAAA,EAAG;IACP,OAAO;MACHhF,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCO,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCyE,cAAc,EAAE,IAAI,CAAC3E,YAAY,CAAC2D;IACtC,CAAC;EACL;AACJ;;AAEA;AACA,MAAMiB,gBAAgB,GAAG,IAAIrF,gBAAgB,CAAC,CAAC;AAE/C,eAAeqF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}