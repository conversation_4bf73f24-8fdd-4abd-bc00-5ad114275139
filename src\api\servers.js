const express = require('express');
const router = express.Router();
const database = require('../database/connection');
const { authenticateUser } = require('../auth/middleware');
const CryptoUtils = require('../utils/crypto');
// const { GATEWAY_EVENTS } = require('../gateway/events');

/**
 * Create a new server
 * POST /api/servers
 */
router.post('/', authenticateUser, async (req, res) => {
    try {
        const { name, description } = req.body;

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                error: 'Server name is required'
            });
        }

        if (name.length > 100) {
            return res.status(400).json({
                error: 'Server name cannot exceed 100 characters'
            });
        }

        if (description && description.length > 500) {
            return res.status(400).json({
                error: 'Description cannot exceed 500 characters'
            });
        }

        const serverId = CryptoUtils.generateUUID();

        // Create server
        await database.run(
            'INSERT INTO servers (id, name, description, owner_id) VALUES (?, ?, ?, ?)',
            [serverId, name.trim(), description || null, req.user.id]
        );

        // Create default general channel
        const channelId = CryptoUtils.generateUUID();
        await database.run(
            'INSERT INTO channels (id, server_id, name, type, position) VALUES (?, ?, ?, ?, ?)',
            [channelId, serverId, 'general', 'text', 0]
        );

        // Get the created server with channels
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ?',
            [serverId]
        );

        const channels = await database.all(
            'SELECT * FROM channels WHERE server_id = ? ORDER BY position',
            [serverId]
        );

        server.channels = channels;

        res.status(201).json({
            message: 'Server created successfully',
            server: server
        });

    } catch (error) {
        console.error('Error creating server:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Get user's servers
 * GET /api/servers
 */
router.get('/', authenticateUser, async (req, res) => {
    try {
        const servers = await database.all(
            'SELECT * FROM servers WHERE owner_id = ? ORDER BY created_at DESC',
            [req.user.id]
        );

        // Get channels for each server
        for (const server of servers) {
            const channels = await database.all(
                'SELECT * FROM channels WHERE server_id = ? ORDER BY position',
                [server.id]
            );
            server.channels = channels;

            // Get bot count
            const botCount = await database.get(
                'SELECT COUNT(*) as count FROM bot_instances WHERE server_id = ?',
                [server.id]
            );
            server.bot_count = botCount.count;
        }

        res.json({
            servers: servers
        });

    } catch (error) {
        console.error('Error getting servers:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Get specific server
 * GET /api/servers/:id
 */
router.get('/:id', authenticateUser, async (req, res) => {
    try {
        const { id } = req.params;

        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [id, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        // Get channels
        const channels = await database.all(
            'SELECT * FROM channels WHERE server_id = ? ORDER BY position',
            [id]
        );
        server.channels = channels;

        // Get deployed bots
        const bots = await database.all(
            `SELECT bi.*, a.name as bot_name
             FROM bot_instances bi
             JOIN applications a ON bi.application_id = a.id
             WHERE bi.server_id = ?
             ORDER BY bi.created_at DESC`,
            [id]
        );
        server.bots = bots;

        res.json({
            server: server
        });

    } catch (error) {
        console.error('Error getting server:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Update server
 * PUT /api/servers/:id
 */
router.put('/:id', authenticateUser, async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description } = req.body;

        // Verify ownership
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [id, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        const updates = {};
        if (name !== undefined) {
            if (!name || name.trim().length === 0) {
                return res.status(400).json({
                    error: 'Server name is required'
                });
            }
            if (name.length > 100) {
                return res.status(400).json({
                    error: 'Server name cannot exceed 100 characters'
                });
            }
            updates.name = name.trim();
        }

        if (description !== undefined) {
            if (description && description.length > 500) {
                return res.status(400).json({
                    error: 'Description cannot exceed 500 characters'
                });
            }
            updates.description = description;
        }

        if (Object.keys(updates).length === 0) {
            return res.status(400).json({
                error: 'No valid fields to update'
            });
        }

        // Update server
        const updateFields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
        const updateValues = Object.values(updates);
        updateValues.push(id);

        await database.run(
            `UPDATE servers SET ${updateFields}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
            updateValues
        );

        // Get updated server
        const updatedServer = await database.get(
            'SELECT * FROM servers WHERE id = ?',
            [id]
        );

        // Broadcast server update to bots
        // const gateway = req.app.get('gateway');
        // if (gateway) {
        //     await gateway.sendToBotsInServer(id, GATEWAY_EVENTS.SERVER_UPDATE, updatedServer);
        // }

        res.json({
            message: 'Server updated successfully',
            server: updatedServer
        });

    } catch (error) {
        console.error('Error updating server:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Delete server
 * DELETE /api/servers/:id
 */
router.delete('/:id', authenticateUser, async (req, res) => {
    try {
        const { id } = req.params;

        // Verify ownership
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [id, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        // Broadcast server deletion to bots before deleting
        // const gateway = req.app.get('gateway');
        // if (gateway) {
        //     await gateway.sendToBotsInServer(id, GATEWAY_EVENTS.SERVER_DELETE, { server_id: id });
        // }

        // Delete server (cascades to channels, bot_instances, etc.)
        await database.run(
            'DELETE FROM servers WHERE id = ?',
            [id]
        );

        res.json({
            message: 'Server deleted successfully'
        });

    } catch (error) {
        console.error('Error deleting server:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Create a channel in a server
 * POST /api/servers/:id/channels
 */
router.post('/:id/channels', authenticateUser, async (req, res) => {
    try {
        const { id: serverId } = req.params;
        const { name, type = 'text', position } = req.body;

        // Verify server ownership
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        if (!name || name.trim().length === 0) {
            return res.status(400).json({
                error: 'Channel name is required'
            });
        }

        if (name.length > 100) {
            return res.status(400).json({
                error: 'Channel name cannot exceed 100 characters'
            });
        }

        if (!['text', 'voice'].includes(type)) {
            return res.status(400).json({
                error: 'Channel type must be text or voice'
            });
        }

        // Get next position if not specified
        let channelPosition = position;
        if (channelPosition === undefined) {
            const maxPosition = await database.get(
                'SELECT MAX(position) as max_pos FROM channels WHERE server_id = ?',
                [serverId]
            );
            channelPosition = (maxPosition.max_pos || -1) + 1;
        }

        const channelId = CryptoUtils.generateUUID();

        await database.run(
            'INSERT INTO channels (id, server_id, name, type, position) VALUES (?, ?, ?, ?, ?)',
            [channelId, serverId, name.trim(), type, channelPosition]
        );

        const channel = await database.get(
            'SELECT * FROM channels WHERE id = ?',
            [channelId]
        );

        // Broadcast channel creation to bots
        // const gateway = req.app.get('gateway');
        // if (gateway) {
        //     await gateway.sendToBotsInServer(serverId, GATEWAY_EVENTS.CHANNEL_CREATE, channel);
        // }

        res.status(201).json({
            message: 'Channel created successfully',
            channel: channel
        });

    } catch (error) {
        console.error('Error creating channel:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Get server channels
 * GET /api/servers/:id/channels
 */
router.get('/:id/channels', authenticateUser, async (req, res) => {
    try {
        const { id: serverId } = req.params;

        // Verify server ownership
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        const channels = await database.all(
            'SELECT * FROM channels WHERE server_id = ? ORDER BY position',
            [serverId]
        );

        res.json({
            channels: channels
        });

    } catch (error) {
        console.error('Error getting channels:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Get server bots
 * GET /api/servers/:id/bots
 */
router.get('/:id/bots', authenticateUser, async (req, res) => {
    try {
        const { id: serverId } = req.params;

        // Verify server ownership
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, req.user.id]
        );

        if (!server) {
            return res.status(404).json({
                error: 'Server not found'
            });
        }

        const bots = await database.all(
            `SELECT bi.*, a.name as bot_name, a.description as bot_description, u.username as owner_username
             FROM bot_instances bi
             JOIN applications a ON bi.application_id = a.id
             JOIN users u ON a.owner_id = u.id
             WHERE bi.server_id = ?
             ORDER BY bi.created_at DESC`,
            [serverId]
        );

        res.json({
            bots: bots
        });

    } catch (error) {
        console.error('Error getting server bots:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

/**
 * Get messages for a channel
 * GET /api/servers/:serverId/channels/:channelId/messages
 */
router.get('/:serverId/channels/:channelId/messages', authenticateUser, async (req, res) => {
    try {
        const { serverId, channelId } = req.params;
        const { limit = 50, before } = req.query;
        const userId = req.user.id;

        // Check if user has access to this server
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, userId]
        );

        if (!server) {
            return res.status(403).json({ error: 'Access denied to this server' });
        }

        let query = `
            SELECT m.*, u.username as author_username, u.id as author_id,
                   CASE WHEN a.id IS NOT NULL THEN 1 ELSE 0 END as author_bot
            FROM messages m
            JOIN users u ON m.author_id = u.id
            LEFT JOIN applications a ON u.id = a.id
            WHERE m.channel_id = ?
        `;

        const params = [channelId];

        if (before) {
            query += ' AND m.created_at < ?';
            params.push(before);
        }

        query += ' ORDER BY m.created_at DESC LIMIT ?';
        params.push(parseInt(limit));

        const messages = await database.all(query, params);

        // Format messages
        const formattedMessages = messages.reverse().map(msg => ({
            id: msg.id,
            content: msg.content,
            channel_id: msg.channel_id,
            author: {
                id: msg.author_id,
                username: msg.author_username,
                bot: Boolean(msg.author_bot)
            },
            created_at: msg.created_at,
            updated_at: msg.updated_at
        }));

        res.json({ messages: formattedMessages });
    } catch (error) {
        console.error('Error fetching messages:', error);
        res.status(500).json({ error: 'Failed to fetch messages' });
    }
});

/**
 * Send a message to a channel
 * POST /api/servers/:serverId/channels/:channelId/messages
 */
router.post('/:serverId/channels/:channelId/messages', authenticateUser, async (req, res) => {
    try {
        const { serverId, channelId } = req.params;
        const { content } = req.body;
        const userId = req.user.id;

        if (!content || content.trim().length === 0) {
            return res.status(400).json({ error: 'Message content is required' });
        }

        // Check access
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, userId]
        );

        if (!server) {
            return res.status(403).json({ error: 'Access denied to this server' });
        }

        const messageId = CryptoUtils.generateUUID();
        const now = new Date().toISOString();

        await database.run(`
            INSERT INTO messages (id, channel_id, author_id, content, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?)
        `, [messageId, channelId, userId, content.trim(), now, now]);

        // Get the created message with author info
        const message = await database.get(`
            SELECT m.*, u.username as author_username, u.id as author_id,
                   CASE WHEN a.id IS NOT NULL THEN 1 ELSE 0 END as author_bot
            FROM messages m
            JOIN users u ON m.author_id = u.id
            LEFT JOIN applications a ON u.id = a.id
            WHERE m.id = ?
        `, [messageId]);

        const formattedMessage = {
            id: message.id,
            content: message.content,
            channel_id: message.channel_id,
            author: {
                id: message.author_id,
                username: message.author_username,
                bot: Boolean(message.author_bot)
            },
            created_at: message.created_at,
            updated_at: message.updated_at
        };

        // Broadcast message via WebSocket
        // const gateway = req.app.get('gateway');
        // if (gateway) {
        //     await gateway.sendToBotsInServer(serverId, GATEWAY_EVENTS.MESSAGE_CREATE, formattedMessage);
        // }

        res.status(201).json({ message: formattedMessage });
    } catch (error) {
        console.error('Error sending message:', error);
        res.status(500).json({ error: 'Failed to send message' });
    }
});

/**
 * Get bots in a channel
 * GET /api/servers/:serverId/channels/:channelId/bots
 */
router.get('/:serverId/channels/:channelId/bots', authenticateUser, async (req, res) => {
    try {
        const { serverId, channelId } = req.params;
        const userId = req.user.id;

        // Check access
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, userId]
        );

        if (!server) {
            return res.status(403).json({ error: 'Access denied to this server' });
        }

        // For now, return all bots in the server (later we can implement channel-specific bots)
        const bots = await database.all(`
            SELECT a.*, bi.status, bi.last_active
            FROM bot_instances bi
            JOIN applications a ON bi.application_id = a.id
            WHERE bi.server_id = ? AND a.status = 'active'
            ORDER BY bi.created_at DESC
        `, [serverId]);

        res.json({ bots });
    } catch (error) {
        console.error('Error fetching channel bots:', error);
        res.status(500).json({ error: 'Failed to fetch channel bots' });
    }
});

/**
 * Invite a bot to a channel
 * POST /api/servers/:serverId/channels/:channelId/bots
 */
router.post('/:serverId/channels/:channelId/bots', authenticateUser, async (req, res) => {
    try {
        const { serverId, channelId } = req.params;
        const { bot_id } = req.body;
        const userId = req.user.id;

        if (!bot_id) {
            return res.status(400).json({ error: 'Bot ID is required' });
        }

        // Check if user has permission to invite bots
        const server = await database.get(
            'SELECT * FROM servers WHERE id = ? AND owner_id = ?',
            [serverId, userId]
        );

        if (!server) {
            return res.status(403).json({ error: 'Only server owners can invite bots' });
        }

        // Check if bot exists and is active
        const bot = await database.get(
            'SELECT * FROM applications WHERE id = ? AND status = ?',
            [bot_id, 'active']
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found or inactive' });
        }

        // Check if bot is already in server
        const existing = await database.get(
            'SELECT * FROM bot_instances WHERE server_id = ? AND application_id = ?',
            [serverId, bot_id]
        );

        if (existing) {
            return res.status(409).json({ error: 'Bot is already in this server' });
        }

        const instanceId = CryptoUtils.generateUUID();
        const now = new Date().toISOString();

        await database.run(`
            INSERT INTO bot_instances (id, application_id, server_id, status, created_at)
            VALUES (?, ?, ?, 'online', ?)
        `, [instanceId, bot_id, serverId, now]);

        res.status(201).json({ message: 'Bot invited successfully' });
    } catch (error) {
        console.error('Error inviting bot:', error);
        res.status(500).json({ error: 'Failed to invite bot' });
    }
});

module.exports = router;
