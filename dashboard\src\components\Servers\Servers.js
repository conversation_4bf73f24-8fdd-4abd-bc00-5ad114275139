import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Add as AddIcon,
  Storage as ServerIcon,
} from '@mui/icons-material';
import { serversAPI, handleApiError } from '../../services/api';
import toast from 'react-hot-toast';

const Servers = () => {
  const navigate = useNavigate();
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newServerName, setNewServerName] = useState('');
  const [newServerDescription, setNewServerDescription] = useState('');

  useEffect(() => {
    loadServers();
  }, []);

  const loadServers = async () => {
    try {
      setLoading(true);
      const response = await serversAPI.getAll();
      setServers(response.data.servers);
    } catch (error) {
      handleApiError(error, 'Failed to load servers');
    } finally {
      setLoading(false);
    }
  };

  const createServer = async () => {
    if (!newServerName.trim()) {
      toast.error('Tên server không được để trống');
      return;
    }

    try {
      console.log('Creating server:', { name: newServerName, description: newServerDescription });

      const response = await fetch('/api/servers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          name: newServerName.trim(),
          description: newServerDescription.trim() || `${newServerName.trim()} server`
        })
      });

      console.log('Response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('Server created:', data);
        toast.success('Tạo server thành công! Chuyển đến Chat...');
        setShowCreateDialog(false);
        setNewServerName('');
        setNewServerDescription('');
        loadServers(); // Reload servers list

        // Trigger event for other components to refresh
        window.dispatchEvent(new CustomEvent('serverCreated', { detail: data }));

        // Auto navigate to chat after 1 second
        setTimeout(() => {
          navigate('/chat');
        }, 1000);
      } else {
        const errorData = await response.json();
        console.error('Error creating server:', errorData);
        toast.error(errorData.error || 'Không thể tạo server');
      }
    } catch (error) {
      console.error('Error creating server:', error);
      toast.error('Lỗi kết nối khi tạo server');
    }
  };

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Servers
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" fontWeight="bold">
          Servers
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            console.log('Create Server button clicked');
            setShowCreateDialog(true);
          }}
          sx={{
            background: 'linear-gradient(45deg, #5865f2 30%, #57f287 90%)',
          }}
        >
          Create Server
        </Button>
      </Box>

      {servers.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 80,
                height: 80,
                mx: 'auto',
                mb: 2,
              }}
            >
              <ServerIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography variant="h5" gutterBottom>
              No Servers Yet
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              Create your first server to deploy bots.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              size="large"
              onClick={() => {
                console.log('Create Your First Server button clicked');
                setShowCreateDialog(true);
              }}
            >
              Create Your First Server
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {servers.map((server) => (
            <Grid item xs={12} sm={6} md={4} key={server.id}>
              <Card className="hover-card" onClick={() => navigate(`/servers/${server.id}`)}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {server.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {server.description || 'No description'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" mt={2} display="block">
                    Channels: {server.channels?.length || 0}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create Server Dialog */}
      <Dialog
        open={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Tạo Server Mới</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Tên Server"
            fullWidth
            variant="outlined"
            value={newServerName}
            onChange={(e) => setNewServerName(e.target.value)}
            placeholder="Server của tôi"
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Mô tả (Tùy chọn)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newServerDescription}
            onChange={(e) => setNewServerDescription(e.target.value)}
            placeholder="Mô tả về server của bạn..."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowCreateDialog(false)}>
            Hủy
          </Button>
          <Button
            onClick={() => {
              console.log('Create button clicked in dialog');
              createServer();
            }}
            variant="contained"
            disabled={!newServerName.trim()}
          >
            Tạo Server
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Servers;
