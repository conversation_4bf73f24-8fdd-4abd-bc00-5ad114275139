{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\bot_fake_discord\\\\dashboard\\\\src\\\\components\\\\Chat\\\\ChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, TextField, IconButton, Avatar, List, ListItem, ListItemText, ListItemAvatar, Chip, Button, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { Send as SendIcon, Add as AddIcon, Settings as SettingsIcon, SmartToy as BotIcon, Person as PersonIcon, MoreVert as MoreIcon } from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { useAuth } from '../../contexts/AuthContext';\nimport websocketService from '../../services/websocket';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInterface = ({\n  serverId,\n  channelId,\n  serverName,\n  channelName,\n  onBotInvite,\n  onCreateBot,\n  onShowBots\n}) => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isConnected, setIsConnected] = useState(false);\n  // const [onlineUsers, setOnlineUsers] = useState([]);\n  const [bots, setBots] = useState([]);\n  const [showBotMenu, setShowBotMenu] = useState(false);\n  const [botMenuAnchor, setBotMenuAnchor] = useState(null);\n  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);\n  const [availableBots, setAvailableBots] = useState([]);\n  const messagesEndRef = useRef(null);\n  const wsRef = useRef(null);\n  useEffect(() => {\n    connectToChat();\n    loadMessages();\n    loadChannelBots();\n    return () => {\n      if (wsRef.current) {\n        wsRef.current.disconnect();\n      }\n    };\n  }, [serverId, channelId]);\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const connectToChat = () => {\n    if (wsRef.current) {\n      wsRef.current.disconnect();\n    }\n    wsRef.current = websocketService;\n    wsRef.current.on('connected', () => {\n      console.log('WebSocket connected, authenticating...');\n    });\n    wsRef.current.on('authenticated', userData => {\n      console.log('Authenticated as:', userData);\n      setIsConnected(true);\n      toast.success('Connected to chat');\n\n      // Join the current channel\n      if (serverId && channelId) {\n        wsRef.current.joinChannel(serverId, channelId);\n      }\n    });\n    wsRef.current.on('authError', error => {\n      console.error('Authentication error:', error);\n      setIsConnected(false);\n      toast.error('Authentication failed');\n    });\n    wsRef.current.on('channelJoined', data => {\n      console.log('Joined channel:', data);\n    });\n    wsRef.current.on('disconnected', () => {\n      setIsConnected(false);\n      toast.error('Disconnected from chat');\n    });\n    wsRef.current.on('messageCreate', message => {\n      console.log('New message:', message);\n      setMessages(prev => [...prev, message]);\n    });\n    wsRef.current.on('error', error => {\n      console.error('WebSocket error:', error);\n      toast.error(error.message || 'WebSocket error');\n    });\n\n    // Connect to WebSocket with proper URL and token\n    const token = localStorage.getItem('token');\n    wsRef.current.connect('ws://localhost:3003/chat', token);\n  };\n  const loadMessages = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages || []);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n  const loadChannelBots = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots || []);\n      }\n    } catch (error) {\n      console.error('Error loading channel bots:', error);\n    }\n  };\n  const loadAvailableBots = async () => {\n    try {\n      const response = await fetch('/api/applications', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setAvailableBots(data.applications.filter(app => app.status === 'active'));\n      }\n    } catch (error) {\n      console.error('Error loading available bots:', error);\n    }\n  };\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !isConnected) return;\n    try {\n      // Send via WebSocket for real-time delivery\n      if (wsRef.current && wsRef.current.isConnectedToServer()) {\n        wsRef.current.sendChatMessage(serverId, channelId, newMessage.trim());\n        setNewMessage('');\n      } else {\n        // Fallback to HTTP API\n        const messageData = {\n          content: newMessage,\n          channel_id: channelId,\n          server_id: serverId\n        };\n        const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify(messageData)\n        });\n        if (response.ok) {\n          setNewMessage('');\n        } else {\n          toast.error('Failed to send message');\n        }\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      toast.error('Failed to send message');\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n  const inviteBot = async botId => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({\n          bot_id: botId\n        })\n      });\n      if (response.ok) {\n        toast.success('Bot invited successfully');\n        loadChannelBots();\n        setShowInviteBotDialog(false);\n\n        // Trigger bot invite callback for code editor integration\n        if (onBotInvite) {\n          const botData = availableBots.find(bot => bot.id === botId);\n          onBotInvite(botData);\n        }\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to invite bot');\n      }\n    } catch (error) {\n      console.error('Error inviting bot:', error);\n      toast.error('Failed to invite bot');\n    }\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const getMessageTime = timestamp => {\n    return formatDistanceToNow(new Date(timestamp), {\n      addSuffix: true\n    });\n  };\n  const isBot = author => {\n    return author.bot || bots.some(bot => bot.id === author.id);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        borderBottom: 1,\n        borderColor: 'divider',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        bgcolor: '#36393f',\n        color: 'white'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        alignItems: \"center\",\n        gap: 2,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            color: '#dcddde',\n            fontWeight: 600\n          },\n          children: serverName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#72767d'\n          },\n          children: [\"# \", channelName]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: isConnected ? '🟢 Kết nối' : '🔴 Mất kết nối',\n          color: isConnected ? 'success' : 'error',\n          size: \"small\",\n          sx: {\n            bgcolor: isConnected ? '#43b581' : '#f04747',\n            color: 'white'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 24\n          }, this),\n          onClick: onCreateBot,\n          size: \"small\",\n          variant: \"outlined\",\n          sx: {\n            color: '#dcddde',\n            borderColor: '#72767d'\n          },\n          children: \"Create Bot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 24\n          }, this),\n          onClick: () => {\n            loadAvailableBots();\n            setShowInviteBotDialog(true);\n          },\n          size: \"small\",\n          variant: \"outlined\",\n          sx: {\n            color: '#dcddde',\n            borderColor: '#72767d'\n          },\n          children: \"Invite Bot\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: onShowBots,\n          size: \"small\",\n          variant: \"contained\",\n          sx: {\n            bgcolor: '#5865f2'\n          },\n          children: \"My Bots\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        flex: 1,\n        overflow: 'auto',\n        p: 2,\n        bgcolor: '#36393f',\n        '&::-webkit-scrollbar': {\n          width: '8px'\n        },\n        '&::-webkit-scrollbar-track': {\n          background: '#2f3136'\n        },\n        '&::-webkit-scrollbar-thumb': {\n          background: '#202225',\n          borderRadius: '4px'\n        }\n      },\n      children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        flexDirection: \"column\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        height: \"100%\",\n        sx: {\n          color: '#72767d'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"Ch\\xE0o m\\u1EEBng \\u0111\\u1EBFn v\\u1EDBi #general-chat!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"\\u0110\\xE2y l\\xE0 k\\xEAnh chat chung. H\\xE3y b\\u1EAFt \\u0111\\u1EA7u cu\\u1ED9c tr\\xF2 chuy\\u1EC7n!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(List, {\n        sx: {\n          p: 0\n        },\n        children: messages.map((message, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          alignItems: \"flex-start\",\n          sx: {\n            py: 1,\n            px: 2,\n            '&:hover': {\n              bgcolor: 'rgba(79, 84, 92, 0.16)'\n            },\n            borderRadius: 1,\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                bgcolor: isBot(message.author) ? '#5865f2' : '#43b581',\n                width: 40,\n                height: 40\n              },\n              children: isBot(message.author) ? /*#__PURE__*/_jsxDEV(BotIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 46\n              }, this) : /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 60\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 1,\n              mb: 0.5,\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                fontWeight: \"600\",\n                sx: {\n                  color: isBot(message.author) ? '#5865f2' : '#dcddde',\n                  fontSize: '16px'\n                },\n                children: message.author.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 23\n              }, this), isBot(message.author) && /*#__PURE__*/_jsxDEV(Chip, {\n                label: \"BOT\",\n                size: \"small\",\n                sx: {\n                  bgcolor: '#5865f2',\n                  color: 'white',\n                  fontSize: '10px',\n                  height: '16px',\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#72767d',\n                  fontSize: '12px',\n                  ml: 1\n                },\n                children: getMessageTime(message.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 21\n            }, this),\n            secondary: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                color: '#dcddde',\n                whiteSpace: 'pre-wrap',\n                fontSize: '16px',\n                lineHeight: 1.375\n              },\n              children: message.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 17\n          }, this)]\n        }, message.id || index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        bgcolor: '#36393f',\n        borderTop: '1px solid #40444b'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        gap: 1,\n        sx: {\n          bgcolor: '#40444b',\n          borderRadius: '8px',\n          p: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          maxRows: 4,\n          placeholder: isConnected ? \"Nhắn tin tại #general-chat\" : \"Đang kết nối...\",\n          value: newMessage,\n          onChange: e => setNewMessage(e.target.value),\n          onKeyDown: handleKeyPress,\n          disabled: !isConnected,\n          variant: \"standard\",\n          InputProps: {\n            disableUnderline: true,\n            sx: {\n              color: '#dcddde',\n              fontSize: '16px',\n              '& input::placeholder': {\n                color: '#72767d',\n                opacity: 1\n              },\n              '& textarea::placeholder': {\n                color: '#72767d',\n                opacity: 1\n              }\n            }\n          },\n          sx: {\n            '& .MuiInputBase-root': {\n              bgcolor: 'transparent'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: sendMessage,\n          disabled: !newMessage.trim() || !isConnected,\n          sx: {\n            color: isConnected && newMessage.trim() ? '#5865f2' : '#72767d',\n            '&:hover': {\n              bgcolor: 'rgba(88, 101, 242, 0.1)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this), !isConnected && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          color: '#f04747',\n          mt: 1,\n          display: 'block'\n        },\n        children: \"\\u26A0\\uFE0F M\\u1EA5t k\\u1EBFt n\\u1ED1i v\\u1EDBi server. \\u0110ang th\\u1EED k\\u1EBFt n\\u1ED1i l\\u1EA1i...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 500,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: botMenuAnchor,\n      open: showBotMenu,\n      onClose: () => setShowBotMenu(false),\n      children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => setShowInviteBotDialog(true),\n        children: [/*#__PURE__*/_jsxDEV(AddIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), \"Invite Bot\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n        children: [/*#__PURE__*/_jsxDEV(SettingsIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), \"Channel Settings\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: showInviteBotDialog,\n      onClose: () => setShowInviteBotDialog(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Invite Bot to Channel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 536,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(List, {\n          children: availableBots.map(bot => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemAvatar, {\n              children: /*#__PURE__*/_jsxDEV(Avatar, {\n                children: /*#__PURE__*/_jsxDEV(BotIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: bot.name,\n              secondary: bot.description || 'No description'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              size: \"small\",\n              onClick: () => inviteBot(bot.id),\n              disabled: bots.some(b => b.id === bot.id),\n              children: bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 17\n            }, this)]\n          }, bot.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 537,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setShowInviteBotDialog(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 530,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 256,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"E+5twDjYFuc0MH4oiyNgG65oo40=\", false, function () {\n  return [useAuth];\n});\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "TextField", "IconButton", "Avatar", "List", "ListItem", "ListItemText", "ListItemAvatar", "Chip", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Send", "SendIcon", "Add", "AddIcon", "Settings", "SettingsIcon", "SmartToy", "BotIcon", "Person", "PersonIcon", "<PERSON><PERSON><PERSON>", "MoreIcon", "formatDistanceToNow", "useAuth", "websocketService", "toast", "jsxDEV", "_jsxDEV", "ChatInterface", "serverId", "channelId", "serverName", "channelName", "onBotInvite", "onCreateBot", "onShowBots", "_s", "user", "messages", "setMessages", "newMessage", "setNewMessage", "isConnected", "setIsConnected", "bots", "setBots", "showBotMenu", "setShowBotMenu", "botMenuAnchor", "setBotMenuAnchor", "showInviteBotDialog", "setShowInviteBotDialog", "availableBots", "setAvailableBots", "messagesEndRef", "wsRef", "connectToChat", "loadMessages", "loadChannelBots", "current", "disconnect", "scrollToBottom", "on", "console", "log", "userData", "success", "joinChannel", "error", "data", "message", "prev", "token", "localStorage", "getItem", "connect", "response", "fetch", "headers", "ok", "json", "loadAvailableBots", "applications", "filter", "app", "status", "sendMessage", "trim", "isConnectedToServer", "sendChatMessage", "messageData", "content", "channel_id", "server_id", "method", "body", "JSON", "stringify", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "inviteBot", "botId", "bot_id", "botData", "find", "bot", "id", "errorData", "_messagesEndRef$curre", "scrollIntoView", "behavior", "getMessageTime", "timestamp", "Date", "addSuffix", "isBot", "author", "some", "sx", "height", "display", "flexDirection", "children", "p", "borderBottom", "borderColor", "justifyContent", "alignItems", "bgcolor", "color", "gap", "variant", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "size", "startIcon", "onClick", "flex", "overflow", "width", "background", "borderRadius", "length", "gutterBottom", "map", "index", "py", "px", "mb", "primary", "fontSize", "username", "ml", "created_at", "secondary", "whiteSpace", "lineHeight", "ref", "borderTop", "fullWidth", "multiline", "maxRows", "placeholder", "value", "onChange", "target", "onKeyDown", "disabled", "InputProps", "disableUnderline", "opacity", "mt", "anchorEl", "open", "onClose", "mr", "max<PERSON><PERSON><PERSON>", "name", "description", "b", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/components/Chat/ChatInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  TextField,\n  IconButton,\n  Avatar,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemAvatar,\n  Chip,\n  Button,\n  Menu,\n  MenuItem,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n} from '@mui/material';\nimport {\n  Send as SendIcon,\n  Add as AddIcon,\n  Settings as SettingsIcon,\n  SmartToy as BotIcon,\n  Person as PersonIcon,\n  MoreVert as MoreIcon,\n} from '@mui/icons-material';\nimport { formatDistanceToNow } from 'date-fns';\nimport { useAuth } from '../../contexts/AuthContext';\nimport websocketService from '../../services/websocket';\nimport toast from 'react-hot-toast';\n\nconst ChatInterface = ({ serverId, channelId, serverName, channelName, onBotInvite, onCreateBot, onShowBots }) => {\n  const { user } = useAuth();\n  const [messages, setMessages] = useState([]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isConnected, setIsConnected] = useState(false);\n  // const [onlineUsers, setOnlineUsers] = useState([]);\n  const [bots, setBots] = useState([]);\n  const [showBotMenu, setShowBotMenu] = useState(false);\n  const [botMenuAnchor, setBotMenuAnchor] = useState(null);\n  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);\n  const [availableBots, setAvailableBots] = useState([]);\n  \n  const messagesEndRef = useRef(null);\n  const wsRef = useRef(null);\n\n  useEffect(() => {\n    connectToChat();\n    loadMessages();\n    loadChannelBots();\n    \n    return () => {\n      if (wsRef.current) {\n        wsRef.current.disconnect();\n      }\n    };\n  }, [serverId, channelId]);\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const connectToChat = () => {\n    if (wsRef.current) {\n      wsRef.current.disconnect();\n    }\n\n    wsRef.current = websocketService;\n\n    wsRef.current.on('connected', () => {\n      console.log('WebSocket connected, authenticating...');\n    });\n\n    wsRef.current.on('authenticated', (userData) => {\n      console.log('Authenticated as:', userData);\n      setIsConnected(true);\n      toast.success('Connected to chat');\n\n      // Join the current channel\n      if (serverId && channelId) {\n        wsRef.current.joinChannel(serverId, channelId);\n      }\n    });\n\n    wsRef.current.on('authError', (error) => {\n      console.error('Authentication error:', error);\n      setIsConnected(false);\n      toast.error('Authentication failed');\n    });\n\n    wsRef.current.on('channelJoined', (data) => {\n      console.log('Joined channel:', data);\n    });\n\n    wsRef.current.on('disconnected', () => {\n      setIsConnected(false);\n      toast.error('Disconnected from chat');\n    });\n\n    wsRef.current.on('messageCreate', (message) => {\n      console.log('New message:', message);\n      setMessages(prev => [...prev, message]);\n    });\n\n    wsRef.current.on('error', (error) => {\n      console.error('WebSocket error:', error);\n      toast.error(error.message || 'WebSocket error');\n    });\n\n    // Connect to WebSocket with proper URL and token\n    const token = localStorage.getItem('token');\n    wsRef.current.connect('ws://localhost:3003/chat', token);\n  };\n\n  const loadMessages = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setMessages(data.messages || []);\n      }\n    } catch (error) {\n      console.error('Error loading messages:', error);\n    }\n  };\n\n  const loadChannelBots = async () => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setBots(data.bots || []);\n      }\n    } catch (error) {\n      console.error('Error loading channel bots:', error);\n    }\n  };\n\n  const loadAvailableBots = async () => {\n    try {\n      const response = await fetch('/api/applications', {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        }\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setAvailableBots(data.applications.filter(app => app.status === 'active'));\n      }\n    } catch (error) {\n      console.error('Error loading available bots:', error);\n    }\n  };\n\n  const sendMessage = async () => {\n    if (!newMessage.trim() || !isConnected) return;\n\n    try {\n      // Send via WebSocket for real-time delivery\n      if (wsRef.current && wsRef.current.isConnectedToServer()) {\n        wsRef.current.sendChatMessage(serverId, channelId, newMessage.trim());\n        setNewMessage('');\n      } else {\n        // Fallback to HTTP API\n        const messageData = {\n          content: newMessage,\n          channel_id: channelId,\n          server_id: serverId\n        };\n\n        const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          body: JSON.stringify(messageData)\n        });\n\n        if (response.ok) {\n          setNewMessage('');\n        } else {\n          toast.error('Failed to send message');\n        }\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      toast.error('Failed to send message');\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const inviteBot = async (botId) => {\n    try {\n      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        body: JSON.stringify({ bot_id: botId })\n      });\n\n      if (response.ok) {\n        toast.success('Bot invited successfully');\n        loadChannelBots();\n        setShowInviteBotDialog(false);\n        \n        // Trigger bot invite callback for code editor integration\n        if (onBotInvite) {\n          const botData = availableBots.find(bot => bot.id === botId);\n          onBotInvite(botData);\n        }\n      } else {\n        const errorData = await response.json();\n        toast.error(errorData.error || 'Failed to invite bot');\n      }\n    } catch (error) {\n      console.error('Error inviting bot:', error);\n      toast.error('Failed to invite bot');\n    }\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const getMessageTime = (timestamp) => {\n    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });\n  };\n\n  const isBot = (author) => {\n    return author.bot || bots.some(bot => bot.id === author.id);\n  };\n\n  return (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Chat Header - Discord Style */}\n      <Box\n        sx={{\n          p: 2,\n          borderBottom: 1,\n          borderColor: 'divider',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          bgcolor: '#36393f',\n          color: 'white'\n        }}\n      >\n        <Box display=\"flex\" alignItems=\"center\" gap={2}>\n          <Typography variant=\"h6\" sx={{ color: '#dcddde', fontWeight: 600 }}>\n            {serverName}\n          </Typography>\n          <Typography variant=\"body2\" sx={{ color: '#72767d' }}>\n            # {channelName}\n          </Typography>\n          <Chip\n            label={isConnected ? '🟢 Kết nối' : '🔴 Mất kết nối'}\n            color={isConnected ? 'success' : 'error'}\n            size=\"small\"\n            sx={{ bgcolor: isConnected ? '#43b581' : '#f04747', color: 'white' }}\n          />\n        </Box>\n\n        <Box display=\"flex\" gap={1}>\n          <Button\n            startIcon={<AddIcon />}\n            onClick={onCreateBot}\n            size=\"small\"\n            variant=\"outlined\"\n            sx={{ color: '#dcddde', borderColor: '#72767d' }}\n          >\n            Create Bot\n          </Button>\n\n          <Button\n            startIcon={<AddIcon />}\n            onClick={() => {\n              loadAvailableBots();\n              setShowInviteBotDialog(true);\n            }}\n            size=\"small\"\n            variant=\"outlined\"\n            sx={{ color: '#dcddde', borderColor: '#72767d' }}\n          >\n            Invite Bot\n          </Button>\n\n          <Button\n            onClick={onShowBots}\n            size=\"small\"\n            variant=\"contained\"\n            sx={{ bgcolor: '#5865f2' }}\n          >\n            My Bots\n          </Button>\n        </Box>\n      </Box>\n\n      {/* Messages Area - Discord Style */}\n      <Box sx={{\n        flex: 1,\n        overflow: 'auto',\n        p: 2,\n        bgcolor: '#36393f',\n        '&::-webkit-scrollbar': {\n          width: '8px',\n        },\n        '&::-webkit-scrollbar-track': {\n          background: '#2f3136',\n        },\n        '&::-webkit-scrollbar-thumb': {\n          background: '#202225',\n          borderRadius: '4px',\n        },\n      }}>\n        {messages.length === 0 ? (\n          <Box\n            display=\"flex\"\n            flexDirection=\"column\"\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            height=\"100%\"\n            sx={{ color: '#72767d' }}\n          >\n            <Typography variant=\"h6\" gutterBottom>\n              Chào mừng đến với #general-chat!\n            </Typography>\n            <Typography variant=\"body2\">\n              Đây là kênh chat chung. Hãy bắt đầu cuộc trò chuyện!\n            </Typography>\n          </Box>\n        ) : (\n          <List sx={{ p: 0 }}>\n            {messages.map((message, index) => (\n              <ListItem\n                key={message.id || index}\n                alignItems=\"flex-start\"\n                sx={{\n                  py: 1,\n                  px: 2,\n                  '&:hover': {\n                    bgcolor: 'rgba(79, 84, 92, 0.16)',\n                  },\n                  borderRadius: 1,\n                  mb: 0.5\n                }}\n              >\n                <ListItemAvatar>\n                  <Avatar\n                    sx={{\n                      bgcolor: isBot(message.author) ? '#5865f2' : '#43b581',\n                      width: 40,\n                      height: 40\n                    }}\n                  >\n                    {isBot(message.author) ? <BotIcon /> : <PersonIcon />}\n                  </Avatar>\n                </ListItemAvatar>\n                <ListItemText\n                  primary={\n                    <Box display=\"flex\" alignItems=\"center\" gap={1} mb={0.5}>\n                      <Typography\n                        variant=\"subtitle2\"\n                        fontWeight=\"600\"\n                        sx={{\n                          color: isBot(message.author) ? '#5865f2' : '#dcddde',\n                          fontSize: '16px'\n                        }}\n                      >\n                        {message.author.username}\n                      </Typography>\n                      {isBot(message.author) && (\n                        <Chip\n                          label=\"BOT\"\n                          size=\"small\"\n                          sx={{\n                            bgcolor: '#5865f2',\n                            color: 'white',\n                            fontSize: '10px',\n                            height: '16px',\n                            fontWeight: 'bold'\n                          }}\n                        />\n                      )}\n                      <Typography\n                        variant=\"caption\"\n                        sx={{\n                          color: '#72767d',\n                          fontSize: '12px',\n                          ml: 1\n                        }}\n                      >\n                        {getMessageTime(message.created_at)}\n                      </Typography>\n                    </Box>\n                  }\n                  secondary={\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        color: '#dcddde',\n                        whiteSpace: 'pre-wrap',\n                        fontSize: '16px',\n                        lineHeight: 1.375\n                      }}\n                    >\n                      {message.content}\n                    </Typography>\n                  }\n                />\n              </ListItem>\n            ))}\n          </List>\n        )}\n        <div ref={messagesEndRef} />\n      </Box>\n\n      {/* Message Input - Discord Style */}\n      <Box sx={{\n        p: 2,\n        bgcolor: '#36393f',\n        borderTop: '1px solid #40444b'\n      }}>\n        <Box\n          display=\"flex\"\n          gap={1}\n          sx={{\n            bgcolor: '#40444b',\n            borderRadius: '8px',\n            p: 1,\n            alignItems: 'flex-end'\n          }}\n        >\n          <TextField\n            fullWidth\n            multiline\n            maxRows={4}\n            placeholder={isConnected ? \"Nhắn tin tại #general-chat\" : \"Đang kết nối...\"}\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyDown={handleKeyPress}\n            disabled={!isConnected}\n            variant=\"standard\"\n            InputProps={{\n              disableUnderline: true,\n              sx: {\n                color: '#dcddde',\n                fontSize: '16px',\n                '& input::placeholder': {\n                  color: '#72767d',\n                  opacity: 1\n                },\n                '& textarea::placeholder': {\n                  color: '#72767d',\n                  opacity: 1\n                }\n              }\n            }}\n            sx={{\n              '& .MuiInputBase-root': {\n                bgcolor: 'transparent',\n              }\n            }}\n          />\n          <IconButton\n            onClick={sendMessage}\n            disabled={!newMessage.trim() || !isConnected}\n            sx={{\n              color: isConnected && newMessage.trim() ? '#5865f2' : '#72767d',\n              '&:hover': {\n                bgcolor: 'rgba(88, 101, 242, 0.1)',\n              }\n            }}\n          >\n            <SendIcon />\n          </IconButton>\n        </Box>\n        {!isConnected && (\n          <Typography\n            variant=\"caption\"\n            sx={{\n              color: '#f04747',\n              mt: 1,\n              display: 'block'\n            }}\n          >\n            ⚠️ Mất kết nối với server. Đang thử kết nối lại...\n          </Typography>\n        )}\n      </Box>\n\n      {/* Bot Menu */}\n      <Menu\n        anchorEl={botMenuAnchor}\n        open={showBotMenu}\n        onClose={() => setShowBotMenu(false)}\n      >\n        <MenuItem onClick={() => setShowInviteBotDialog(true)}>\n          <AddIcon sx={{ mr: 1 }} />\n          Invite Bot\n        </MenuItem>\n        <MenuItem>\n          <SettingsIcon sx={{ mr: 1 }} />\n          Channel Settings\n        </MenuItem>\n      </Menu>\n\n      {/* Invite Bot Dialog */}\n      <Dialog \n        open={showInviteBotDialog} \n        onClose={() => setShowInviteBotDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>Invite Bot to Channel</DialogTitle>\n        <DialogContent>\n          <List>\n            {availableBots.map((bot) => (\n              <ListItem key={bot.id}>\n                <ListItemAvatar>\n                  <Avatar>\n                    <BotIcon />\n                  </Avatar>\n                </ListItemAvatar>\n                <ListItemText\n                  primary={bot.name}\n                  secondary={bot.description || 'No description'}\n                />\n                <Button\n                  variant=\"contained\"\n                  size=\"small\"\n                  onClick={() => inviteBot(bot.id)}\n                  disabled={bots.some(b => b.id === bot.id)}\n                >\n                  {bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'}\n                </Button>\n              </ListItem>\n            ))}\n          </List>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowInviteBotDialog(false)}>\n            Close\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default ChatInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,cAAc,EACdC,IAAI,EACJC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,GAAG,IAAIC,OAAO,EACdC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,OAAO,EACnBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,QAAQ,QACf,qBAAqB;AAC5B,SAASC,mBAAmB,QAAQ,UAAU;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AACpD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,aAAa,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS;EAAEC,UAAU;EAAEC,WAAW;EAAEC,WAAW;EAAEC,WAAW;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAChH,MAAM;IAAEC;EAAK,CAAC,GAAGd,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACe,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACrD;EACA,MAAM,CAACsD,IAAI,EAAEC,OAAO,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwD,WAAW,EAAEC,cAAc,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC0D,aAAa,EAAEC,gBAAgB,CAAC,GAAG3D,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8D,aAAa,EAAEC,gBAAgB,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAMgE,cAAc,GAAG9D,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+D,KAAK,GAAG/D,MAAM,CAAC,IAAI,CAAC;EAE1BD,SAAS,CAAC,MAAM;IACdiE,aAAa,CAAC,CAAC;IACfC,YAAY,CAAC,CAAC;IACdC,eAAe,CAAC,CAAC;IAEjB,OAAO,MAAM;MACX,IAAIH,KAAK,CAACI,OAAO,EAAE;QACjBJ,KAAK,CAACI,OAAO,CAACC,UAAU,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAAC/B,QAAQ,EAAEC,SAAS,CAAC,CAAC;EAEzBvC,SAAS,CAAC,MAAM;IACdsE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACvB,QAAQ,CAAC,CAAC;EAEd,MAAMkB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAID,KAAK,CAACI,OAAO,EAAE;MACjBJ,KAAK,CAACI,OAAO,CAACC,UAAU,CAAC,CAAC;IAC5B;IAEAL,KAAK,CAACI,OAAO,GAAGnC,gBAAgB;IAEhC+B,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,WAAW,EAAE,MAAM;MAClCC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD,CAAC,CAAC;IAEFT,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,eAAe,EAAGG,QAAQ,IAAK;MAC9CF,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAAC;MAC1CtB,cAAc,CAAC,IAAI,CAAC;MACpBlB,KAAK,CAACyC,OAAO,CAAC,mBAAmB,CAAC;;MAElC;MACA,IAAIrC,QAAQ,IAAIC,SAAS,EAAE;QACzByB,KAAK,CAACI,OAAO,CAACQ,WAAW,CAACtC,QAAQ,EAAEC,SAAS,CAAC;MAChD;IACF,CAAC,CAAC;IAEFyB,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,WAAW,EAAGM,KAAK,IAAK;MACvCL,OAAO,CAACK,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CzB,cAAc,CAAC,KAAK,CAAC;MACrBlB,KAAK,CAAC2C,KAAK,CAAC,uBAAuB,CAAC;IACtC,CAAC,CAAC;IAEFb,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,eAAe,EAAGO,IAAI,IAAK;MAC1CN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEK,IAAI,CAAC;IACtC,CAAC,CAAC;IAEFd,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,cAAc,EAAE,MAAM;MACrCnB,cAAc,CAAC,KAAK,CAAC;MACrBlB,KAAK,CAAC2C,KAAK,CAAC,wBAAwB,CAAC;IACvC,CAAC,CAAC;IAEFb,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,eAAe,EAAGQ,OAAO,IAAK;MAC7CP,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEM,OAAO,CAAC;MACpC/B,WAAW,CAACgC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAED,OAAO,CAAC,CAAC;IACzC,CAAC,CAAC;IAEFf,KAAK,CAACI,OAAO,CAACG,EAAE,CAAC,OAAO,EAAGM,KAAK,IAAK;MACnCL,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxC3C,KAAK,CAAC2C,KAAK,CAACA,KAAK,CAACE,OAAO,IAAI,iBAAiB,CAAC;IACjD,CAAC,CAAC;;IAEF;IACA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3CnB,KAAK,CAACI,OAAO,CAACgB,OAAO,CAAC,0BAA0B,EAAEH,KAAK,CAAC;EAC1D,CAAC;EAED,MAAMf,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBhD,QAAQ,aAAaC,SAAS,WAAW,EAAE;QACtFgD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIE,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMV,IAAI,GAAG,MAAMO,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCzC,WAAW,CAAC8B,IAAI,CAAC/B,QAAQ,IAAI,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMV,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMkB,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBhD,QAAQ,aAAaC,SAAS,OAAO,EAAE;QAClFgD,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIE,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMV,IAAI,GAAG,MAAMO,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCnC,OAAO,CAACwB,IAAI,CAACzB,IAAI,IAAI,EAAE,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMa,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;QAChDC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D;MACF,CAAC,CAAC;MAEF,IAAIE,QAAQ,CAACG,EAAE,EAAE;QACf,MAAMV,IAAI,GAAG,MAAMO,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClC3B,gBAAgB,CAACgB,IAAI,CAACa,YAAY,CAACC,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,QAAQ,CAAC,CAAC;MAC5E;IACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAAC9C,UAAU,CAAC+C,IAAI,CAAC,CAAC,IAAI,CAAC7C,WAAW,EAAE;IAExC,IAAI;MACF;MACA,IAAIa,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAAC6B,mBAAmB,CAAC,CAAC,EAAE;QACxDjC,KAAK,CAACI,OAAO,CAAC8B,eAAe,CAAC5D,QAAQ,EAAEC,SAAS,EAAEU,UAAU,CAAC+C,IAAI,CAAC,CAAC,CAAC;QACrE9C,aAAa,CAAC,EAAE,CAAC;MACnB,CAAC,MAAM;QACL;QACA,MAAMiD,WAAW,GAAG;UAClBC,OAAO,EAAEnD,UAAU;UACnBoD,UAAU,EAAE9D,SAAS;UACrB+D,SAAS,EAAEhE;QACb,CAAC;QAED,MAAM+C,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBhD,QAAQ,aAAaC,SAAS,WAAW,EAAE;UACtFgE,MAAM,EAAE,MAAM;UACdhB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACP,WAAW;QAClC,CAAC,CAAC;QAEF,IAAId,QAAQ,CAACG,EAAE,EAAE;UACftC,aAAa,CAAC,EAAE,CAAC;QACnB,CAAC,MAAM;UACLhB,KAAK,CAAC2C,KAAK,CAAC,wBAAwB,CAAC;QACvC;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C3C,KAAK,CAAC2C,KAAK,CAAC,wBAAwB,CAAC;IACvC;EACF,CAAC;EAED,MAAM8B,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBhB,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,MAAMiB,SAAS,GAAG,MAAOC,KAAK,IAAK;IACjC,IAAI;MACF,MAAM5B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gBAAgBhD,QAAQ,aAAaC,SAAS,OAAO,EAAE;QAClFgE,MAAM,EAAE,MAAM;QACdhB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDqB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEQ,MAAM,EAAED;QAAM,CAAC;MACxC,CAAC,CAAC;MAEF,IAAI5B,QAAQ,CAACG,EAAE,EAAE;QACftD,KAAK,CAACyC,OAAO,CAAC,0BAA0B,CAAC;QACzCR,eAAe,CAAC,CAAC;QACjBP,sBAAsB,CAAC,KAAK,CAAC;;QAE7B;QACA,IAAIlB,WAAW,EAAE;UACf,MAAMyE,OAAO,GAAGtD,aAAa,CAACuD,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKL,KAAK,CAAC;UAC3DvE,WAAW,CAACyE,OAAO,CAAC;QACtB;MACF,CAAC,MAAM;QACL,MAAMI,SAAS,GAAG,MAAMlC,QAAQ,CAACI,IAAI,CAAC,CAAC;QACvCvD,KAAK,CAAC2C,KAAK,CAAC0C,SAAS,CAAC1C,KAAK,IAAI,sBAAsB,CAAC;MACxD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdL,OAAO,CAACK,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C3C,KAAK,CAAC2C,KAAK,CAAC,sBAAsB,CAAC;IACrC;EACF,CAAC;EAED,MAAMP,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAkD,qBAAA;IAC3B,CAAAA,qBAAA,GAAAzD,cAAc,CAACK,OAAO,cAAAoD,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,cAAc,GAAIC,SAAS,IAAK;IACpC,OAAO7F,mBAAmB,CAAC,IAAI8F,IAAI,CAACD,SAAS,CAAC,EAAE;MAAEE,SAAS,EAAE;IAAK,CAAC,CAAC;EACtE,CAAC;EAED,MAAMC,KAAK,GAAIC,MAAM,IAAK;IACxB,OAAOA,MAAM,CAACX,GAAG,IAAIhE,IAAI,CAAC4E,IAAI,CAACZ,GAAG,IAAIA,GAAG,CAACC,EAAE,KAAKU,MAAM,CAACV,EAAE,CAAC;EAC7D,CAAC;EAED,oBACElF,OAAA,CAAClC,GAAG;IAACgI,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpElG,OAAA,CAAClC,GAAG;MACFgI,EAAE,EAAE;QACFK,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,CAAC;QACfC,WAAW,EAAE,SAAS;QACtBL,OAAO,EAAE,MAAM;QACfM,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,OAAO,EAAE,SAAS;QAClBC,KAAK,EAAE;MACT,CAAE;MAAAP,QAAA,gBAEFlG,OAAA,CAAClC,GAAG;QAACkI,OAAO,EAAC,MAAM;QAACO,UAAU,EAAC,QAAQ;QAACG,GAAG,EAAE,CAAE;QAAAR,QAAA,gBAC7ClG,OAAA,CAACjC,UAAU;UAAC4I,OAAO,EAAC,IAAI;UAACb,EAAE,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAEG,UAAU,EAAE;UAAI,CAAE;UAAAV,QAAA,EAChE9F;QAAU;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACbhH,OAAA,CAACjC,UAAU;UAAC4I,OAAO,EAAC,OAAO;UAACb,EAAE,EAAE;YAAEW,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,GAAC,IAClD,EAAC7F,WAAW;QAAA;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACbhH,OAAA,CAACzB,IAAI;UACH0I,KAAK,EAAElG,WAAW,GAAG,YAAY,GAAG,gBAAiB;UACrD0F,KAAK,EAAE1F,WAAW,GAAG,SAAS,GAAG,OAAQ;UACzCmG,IAAI,EAAC,OAAO;UACZpB,EAAE,EAAE;YAAEU,OAAO,EAAEzF,WAAW,GAAG,SAAS,GAAG,SAAS;YAAE0F,KAAK,EAAE;UAAQ;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENhH,OAAA,CAAClC,GAAG;QAACkI,OAAO,EAAC,MAAM;QAACU,GAAG,EAAE,CAAE;QAAAR,QAAA,gBACzBlG,OAAA,CAACxB,MAAM;UACL2I,SAAS,eAAEnH,OAAA,CAACd,OAAO;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBI,OAAO,EAAE7G,WAAY;UACrB2G,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBb,EAAE,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAEJ,WAAW,EAAE;UAAU,CAAE;UAAAH,QAAA,EAClD;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThH,OAAA,CAACxB,MAAM;UACL2I,SAAS,eAAEnH,OAAA,CAACd,OAAO;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBI,OAAO,EAAEA,CAAA,KAAM;YACb9D,iBAAiB,CAAC,CAAC;YACnB9B,sBAAsB,CAAC,IAAI,CAAC;UAC9B,CAAE;UACF0F,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,UAAU;UAClBb,EAAE,EAAE;YAAEW,KAAK,EAAE,SAAS;YAAEJ,WAAW,EAAE;UAAU,CAAE;UAAAH,QAAA,EAClD;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAEThH,OAAA,CAACxB,MAAM;UACL4I,OAAO,EAAE5G,UAAW;UACpB0G,IAAI,EAAC,OAAO;UACZP,OAAO,EAAC,WAAW;UACnBb,EAAE,EAAE;YAAEU,OAAO,EAAE;UAAU,CAAE;UAAAN,QAAA,EAC5B;QAED;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhH,OAAA,CAAClC,GAAG;MAACgI,EAAE,EAAE;QACPuB,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,MAAM;QAChBnB,CAAC,EAAE,CAAC;QACJK,OAAO,EAAE,SAAS;QAClB,sBAAsB,EAAE;UACtBe,KAAK,EAAE;QACT,CAAC;QACD,4BAA4B,EAAE;UAC5BC,UAAU,EAAE;QACd,CAAC;QACD,4BAA4B,EAAE;UAC5BA,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE;QAChB;MACF,CAAE;MAAAvB,QAAA,GACCvF,QAAQ,CAAC+G,MAAM,KAAK,CAAC,gBACpB1H,OAAA,CAAClC,GAAG;QACFkI,OAAO,EAAC,MAAM;QACdC,aAAa,EAAC,QAAQ;QACtBM,UAAU,EAAC,QAAQ;QACnBD,cAAc,EAAC,QAAQ;QACvBP,MAAM,EAAC,MAAM;QACbD,EAAE,EAAE;UAAEW,KAAK,EAAE;QAAU,CAAE;QAAAP,QAAA,gBAEzBlG,OAAA,CAACjC,UAAU;UAAC4I,OAAO,EAAC,IAAI;UAACgB,YAAY;UAAAzB,QAAA,EAAC;QAEtC;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhH,OAAA,CAACjC,UAAU;UAAC4I,OAAO,EAAC,OAAO;UAAAT,QAAA,EAAC;QAE5B;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENhH,OAAA,CAAC7B,IAAI;QAAC2H,EAAE,EAAE;UAAEK,CAAC,EAAE;QAAE,CAAE;QAAAD,QAAA,EAChBvF,QAAQ,CAACiH,GAAG,CAAC,CAACjF,OAAO,EAAEkF,KAAK,kBAC3B7H,OAAA,CAAC5B,QAAQ;UAEPmI,UAAU,EAAC,YAAY;UACvBT,EAAE,EAAE;YACFgC,EAAE,EAAE,CAAC;YACLC,EAAE,EAAE,CAAC;YACL,SAAS,EAAE;cACTvB,OAAO,EAAE;YACX,CAAC;YACDiB,YAAY,EAAE,CAAC;YACfO,EAAE,EAAE;UACN,CAAE;UAAA9B,QAAA,gBAEFlG,OAAA,CAAC1B,cAAc;YAAA4H,QAAA,eACblG,OAAA,CAAC9B,MAAM;cACL4H,EAAE,EAAE;gBACFU,OAAO,EAAEb,KAAK,CAAChD,OAAO,CAACiD,MAAM,CAAC,GAAG,SAAS,GAAG,SAAS;gBACtD2B,KAAK,EAAE,EAAE;gBACTxB,MAAM,EAAE;cACV,CAAE;cAAAG,QAAA,EAEDP,KAAK,CAAChD,OAAO,CAACiD,MAAM,CAAC,gBAAG5F,OAAA,CAACV,OAAO;gBAAAuH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGhH,OAAA,CAACR,UAAU;gBAAAqH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eACjBhH,OAAA,CAAC3B,YAAY;YACX4J,OAAO,eACLjI,OAAA,CAAClC,GAAG;cAACkI,OAAO,EAAC,MAAM;cAACO,UAAU,EAAC,QAAQ;cAACG,GAAG,EAAE,CAAE;cAACsB,EAAE,EAAE,GAAI;cAAA9B,QAAA,gBACtDlG,OAAA,CAACjC,UAAU;gBACT4I,OAAO,EAAC,WAAW;gBACnBC,UAAU,EAAC,KAAK;gBAChBd,EAAE,EAAE;kBACFW,KAAK,EAAEd,KAAK,CAAChD,OAAO,CAACiD,MAAM,CAAC,GAAG,SAAS,GAAG,SAAS;kBACpDsC,QAAQ,EAAE;gBACZ,CAAE;gBAAAhC,QAAA,EAEDvD,OAAO,CAACiD,MAAM,CAACuC;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,EACZrB,KAAK,CAAChD,OAAO,CAACiD,MAAM,CAAC,iBACpB5F,OAAA,CAACzB,IAAI;gBACH0I,KAAK,EAAC,KAAK;gBACXC,IAAI,EAAC,OAAO;gBACZpB,EAAE,EAAE;kBACFU,OAAO,EAAE,SAAS;kBAClBC,KAAK,EAAE,OAAO;kBACdyB,QAAQ,EAAE,MAAM;kBAChBnC,MAAM,EAAE,MAAM;kBACda,UAAU,EAAE;gBACd;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,eACDhH,OAAA,CAACjC,UAAU;gBACT4I,OAAO,EAAC,SAAS;gBACjBb,EAAE,EAAE;kBACFW,KAAK,EAAE,SAAS;kBAChByB,QAAQ,EAAE,MAAM;kBAChBE,EAAE,EAAE;gBACN,CAAE;gBAAAlC,QAAA,EAEDX,cAAc,CAAC5C,OAAO,CAAC0F,UAAU;cAAC;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN;YACDsB,SAAS,eACPtI,OAAA,CAACjC,UAAU;cACT4I,OAAO,EAAC,OAAO;cACfb,EAAE,EAAE;gBACFW,KAAK,EAAE,SAAS;gBAChB8B,UAAU,EAAE,UAAU;gBACtBL,QAAQ,EAAE,MAAM;gBAChBM,UAAU,EAAE;cACd,CAAE;cAAAtC,QAAA,EAEDvD,OAAO,CAACqB;YAAO;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GA1EGrE,OAAO,CAACuC,EAAE,IAAI2C,KAAK;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2EhB,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACP,eACDhH,OAAA;QAAKyI,GAAG,EAAE9G;MAAe;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGNhH,OAAA,CAAClC,GAAG;MAACgI,EAAE,EAAE;QACPK,CAAC,EAAE,CAAC;QACJK,OAAO,EAAE,SAAS;QAClBkC,SAAS,EAAE;MACb,CAAE;MAAAxC,QAAA,gBACAlG,OAAA,CAAClC,GAAG;QACFkI,OAAO,EAAC,MAAM;QACdU,GAAG,EAAE,CAAE;QACPZ,EAAE,EAAE;UACFU,OAAO,EAAE,SAAS;UAClBiB,YAAY,EAAE,KAAK;UACnBtB,CAAC,EAAE,CAAC;UACJI,UAAU,EAAE;QACd,CAAE;QAAAL,QAAA,gBAEFlG,OAAA,CAAChC,SAAS;UACR2K,SAAS;UACTC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,WAAW,EAAE/H,WAAW,GAAG,4BAA4B,GAAG,iBAAkB;UAC5EgI,KAAK,EAAElI,UAAW;UAClBmI,QAAQ,EAAGxE,CAAC,IAAK1D,aAAa,CAAC0D,CAAC,CAACyE,MAAM,CAACF,KAAK,CAAE;UAC/CG,SAAS,EAAE3E,cAAe;UAC1B4E,QAAQ,EAAE,CAACpI,WAAY;UACvB4F,OAAO,EAAC,UAAU;UAClByC,UAAU,EAAE;YACVC,gBAAgB,EAAE,IAAI;YACtBvD,EAAE,EAAE;cACFW,KAAK,EAAE,SAAS;cAChByB,QAAQ,EAAE,MAAM;cAChB,sBAAsB,EAAE;gBACtBzB,KAAK,EAAE,SAAS;gBAChB6C,OAAO,EAAE;cACX,CAAC;cACD,yBAAyB,EAAE;gBACzB7C,KAAK,EAAE,SAAS;gBAChB6C,OAAO,EAAE;cACX;YACF;UACF,CAAE;UACFxD,EAAE,EAAE;YACF,sBAAsB,EAAE;cACtBU,OAAO,EAAE;YACX;UACF;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFhH,OAAA,CAAC/B,UAAU;UACTmJ,OAAO,EAAEzD,WAAY;UACrBwF,QAAQ,EAAE,CAACtI,UAAU,CAAC+C,IAAI,CAAC,CAAC,IAAI,CAAC7C,WAAY;UAC7C+E,EAAE,EAAE;YACFW,KAAK,EAAE1F,WAAW,IAAIF,UAAU,CAAC+C,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,SAAS;YAC/D,SAAS,EAAE;cACT4C,OAAO,EAAE;YACX;UACF,CAAE;UAAAN,QAAA,eAEFlG,OAAA,CAAChB,QAAQ;YAAA6H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EACL,CAACjG,WAAW,iBACXf,OAAA,CAACjC,UAAU;QACT4I,OAAO,EAAC,SAAS;QACjBb,EAAE,EAAE;UACFW,KAAK,EAAE,SAAS;UAChB8C,EAAE,EAAE,CAAC;UACLvD,OAAO,EAAE;QACX,CAAE;QAAAE,QAAA,EACH;MAED;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhH,OAAA,CAACvB,IAAI;MACH+K,QAAQ,EAAEnI,aAAc;MACxBoI,IAAI,EAAEtI,WAAY;MAClBuI,OAAO,EAAEA,CAAA,KAAMtI,cAAc,CAAC,KAAK,CAAE;MAAA8E,QAAA,gBAErClG,OAAA,CAACtB,QAAQ;QAAC0I,OAAO,EAAEA,CAAA,KAAM5F,sBAAsB,CAAC,IAAI,CAAE;QAAA0E,QAAA,gBACpDlG,OAAA,CAACd,OAAO;UAAC4G,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,cAE5B;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC,eACXhH,OAAA,CAACtB,QAAQ;QAAAwH,QAAA,gBACPlG,OAAA,CAACZ,YAAY;UAAC0G,EAAE,EAAE;YAAE6D,EAAE,EAAE;UAAE;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,oBAEjC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGPhH,OAAA,CAACrB,MAAM;MACL8K,IAAI,EAAElI,mBAAoB;MAC1BmI,OAAO,EAAEA,CAAA,KAAMlI,sBAAsB,CAAC,KAAK,CAAE;MAC7CoI,QAAQ,EAAC,IAAI;MACbjB,SAAS;MAAAzC,QAAA,gBAETlG,OAAA,CAACpB,WAAW;QAAAsH,QAAA,EAAC;MAAqB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAChDhH,OAAA,CAACnB,aAAa;QAAAqH,QAAA,eACZlG,OAAA,CAAC7B,IAAI;UAAA+H,QAAA,EACFzE,aAAa,CAACmG,GAAG,CAAE3C,GAAG,iBACrBjF,OAAA,CAAC5B,QAAQ;YAAA8H,QAAA,gBACPlG,OAAA,CAAC1B,cAAc;cAAA4H,QAAA,eACblG,OAAA,CAAC9B,MAAM;gBAAAgI,QAAA,eACLlG,OAAA,CAACV,OAAO;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACjBhH,OAAA,CAAC3B,YAAY;cACX4J,OAAO,EAAEhD,GAAG,CAAC4E,IAAK;cAClBvB,SAAS,EAAErD,GAAG,CAAC6E,WAAW,IAAI;YAAiB;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACFhH,OAAA,CAACxB,MAAM;cACLmI,OAAO,EAAC,WAAW;cACnBO,IAAI,EAAC,OAAO;cACZE,OAAO,EAAEA,CAAA,KAAMxC,SAAS,CAACK,GAAG,CAACC,EAAE,CAAE;cACjCiE,QAAQ,EAAElI,IAAI,CAAC4E,IAAI,CAACkE,CAAC,IAAIA,CAAC,CAAC7E,EAAE,KAAKD,GAAG,CAACC,EAAE,CAAE;cAAAgB,QAAA,EAEzCjF,IAAI,CAAC4E,IAAI,CAACkE,CAAC,IAAIA,CAAC,CAAC7E,EAAE,KAAKD,GAAG,CAACC,EAAE,CAAC,GAAG,OAAO,GAAG;YAAQ;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA,GAjBI/B,GAAG,CAACC,EAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBX,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAChBhH,OAAA,CAAClB,aAAa;QAAAoH,QAAA,eACZlG,OAAA,CAACxB,MAAM;UAAC4I,OAAO,EAAEA,CAAA,KAAM5F,sBAAsB,CAAC,KAAK,CAAE;UAAA0E,QAAA,EAAC;QAEtD;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvG,EAAA,CAxhBIR,aAAa;EAAA,QACAL,OAAO;AAAA;AAAAoK,EAAA,GADpB/J,aAAa;AA0hBnB,eAAeA,aAAa;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}