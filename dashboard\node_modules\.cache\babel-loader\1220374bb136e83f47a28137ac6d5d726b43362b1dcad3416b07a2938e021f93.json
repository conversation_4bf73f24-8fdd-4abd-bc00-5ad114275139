{"ast": null, "code": "var isArrayLike = require('./isArrayLike');\n\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseEach(eachFunc, fromRight) {\n  return function (collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n    var length = collection.length,\n      index = fromRight ? length : -1,\n      iterable = Object(collection);\n    while (fromRight ? index-- : ++index < length) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n    return collection;\n  };\n}\nmodule.exports = createBaseEach;", "map": {"version": 3, "names": ["isArrayLike", "require", "createBaseEach", "eachFunc", "fromRight", "collection", "iteratee", "length", "index", "iterable", "Object", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_createBaseEach.js"], "sourcesContent": ["var isArrayLike = require('./isArrayLike');\n\n/**\n * Creates a `baseEach` or `baseEachRight` function.\n *\n * @private\n * @param {Function} eachFunc The function to iterate over a collection.\n * @param {boolean} [fromRight] Specify iterating from right to left.\n * @returns {Function} Returns the new base function.\n */\nfunction createBaseEach(eachFunc, fromRight) {\n  return function(collection, iteratee) {\n    if (collection == null) {\n      return collection;\n    }\n    if (!isArrayLike(collection)) {\n      return eachFunc(collection, iteratee);\n    }\n    var length = collection.length,\n        index = fromRight ? length : -1,\n        iterable = Object(collection);\n\n    while ((fromRight ? index-- : ++index < length)) {\n      if (iteratee(iterable[index], index, iterable) === false) {\n        break;\n      }\n    }\n    return collection;\n  };\n}\n\nmodule.exports = createBaseEach;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,QAAQ,EAAEC,SAAS,EAAE;EAC3C,OAAO,UAASC,UAAU,EAAEC,QAAQ,EAAE;IACpC,IAAID,UAAU,IAAI,IAAI,EAAE;MACtB,OAAOA,UAAU;IACnB;IACA,IAAI,CAACL,WAAW,CAACK,UAAU,CAAC,EAAE;MAC5B,OAAOF,QAAQ,CAACE,UAAU,EAAEC,QAAQ,CAAC;IACvC;IACA,IAAIC,MAAM,GAAGF,UAAU,CAACE,MAAM;MAC1BC,KAAK,GAAGJ,SAAS,GAAGG,MAAM,GAAG,CAAC,CAAC;MAC/BE,QAAQ,GAAGC,MAAM,CAACL,UAAU,CAAC;IAEjC,OAAQD,SAAS,GAAGI,KAAK,EAAE,GAAG,EAAEA,KAAK,GAAGD,MAAM,EAAG;MAC/C,IAAID,QAAQ,CAACG,QAAQ,CAACD,KAAK,CAAC,EAAEA,KAAK,EAAEC,QAAQ,CAAC,KAAK,KAAK,EAAE;QACxD;MACF;IACF;IACA,OAAOJ,UAAU;EACnB,CAAC;AACH;AAEAM,MAAM,CAACC,OAAO,GAAGV,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}