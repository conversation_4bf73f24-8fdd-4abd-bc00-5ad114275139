{"ast": null, "code": "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}", "map": {"version": 3, "names": ["getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "map", "item", "brand", "version", "join", "userAgent"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/@popperjs/core/lib/utils/userAgent.js"], "sourcesContent": ["export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAAA,EAAG;EACpC,IAAIC,MAAM,GAAGC,SAAS,CAACC,aAAa;EAEpC,IAAIF,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACL,MAAM,CAACG,MAAM,CAAC,EAAE;IACnE,OAAOH,MAAM,CAACG,MAAM,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAE;MACvC,OAAOA,IAAI,CAACC,KAAK,GAAG,GAAG,GAAGD,IAAI,CAACE,OAAO;IACxC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd;EAEA,OAAOT,SAAS,CAACU,SAAS;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}