import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Chip,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  Save as SaveIcon,
  PlayArrow as TestIcon,
  Publish as DeployIcon,
  Settings as SettingsIcon,
  History as HistoryIcon,
  FolderOpen as TemplateIcon,
  BugReport as DebugIcon,
  Refresh as RefreshIcon,
  MoreVert as MoreIcon,
  Add as AddIcon,
  Close as CloseIcon,
  ContentCopy as ContentCopyIcon,
} from '@mui/icons-material';
import Editor from '@monaco-editor/react';
import toast from 'react-hot-toast';

// Import our custom components
import CodeTemplateSelector from './CodeTemplateSelector';
import LiveConsole from './LiveConsole';
import FileExplorer from './FileExplorer';
import CodeHistory from './CodeHistory';
import DeploymentPanel from './DeploymentPanel';

const CodeEditor = () => {
  const { id: applicationId, codeId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  // Get bot context from navigation state
  const botContext = location.state || {};

  // State management
  const [code, setCode] = useState('');
  const [originalCode, setOriginalCode] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isDeploying, setIsDeploying] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Bot context
  const [botToken, setBotToken] = useState(botContext.botToken || '');
  const [isConnectedToChat, setIsConnectedToChat] = useState(false);
  const [chatServerId, setChatServerId] = useState(botContext.serverId || '');
  const [chatChannelId, setChatChannelId] = useState(botContext.channelId || '');

  // UI State
  const [activeTab, setActiveTab] = useState(0);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);
  const [showDeployDialog, setShowDeployDialog] = useState(false);
  const [settingsMenuAnchor, setSettingsMenuAnchor] = useState(null);

  // Editor state
  const [editorTheme, setEditorTheme] = useState('vs-dark');
  const [fontSize, setFontSize] = useState(14);
  const [wordWrap, setWordWrap] = useState('on');
  const [minimap, setMinimap] = useState(false);

  // Console and testing
  const [consoleLogs, setConsoleLogs] = useState([]);
  const [testResults, setTestResults] = useState(null);
  const [validationErrors, setValidationErrors] = useState([]);

  // File management
  const [files, setFiles] = useState([]);
  const [currentFile, setCurrentFile] = useState(null);

  // Code versions and history
  const [codeVersions, setCodeVersions] = useState([]);
  const [currentVersion, setCurrentVersion] = useState(null);

  // Refs
  const editorRef = useRef(null);
  const monacoRef = useRef(null);

  // Auto-save timer
  const autoSaveTimerRef = useRef(null);

  // Load initial data
  useEffect(() => {
    loadCodeData();
    loadCodeVersions();
  }, [applicationId, codeId]);

  // Auto-save functionality
  useEffect(() => {
    if (hasUnsavedChanges && code !== originalCode) {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }

      autoSaveTimerRef.current = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity
    }

    return () => {
      if (autoSaveTimerRef.current) {
        clearTimeout(autoSaveTimerRef.current);
      }
    };
  }, [code, hasUnsavedChanges, originalCode]);

  const loadCodeData = async () => {
    try {
      setIsLoading(true);

      let response;
      if (codeId) {
        // Load specific code version
        response = await fetch(`/api/applications/${applicationId}/code/${codeId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
      } else {
        // Load latest code or create new
        const codesResponse = await fetch(`/api/applications/${applicationId}/code`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (codesResponse.ok) {
          const codesData = await codesResponse.json();
          if (codesData.codes && codesData.codes.length > 0) {
            // Use the latest draft or deployed version
            const latestCode = codesData.codes.find(c => c.status === 'draft') || codesData.codes[0];
            response = await fetch(`/api/applications/${applicationId}/code/${latestCode.id}`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            });
          } else {
            // No code exists, start with template
            setShowTemplateDialog(true);
            setIsLoading(false);
            return;
          }
        }
      }

      if (response && response.ok) {
        const data = await response.json();
        const codeData = data.code;

        setCode(codeData.code || '');
        setOriginalCode(codeData.code || '');
        setCurrentVersion(codeData);
        setHasUnsavedChanges(false);

        // Set validation errors if any
        if (codeData.validation_errors) {
          setValidationErrors(codeData.validation_errors);
        }
      } else {
        toast.error('Failed to load code');
      }
    } catch (error) {
      console.error('Error loading code:', error);
      toast.error('Failed to load code');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCodeVersions = async () => {
    try {
      const response = await fetch(`/api/applications/${applicationId}/code`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCodeVersions(data.codes || []);
      }
    } catch (error) {
      console.error('Error loading code versions:', error);
    }
  };
  const handleAutoSave = async () => {
    if (!hasUnsavedChanges || !currentVersion) return;

    try {
      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          code: code
        })
      });

      if (response.ok) {
        setOriginalCode(code);
        setHasUnsavedChanges(false);
        toast.success('Auto-saved', { duration: 1000 });
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);

      let response;
      if (currentVersion && currentVersion.status === 'draft') {
        // Update existing draft
        response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            code: code
          })
        });
      } else {
        // Create new version
        response = await fetch(`/api/applications/${applicationId}/code`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            code: code,
            entry_point: 'index.js'
          })
        });
      }

      if (response.ok) {
        const data = await response.json();
        setCurrentVersion(data.code);
        setOriginalCode(code);
        setHasUnsavedChanges(false);

        if (data.validation && !data.validation.isValid) {
          setValidationErrors(data.validation.errors || []);
          toast.error('Code saved with validation errors');
        } else {
          setValidationErrors([]);
          toast.success('Code saved successfully');
        }

        // Reload versions
        loadCodeVersions();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to save code');

        if (errorData.validation_errors) {
          setValidationErrors(errorData.validation_errors);
        }
      }
    } catch (error) {
      console.error('Error saving code:', error);
      toast.error('Failed to save code');
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    try {
      setIsTesting(true);
      setConsoleLogs([]);
      setTestResults(null);

      // First save the code if there are unsaved changes
      if (hasUnsavedChanges) {
        await handleSave();
      }

      if (!currentVersion) {
        toast.error('Please save the code first');
        return;
      }

      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          test_input: {
            message: {
              content: '!ping',
              channel_id: 'test-channel-1',
              author: { id: 'test-user', username: 'TestUser' }
            }
          }
        })
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(data.result);

        // Add test results to console
        addToConsole('=== Test Results ===', 'info');
        addToConsole(JSON.stringify(data.result, null, 2), 'log');

        toast.success('Code test completed');
      } else {
        const errorData = await response.json();
        setTestResults({ error: errorData.message });
        addToConsole(`Test Error: ${errorData.message}`, 'error');
        toast.error('Code test failed');
      }
    } catch (error) {
      console.error('Error testing code:', error);
      addToConsole(`Test Error: ${error.message}`, 'error');
      toast.error('Failed to test code');
    } finally {
      setIsTesting(false);
    }
  };

  const handleDeploy = async () => {
    try {
      setIsDeploying(true);

      // First save the code if there are unsaved changes
      if (hasUnsavedChanges) {
        await handleSave();
      }

      if (!currentVersion) {
        toast.error('Please save the code first');
        return;
      }

      const response = await fetch(`/api/applications/${applicationId}/code/${currentVersion.id}/deploy`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentVersion(data.code);
        toast.success('Code deployed successfully');

        // Reload versions to show the new deployed version
        loadCodeVersions();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to deploy code');

        if (errorData.validation_errors) {
          setValidationErrors(errorData.validation_errors);
        }
      }
    } catch (error) {
      console.error('Error deploying code:', error);
      toast.error('Failed to deploy code');
    } finally {
      setIsDeploying(false);
    }
  };

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // Configure Monaco Editor for Bot SDK
    setupBotSDKIntelliSense(monaco);

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Enter, () => {
      handleTest();
    });
  };

  const setupBotSDKIntelliSense = (monaco) => {
    // Register completion item provider for Bot SDK
    monaco.languages.registerCompletionItemProvider('javascript', {
      provideCompletionItems: (model, position) => {
        const suggestions = [
          {
            label: 'bot.sendMessage',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'bot.sendMessage(${1:channelId}, ${2:message})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Send a message to a channel'
          },
          {
            label: 'bot.on',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'bot.on(\'${1:event}\', async (${2:data}) => {\n\t${3:// Handle event}\n});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Register an event handler'
          },
          {
            label: 'bot.storage.set',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'await bot.storage.set(${1:key}, ${2:value})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Store a value in bot storage'
          },
          {
            label: 'bot.storage.get',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'await bot.storage.get(${1:key})',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Retrieve a value from bot storage'
          },
          {
            label: 'bot.registerCommand',
            kind: monaco.languages.CompletionItemKind.Method,
            insertText: 'bot.registerCommand(\'${1:commandName}\', async (${2:context}) => {\n\t${3:// Command logic}\n\tawait context.reply(\'${4:response}\');\n});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Register a bot command'
          }
        ];

        return { suggestions };
      }
    });

    // Add hover provider for Bot SDK methods
    monaco.languages.registerHoverProvider('javascript', {
      provideHover: (model, position) => {
        const word = model.getWordAtPosition(position);
        if (!word) return;

        const botSDKDocs = {
          'sendMessage': 'Send a message to a specific channel\n\nParameters:\n- channelId: string - The ID of the channel\n- message: string - The message content',
          'storage': 'Bot persistent storage system for saving data between sessions',
          'registerCommand': 'Register a new bot command that users can invoke'
        };

        if (botSDKDocs[word.word]) {
          return {
            range: new monaco.Range(position.lineNumber, word.startColumn, position.lineNumber, word.endColumn),
            contents: [
              { value: `**${word.word}**` },
              { value: botSDKDocs[word.word] }
            ]
          };
        }
      }
    });
  };

  const addToConsole = useCallback((message, type = 'log') => {
    setConsoleLogs(prev => [...prev, {
      message,
      type,
      timestamp: new Date().toLocaleTimeString()
    }]);
  }, []);

  const handleCodeChange = (value) => {
    setCode(value || '');
    setHasUnsavedChanges(value !== originalCode);
  };

  const handleTemplateSelect = (templateCode) => {
    setCode(templateCode);
    setHasUnsavedChanges(true);
    setShowTemplateDialog(false);
  };

  const clearConsole = () => {
    setConsoleLogs([]);
    setTestResults(null);
  };
  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box className="fade-in" sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} px={2} py={1}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h4" fontWeight="bold">
            Code Editor
          </Typography>
          {hasUnsavedChanges && (
            <Chip
              label="Unsaved Changes"
              color="warning"
              size="small"
              variant="outlined"
            />
          )}
          {currentVersion && (
            <Chip
              label={`v${currentVersion.version} (${currentVersion.status})`}
              color={currentVersion.status === 'deployed' ? 'success' : 'default'}
              size="small"
            />
          )}
        </Box>

        <Box display="flex" gap={1}>
          <Tooltip title="Templates">
            <IconButton onClick={() => setShowTemplateDialog(true)}>
              <TemplateIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="History">
            <IconButton onClick={() => setShowHistoryDialog(true)}>
              <HistoryIcon />
            </IconButton>
          </Tooltip>

          <Tooltip title="Settings">
            <IconButton onClick={(e) => setSettingsMenuAnchor(e.currentTarget)}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>

          <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />

          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            disabled={isSaving || !hasUnsavedChanges}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </Button>

          <Button
            variant="outlined"
            startIcon={<TestIcon />}
            onClick={handleTest}
            disabled={isTesting}
          >
            {isTesting ? 'Testing...' : 'Test'}
          </Button>

          <Button
            variant="contained"
            startIcon={<DeployIcon />}
            onClick={handleDeploy}
            disabled={isDeploying || hasUnsavedChanges}
          >
            {isDeploying ? 'Deploying...' : 'Deploy'}
          </Button>
        </Box>
      </Box>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Box px={2} mb={2}>
          <Alert severity="error" sx={{ mb: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Code Validation Errors:
            </Typography>
            {validationErrors.map((error, index) => (
              <Typography key={index} variant="body2">
                • {error}
              </Typography>
            ))}
          </Alert>
        </Box>
      )}

      {/* Main Content */}
      <Box sx={{ flex: 1, display: 'flex', overflow: 'hidden' }}>
        <Grid container sx={{ height: '100%' }}>
          {/* Code Editor */}
          <Grid item xs={12} lg={8} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flex: 1, p: 0, '&:last-child': { pb: 0 } }}>
                <Editor
                  height="100%"
                  defaultLanguage="javascript"
                  value={code}
                  onChange={handleCodeChange}
                  onMount={handleEditorDidMount}
                  theme={editorTheme}
                  options={{
                    fontSize: fontSize,
                    wordWrap: wordWrap,
                    minimap: { enabled: minimap },
                    automaticLayout: true,
                    scrollBeyondLastLine: false,
                    renderLineHighlight: 'all',
                    selectOnLineNumbers: true,
                    matchBrackets: 'always',
                    autoClosingBrackets: 'always',
                    autoClosingQuotes: 'always',
                    folding: true,
                    foldingHighlight: true,
                    showFoldingControls: 'always',
                    smoothScrolling: true,
                    cursorBlinking: 'smooth',
                    cursorSmoothCaretAnimation: true,
                    suggest: {
                      showKeywords: true,
                      showSnippets: true,
                      showFunctions: true,
                      showConstructors: true,
                      showFields: true,
                      showVariables: true,
                      showClasses: true,
                      showStructs: true,
                      showInterfaces: true,
                      showModules: true,
                      showProperties: true,
                      showEvents: true,
                      showOperators: true,
                      showUnits: true,
                      showValues: true,
                      showConstants: true,
                      showEnums: true,
                      showEnumMembers: true,
                      showColors: true,
                      showFiles: true,
                      showReferences: true,
                      showFolders: true,
                      showTypeParameters: true,
                      showIssues: true,
                      showUsers: true,
                    }
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          {/* Side Panel */}
          <Grid item xs={12} lg={4} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={activeTab}
                  onChange={(e, newValue) => setActiveTab(newValue)}
                  variant="fullWidth"
                >
                  <Tab label="Console" />
                  <Tab label="Info" />
                </Tabs>
              </Box>

              <CardContent sx={{ flex: 1, p: 0, overflow: 'hidden' }}>
                {activeTab === 0 && (
                  <LiveConsole
                    logs={consoleLogs}
                    onClear={clearConsole}
                    testResults={testResults}
                  />
                )}

                {activeTab === 1 && (
                  <Box p={2}>
                    <Typography variant="h6" gutterBottom>
                      Bot Information
                    </Typography>

                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Application ID: {applicationId}
                    </Typography>

                    {/* Bot Token Section */}
                    <Box sx={{ my: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Bot Token:
                      </Typography>
                      {botToken ? (
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="body2" fontFamily="monospace" sx={{
                            bgcolor: 'background.paper',
                            p: 1,
                            borderRadius: 1,
                            border: 1,
                            borderColor: 'divider',
                            flex: 1
                          }}>
                            {botToken.substring(0, 8)}...{botToken.substring(botToken.length - 4)}
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => {
                              navigator.clipboard.writeText(botToken);
                              toast.success('Token copied to clipboard');
                            }}
                          >
                            <ContentCopyIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No token available
                        </Typography>
                      )}
                    </Box>

                    {/* Chat Context */}
                    {(chatServerId || chatChannelId) && (
                      <Box sx={{ my: 2, p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Chat Context:
                        </Typography>
                        {chatServerId && (
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Server ID: {chatServerId}
                          </Typography>
                        )}
                        {chatChannelId && (
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Channel ID: {chatChannelId}
                          </Typography>
                        )}
                        <Chip
                          label={isConnectedToChat ? 'Connected to Chat' : 'Not Connected'}
                          color={isConnectedToChat ? 'success' : 'default'}
                          size="small"
                          sx={{ mt: 1 }}
                        />
                      </Box>
                    )}

                    {currentVersion && (
                      <>
                        <Divider sx={{ my: 2 }} />
                        <Typography variant="subtitle2" gutterBottom>
                          Version Info:
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Version: {currentVersion.version}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Status: {currentVersion.status}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Created: {new Date(currentVersion.created_at).toLocaleString()}
                        </Typography>
                        {currentVersion.deployed_at && (
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Deployed: {new Date(currentVersion.deployed_at).toLocaleString()}
                          </Typography>
                        )}
                      </>
                    )}

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="subtitle2" gutterBottom>
                      Keyboard Shortcuts:
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      • Ctrl/Cmd + S: Save
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      • Ctrl/Cmd + Enter: Test
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      • Ctrl/Cmd + /: Toggle Comment
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
      {/* Settings Menu */}
      <Menu
        anchorEl={settingsMenuAnchor}
        open={Boolean(settingsMenuAnchor)}
        onClose={() => setSettingsMenuAnchor(null)}
      >
        <MenuItem onClick={() => {
          setEditorTheme(editorTheme === 'vs-dark' ? 'light' : 'vs-dark');
          setSettingsMenuAnchor(null);
        }}>
          Toggle Theme
        </MenuItem>
        <MenuItem onClick={() => {
          setFontSize(fontSize === 14 ? 16 : 14);
          setSettingsMenuAnchor(null);
        }}>
          Font Size: {fontSize}px
        </MenuItem>
        <MenuItem onClick={() => {
          setWordWrap(wordWrap === 'on' ? 'off' : 'on');
          setSettingsMenuAnchor(null);
        }}>
          Word Wrap: {wordWrap}
        </MenuItem>
        <MenuItem onClick={() => {
          setMinimap(!minimap);
          setSettingsMenuAnchor(null);
        }}>
          Minimap: {minimap ? 'On' : 'Off'}
        </MenuItem>
      </Menu>

      {/* Template Selection Dialog */}
      <Dialog
        open={showTemplateDialog}
        onClose={() => setShowTemplateDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Choose a Bot Template
        </DialogTitle>
        <DialogContent>
          <CodeTemplateSelector onSelect={handleTemplateSelect} />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplateDialog(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* History Dialog */}
      <Dialog
        open={showHistoryDialog}
        onClose={() => setShowHistoryDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Code History
        </DialogTitle>
        <DialogContent>
          <CodeHistory
            versions={codeVersions}
            currentVersion={currentVersion}
            onRestore={(version) => {
              setCode(version.code);
              setHasUnsavedChanges(true);
              setShowHistoryDialog(false);
              toast.success('Code restored from history');
            }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowHistoryDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CodeEditor;
