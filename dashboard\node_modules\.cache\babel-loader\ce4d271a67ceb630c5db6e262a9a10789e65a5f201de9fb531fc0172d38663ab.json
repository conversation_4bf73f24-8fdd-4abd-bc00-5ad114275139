{"ast": null, "code": "var baseIndexOf = require('./_baseIndexOf');\n\n/**\n * A specialized version of `_.includes` for arrays without support for\n * specifying an index to search from.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludes(array, value) {\n  var length = array == null ? 0 : array.length;\n  return !!length && baseIndexOf(array, value, 0) > -1;\n}\nmodule.exports = arrayIncludes;", "map": {"version": 3, "names": ["baseIndexOf", "require", "arrayIncludes", "array", "value", "length", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_arrayIncludes.js"], "sourcesContent": ["var baseIndexOf = require('./_baseIndexOf');\n\n/**\n * A specialized version of `_.includes` for arrays without support for\n * specifying an index to search from.\n *\n * @private\n * @param {Array} [array] The array to inspect.\n * @param {*} target The value to search for.\n * @returns {boolean} Returns `true` if `target` is found, else `false`.\n */\nfunction arrayIncludes(array, value) {\n  var length = array == null ? 0 : array.length;\n  return !!length && baseIndexOf(array, value, 0) > -1;\n}\n\nmodule.exports = arrayIncludes;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,OAAO,CAAC,CAACA,MAAM,IAAIL,WAAW,CAACG,KAAK,EAAEC,KAAK,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACtD;AAEAE,MAAM,CAACC,OAAO,GAAGL,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}