import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Divider,
  Alert,
} from '@mui/material';
import {
  SmartToy as BotIcon,
  Code as CodeIcon,
  Add as AddIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import ServerSidebar from './ServerSidebar';
import ChatInterface from './ChatInterface';
import { useAuth } from '../../contexts/AuthContext';
import toast from 'react-hot-toast';

const Chat = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedServer, setSelectedServer] = useState(null);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [showBotCodeDialog, setShowBotCodeDialog] = useState(false);
  const [selectedBot, setSelectedBot] = useState(null);
  const [userBots, setUserBots] = useState([]);

  useEffect(() => {
    loadUserBots();
  }, []);

  const loadUserBots = async () => {
    try {
      const response = await fetch('/api/applications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUserBots(data.applications || []);
      }
    } catch (error) {
      console.error('Error loading user bots:', error);
    }
  };

  const handleBotInvite = (botData) => {
    // When a bot is invited to a channel, show option to edit its code
    setSelectedBot(botData);
    setShowBotCodeDialog(true);
  };

  const openCodeEditor = (bot) => {
    // Navigate to code editor with bot context
    navigate(`/applications/${bot.id}/code`, {
      state: {
        serverId: selectedServer?.id,
        channelId: selectedChannel?.id,
        botToken: bot.bot_token
      }
    });
  };

  const createNewBot = () => {
    navigate('/applications/new');
  };

  const getBotStatus = (bot) => {
    // This would check if bot is online/connected
    return Math.random() > 0.5 ? 'online' : 'offline';
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex' }}>
      {/* Server Sidebar */}
      <ServerSidebar
        selectedServer={selectedServer}
        selectedChannel={selectedChannel}
        onServerSelect={setSelectedServer}
        onChannelSelect={setSelectedChannel}
      />

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {selectedServer && selectedChannel ? (
          <>
            {/* Chat Header with Bot Info */}
            <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h5" gutterBottom>
                    {selectedServer.name}
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    # {selectedChannel.name}
                  </Typography>
                </Box>
                
                <Box display="flex" gap={2}>
                  <Button
                    startIcon={<AddIcon />}
                    onClick={createNewBot}
                    variant="outlined"
                  >
                    Create Bot
                  </Button>
                  
                  <Button
                    startIcon={<CodeIcon />}
                    onClick={() => setShowBotCodeDialog(true)}
                    variant="contained"
                  >
                    My Bots
                  </Button>
                </Box>
              </Box>
            </Box>

            {/* Chat Interface */}
            <ChatInterface
              serverId={selectedServer.id}
              channelId={selectedChannel.id}
              onBotInvite={handleBotInvite}
            />
          </>
        ) : (
          /* Welcome Screen */
          <Box 
            sx={{ 
              flex: 1, 
              display: 'flex', 
              flexDirection: 'column',
              justifyContent: 'center', 
              alignItems: 'center',
              p: 4
            }}
          >
            <Typography variant="h4" gutterBottom>
              Welcome to Discord-like Chat! 🚀
            </Typography>
            <Typography variant="body1" color="text.secondary" textAlign="center" mb={4}>
              Create a server and channels to start chatting and testing your bots in real-time.
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3, maxWidth: 600 }}>
              <Typography variant="body2">
                <strong>How it works:</strong>
                <br />
                1. Create a server and channels
                <br />
                2. Create or invite bots to your channels
                <br />
                3. Use the Code Editor to write custom bot logic
                <br />
                4. Test your bots in real-time chat!
              </Typography>
            </Alert>

            <Box display="flex" gap={2}>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={createNewBot}
                size="large"
              >
                Create Your First Bot
              </Button>
              
              <Button
                variant="outlined"
                onClick={() => setShowBotCodeDialog(true)}
                size="large"
              >
                View My Bots
              </Button>
            </Box>
          </Box>
        )}
      </Box>

      {/* Bot Code Dialog */}
      <Dialog 
        open={showBotCodeDialog} 
        onClose={() => setShowBotCodeDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box display="flex" alignItems="center" gap={2}>
            <BotIcon />
            My Bots
          </Box>
        </DialogTitle>
        <DialogContent>
          {userBots.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="h6" gutterBottom>
                No bots yet
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={3}>
                Create your first bot to start building custom Discord-like functionality!
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => {
                  setShowBotCodeDialog(false);
                  createNewBot();
                }}
              >
                Create Bot
              </Button>
            </Box>
          ) : (
            <List>
              {userBots.map((bot, index) => (
                <React.Fragment key={bot.id}>
                  <ListItem>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        <BotIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={2}>
                          <Typography variant="subtitle1">
                            {bot.name}
                          </Typography>
                          <Chip 
                            label={getBotStatus(bot)}
                            color={getBotStatus(bot) === 'online' ? 'success' : 'default'}
                            size="small"
                          />
                          <Chip 
                            label={bot.status}
                            color={bot.status === 'active' ? 'success' : 'warning'}
                            size="small"
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {bot.description || 'No description'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Token: {bot.bot_token ? `${bot.bot_token.substring(0, 8)}...` : 'Not generated'}
                          </Typography>
                        </Box>
                      }
                    />
                    <Button
                      variant="contained"
                      startIcon={<CodeIcon />}
                      onClick={() => {
                        setShowBotCodeDialog(false);
                        openCodeEditor(bot);
                      }}
                    >
                      Edit Code
                    </Button>
                  </ListItem>
                  {index < userBots.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowBotCodeDialog(false)}>
            Close
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setShowBotCodeDialog(false);
              createNewBot();
            }}
          >
            Create New Bot
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Chat;
