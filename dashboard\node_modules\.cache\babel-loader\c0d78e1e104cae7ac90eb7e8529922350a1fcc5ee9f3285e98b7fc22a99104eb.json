{"ast": null, "code": "export const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\nexport const epsilon = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const tau = 2 * pi;\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}", "map": {"version": 3, "names": ["abs", "Math", "atan2", "cos", "max", "min", "sin", "sqrt", "epsilon", "pi", "PI", "halfPi", "tau", "acos", "x", "asin"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-shape/src/math.js"], "sourcesContent": ["export const abs = Math.abs;\nexport const atan2 = Math.atan2;\nexport const cos = Math.cos;\nexport const max = Math.max;\nexport const min = Math.min;\nexport const sin = Math.sin;\nexport const sqrt = Math.sqrt;\n\nexport const epsilon = 1e-12;\nexport const pi = Math.PI;\nexport const halfPi = pi / 2;\nexport const tau = 2 * pi;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x >= 1 ? halfPi : x <= -1 ? -halfPi : Math.asin(x);\n}\n"], "mappings": "AAAA,OAAO,MAAMA,GAAG,GAAGC,IAAI,CAACD,GAAG;AAC3B,OAAO,MAAME,KAAK,GAAGD,IAAI,CAACC,KAAK;AAC/B,OAAO,MAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG;AAC3B,OAAO,MAAMC,GAAG,GAAGH,IAAI,CAACG,GAAG;AAC3B,OAAO,MAAMC,GAAG,GAAGJ,IAAI,CAACI,GAAG;AAC3B,OAAO,MAAMC,GAAG,GAAGL,IAAI,CAACK,GAAG;AAC3B,OAAO,MAAMC,IAAI,GAAGN,IAAI,CAACM,IAAI;AAE7B,OAAO,MAAMC,OAAO,GAAG,KAAK;AAC5B,OAAO,MAAMC,EAAE,GAAGR,IAAI,CAACS,EAAE;AACzB,OAAO,MAAMC,MAAM,GAAGF,EAAE,GAAG,CAAC;AAC5B,OAAO,MAAMG,GAAG,GAAG,CAAC,GAAGH,EAAE;AAEzB,OAAO,SAASI,IAAIA,CAACC,CAAC,EAAE;EACtB,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAGL,EAAE,GAAGR,IAAI,CAACY,IAAI,CAACC,CAAC,CAAC;AAC/C;AAEA,OAAO,SAASC,IAAIA,CAACD,CAAC,EAAE;EACtB,OAAOA,CAAC,IAAI,CAAC,GAAGH,MAAM,GAAGG,CAAC,IAAI,CAAC,CAAC,GAAG,CAACH,MAAM,GAAGV,IAAI,CAACc,IAAI,CAACD,CAAC,CAAC;AAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}