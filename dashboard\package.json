{"name": "bot-discord-dashboard", "version": "1.0.0", "description": "Developer Dashboard for Bot Discord Platform", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.19", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "acorn": "^8.14.1", "acorn-walk": "^8.3.4", "ajv": "^8.17.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "diff": "^5.2.0", "js-beautify": "^1.15.4", "monaco-editor": "^0.44.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-resizable-panels": "^0.0.55", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-split": "^2.0.14", "recharts": "^2.8.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3002"}