import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Fab,
  Menu,
  MenuItem,
  Avatar,
  LinearProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Code as CodeIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  SmartToy as BotIcon,
} from '@mui/icons-material';
import { applicationsAPI, handleApiError, handleApiSuccess } from '../../services/api';
import BotCodeEditor from '../Bot/BotCodeEditor';
import toast from 'react-hot-toast';

const Applications = () => {
  const navigate = useNavigate();
  const [applications, setApplications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [selectedApp, setSelectedApp] = useState(null);
  const [newApp, setNewApp] = useState({
    name: '',
    description: '',
  });

  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const response = await applicationsAPI.getAll();
      setApplications(response.data.applications);
    } catch (error) {
      handleApiError(error, 'Failed to load applications');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApp = async () => {
    try {
      if (!newApp.name.trim()) {
        toast.error('Application name is required');
        return;
      }

      await applicationsAPI.create(newApp);
      handleApiSuccess('Application created successfully');
      setCreateDialogOpen(false);
      setNewApp({ name: '', description: '' });
      loadApplications();
    } catch (error) {
      handleApiError(error, 'Failed to create application');
    }
  };

  const handleDeleteApp = async (appId) => {
    try {
      await applicationsAPI.delete(appId);
      handleApiSuccess('Application deleted successfully');
      loadApplications();
    } catch (error) {
      handleApiError(error, 'Failed to delete application');
    }
  };

  const handleMenuClick = (event, app) => {
    setMenuAnchor(event.currentTarget);
    setSelectedApp(app);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setSelectedApp(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Applications
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
        <Typography variant="h4" fontWeight="bold">
          Applications
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          sx={{
            background: 'linear-gradient(45deg, #5865f2 30%, #57f287 90%)',
            '&:hover': {
              background: 'linear-gradient(45deg, #4752c4 30%, #3ba55c 90%)',
            },
          }}
        >
          Create Application
        </Button>
      </Box>

      {applications.length === 0 ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 80,
                height: 80,
                mx: 'auto',
                mb: 2,
              }}
            >
              <BotIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography variant="h5" gutterBottom>
              No Applications Yet
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              Create your first bot application to get started with the platform.
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              size="large"
            >
              Create Your First Bot
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {applications.map((app) => (
            <Grid item xs={12} sm={6} md={4} key={app.id}>
              <Card className="hover-card" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                    <Avatar sx={{ bgcolor: 'primary.main', width: 48, height: 48 }}>
                      <BotIcon />
                    </Avatar>
                    <Box>
                      <Chip
                        label={app.status}
                        color={getStatusColor(app.status)}
                        size="small"
                      />
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuClick(e, app)}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </Box>
                  </Box>
                  
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    {app.name}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    {app.description || 'No description provided'}
                  </Typography>
                  
                  <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="caption" color="text.secondary">
                      Created: {new Date(app.created_at).toLocaleDateString()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Token: {app.bot_token ? '••••••••' : 'Not generated'}
                    </Typography>
                  </Box>
                </CardContent>
                
                <CardActions>
                  <Button
                    size="small"
                    startIcon={<CodeIcon />}
                    onClick={() => navigate(`/applications/${app.id}/code`)}
                  >
                    Code
                  </Button>
                  <Button
                    size="small"
                    startIcon={<PlayIcon />}
                    onClick={() => navigate(`/applications/${app.id}/runtime`)}
                  >
                    Runtime
                  </Button>
                  <Button
                    size="small"
                    onClick={() => navigate(`/applications/${app.id}`)}
                  >
                    Manage
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create Application Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Create New Application</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Application Name"
            fullWidth
            variant="outlined"
            value={newApp.name}
            onChange={(e) => setNewApp({ ...newApp, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newApp.description}
            onChange={(e) => setNewApp({ ...newApp, description: e.target.value })}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleCreateApp}
            variant="contained"
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => {
          navigate(`/applications/${selectedApp?.id}`);
          handleMenuClose();
        }}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={() => {
          navigate(`/applications/${selectedApp?.id}/code`);
          handleMenuClose();
        }}>
          <CodeIcon sx={{ mr: 1 }} />
          Code Editor
        </MenuItem>
        <MenuItem onClick={() => {
          navigate(`/applications/${selectedApp?.id}/runtime`);
          handleMenuClose();
        }}>
          <PlayIcon sx={{ mr: 1 }} />
          Runtime
        </MenuItem>
        <MenuItem
          onClick={() => {
            if (selectedApp && window.confirm('Are you sure you want to delete this application?')) {
              handleDeleteApp(selectedApp.id);
            }
            handleMenuClose();
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default Applications;
