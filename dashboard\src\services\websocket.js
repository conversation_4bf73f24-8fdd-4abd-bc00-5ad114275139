/**
 * WebSocket service for real-time chat functionality
 */

class WebSocketService {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.eventHandlers = new Map();
        this.messageQueue = [];
        this.heartbeatInterval = null;
        this.lastHeartbeat = null;
    }

    /**
     * Connect to WebSocket server
     * @param {string} url - WebSocket URL
     * @param {string} token - Authentication token
     */
    connect(url = 'ws://localhost:3003/chat', token = null) {
        try {
            // Prevent multiple connections
            if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
                console.log('⚠️ WebSocket already connecting, skipping...');
                return;
            }

            if (this.isConnected) {
                console.log('⚠️ WebSocket already connected, skipping...');
                return;
            }

            console.log('🔌 Connecting to WebSocket:', url);

            this.ws = new WebSocket(url);
            
            this.ws.onopen = () => {
                console.log('✅ WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;

                // Send authentication if token provided
                if (token) {
                    this.authenticate(token);
                }

                // Send queued messages
                this.flushMessageQueue();

                this.emit('connected');
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    console.log('📨 WebSocket message:', data);

                    // Handle different message types for chat server
                    switch (data.type) {
                        case 'hello':
                            this.emit('connected');
                            break;
                        case 'authenticated':
                            this.emit('authenticated', data.data);
                            break;
                        case 'auth_error':
                            this.emit('authError', data.data);
                            break;
                        case 'channel_joined':
                            this.emit('channelJoined', data.data);
                            break;
                        case 'message_create':
                            this.emit('messageCreate', data.data);
                            break;
                        case 'pong':
                            this.lastHeartbeat = Date.now();
                            break;
                        case 'error':
                            this.emit('error', data.data);
                            break;
                        default:
                            console.log('Unknown WebSocket message type:', data.type);
                    }
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log('🔌 WebSocket disconnected:', event.code, event.reason);
                this.isConnected = false;
                this.stopHeartbeat();
                
                this.emit('disconnected', { code: event.code, reason: event.reason });
                
                // Attempt to reconnect
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.scheduleReconnect();
                }
            };

            this.ws.onerror = (error) => {
                console.error('❌ WebSocket error:', error);
                this.emit('error', error);
            };

        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            this.emit('error', error);
        }
    }

    /**
     * Disconnect from WebSocket
     */
    disconnect() {
        if (this.ws) {
            this.ws.close(1000, 'Client disconnect');
            this.ws = null;
        }
        this.isConnected = false;
        this.stopHeartbeat();
    }

    /**
     * Send authentication message
     * @param {string} token - Authentication token
     */
    authenticate(token) {
        this.send({
            type: 'authenticate',
            data: {
                token: token
            }
        });
    }

    /**
     * Send message to WebSocket
     * @param {Object} data - Message data
     */
    send(data) {
        if (this.isConnected && this.ws) {
            try {
                this.ws.send(JSON.stringify(data));
            } catch (error) {
                console.error('Error sending WebSocket message:', error);
            }
        } else {
            // Queue message for later
            this.messageQueue.push(data);
        }
    }

    /**
     * Join a channel
     * @param {string} serverId - Server ID
     * @param {string} channelId - Channel ID
     */
    joinChannel(serverId, channelId) {
        this.send({
            type: 'join_channel',
            data: {
                serverId,
                channelId
            }
        });
    }

    /**
     * Send a chat message
     * @param {string} serverId - Server ID
     * @param {string} channelId - Channel ID
     * @param {string} content - Message content
     */
    sendChatMessage(serverId, channelId, content) {
        this.send({
            type: 'send_message',
            data: {
                serverId,
                channelId,
                content
            }
        });
    }

    /**
     * Send ping to keep connection alive
     */
    ping() {
        this.send({
            type: 'ping',
            data: {
                timestamp: Date.now()
            }
        });
    }

    /**
     * Handle dispatch messages (events)
     * @param {Object} data - Message data
     */
    handleDispatch(data) {
        const { t: eventType, d: eventData } = data;
        
        switch (eventType) {
            case 'MESSAGE_CREATE':
                this.emit('messageCreate', eventData);
                break;
            case 'MESSAGE_UPDATE':
                this.emit('messageUpdate', eventData);
                break;
            case 'MESSAGE_DELETE':
                this.emit('messageDelete', eventData);
                break;
            case 'CHANNEL_CREATE':
                this.emit('channelCreate', eventData);
                break;
            case 'CHANNEL_UPDATE':
                this.emit('channelUpdate', eventData);
                break;
            case 'CHANNEL_DELETE':
                this.emit('channelDelete', eventData);
                break;
            case 'GUILD_CREATE':
                this.emit('guildCreate', eventData);
                break;
            case 'GUILD_UPDATE':
                this.emit('guildUpdate', eventData);
                break;
            case 'GUILD_DELETE':
                this.emit('guildDelete', eventData);
                break;
            case 'READY':
                this.emit('ready', eventData);
                break;
            default:
                this.emit('event', { type: eventType, data: eventData });
        }
    }

    /**
     * Handle heartbeat
     * @param {Object} data - Message data
     */
    handleHeartbeat(data) {
        // Send heartbeat response
        this.send({
            op: 1,
            d: this.lastHeartbeat
        });
    }

    /**
     * Handle hello message
     * @param {Object} data - Message data
     */
    handleHello(data) {
        const { heartbeat_interval } = data.d;
        this.heartbeatInterval = heartbeat_interval;
        console.log('💓 Heartbeat interval:', heartbeat_interval);
    }

    /**
     * Handle heartbeat acknowledgment
     * @param {Object} data - Message data
     */
    handleHeartbeatAck(data) {
        this.lastHeartbeat = Date.now();
    }

    /**
     * Handle invalid session
     * @param {Object} data - Message data
     */
    handleInvalidSession(data) {
        console.error('Invalid WebSocket session');
        this.emit('invalidSession', data);
    }

    /**
     * Start heartbeat timer
     */
    startHeartbeat() {
        if (this.heartbeatInterval) {
            this.heartbeatTimer = setInterval(() => {
                this.send({
                    op: 1,
                    d: this.lastHeartbeat
                });
            }, this.heartbeatInterval);
        }
    }

    /**
     * Stop heartbeat timer
     */
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.connect();
            }
        }, delay);
    }

    /**
     * Flush queued messages
     */
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }

    /**
     * Register event handler
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Remove event handler
     * @param {string} event - Event name
     * @param {Function} handler - Event handler
     */
    off(event, handler) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Emit event to handlers
     * @param {string} event - Event name
     * @param {*} data - Event data
     */
    emit(event, data) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error(`Error in WebSocket event handler for ${event}:`, error);
                }
            });
        }
    }

    /**
     * Get connection status
     * @returns {boolean} - Connection status
     */
    isConnectedToServer() {
        return this.isConnected;
    }

    /**
     * Get connection statistics
     * @returns {Object} - Connection stats
     */
    getStats() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            lastHeartbeat: this.lastHeartbeat,
            queuedMessages: this.messageQueue.length
        };
    }
}

// Create singleton instance
const websocketService = new WebSocketService();

export default websocketService;
