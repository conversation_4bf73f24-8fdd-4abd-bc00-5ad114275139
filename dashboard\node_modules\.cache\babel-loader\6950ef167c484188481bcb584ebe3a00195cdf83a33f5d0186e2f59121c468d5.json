{"ast": null, "code": "var baseFindIndex = require('./_baseFindIndex'),\n  baseIsNaN = require('./_baseIsNaN'),\n  strictIndexOf = require('./_strictIndexOf');\n\n/**\n * The base implementation of `_.indexOf` without `fromIndex` bounds checks.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOf(array, value, fromIndex) {\n  return value === value ? strictIndexOf(array, value, fromIndex) : baseFindIndex(array, baseIsNaN, fromIndex);\n}\nmodule.exports = baseIndexOf;", "map": {"version": 3, "names": ["baseFindIndex", "require", "baseIsNaN", "strictIndexOf", "baseIndexOf", "array", "value", "fromIndex", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/lodash/_baseIndexOf.js"], "sourcesContent": ["var baseFindIndex = require('./_baseFindIndex'),\n    baseIsNaN = require('./_baseIsNaN'),\n    strictIndexOf = require('./_strictIndexOf');\n\n/**\n * The base implementation of `_.indexOf` without `fromIndex` bounds checks.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOf(array, value, fromIndex) {\n  return value === value\n    ? strictIndexOf(array, value, fromIndex)\n    : baseFindIndex(array, baseIsNaN, fromIndex);\n}\n\nmodule.exports = baseIndexOf;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAGC,OAAO,CAAC,kBAAkB,CAAC;EAC3CC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;EACnCE,aAAa,GAAGF,OAAO,CAAC,kBAAkB,CAAC;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5C,OAAOD,KAAK,KAAKA,KAAK,GAClBH,aAAa,CAACE,KAAK,EAAEC,KAAK,EAAEC,SAAS,CAAC,GACtCP,aAAa,CAACK,KAAK,EAAEH,SAAS,EAAEK,SAAS,CAAC;AAChD;AAEAC,MAAM,CAACC,OAAO,GAAGL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}