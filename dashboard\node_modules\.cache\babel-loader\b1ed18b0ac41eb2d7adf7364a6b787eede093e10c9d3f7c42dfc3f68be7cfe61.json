{"ast": null, "code": "/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n  constructor() {\n    this.ws = null;\n    this.isConnected = false;\n    this.reconnectAttempts = 0;\n    this.maxReconnectAttempts = 5;\n    this.reconnectDelay = 1000;\n    this.eventHandlers = new Map();\n    this.messageQueue = [];\n    this.heartbeatInterval = null;\n    this.lastHeartbeat = null;\n  }\n\n  /**\n   * Connect to WebSocket server\n   * @param {string} url - WebSocket URL\n   * @param {string} token - Authentication token\n   */\n  connect(url = 'ws://localhost:3003/chat', token = null) {\n    try {\n      console.log('🔌 Connecting to WebSocket:', url);\n      this.ws = new WebSocket(url);\n      this.ws.onopen = () => {\n        console.log('✅ WebSocket connected');\n        this.isConnected = true;\n        this.reconnectAttempts = 0;\n\n        // Send authentication if token provided\n        if (token) {\n          this.authenticate(token);\n        }\n\n        // Send queued messages\n        this.flushMessageQueue();\n        this.emit('connected');\n      };\n      this.ws.onmessage = event => {\n        try {\n          const data = JSON.parse(event.data);\n          console.log('📨 WebSocket message:', data);\n\n          // Handle different message types for chat server\n          switch (data.type) {\n            case 'hello':\n              this.emit('connected');\n              break;\n            case 'authenticated':\n              this.emit('authenticated', data.data);\n              break;\n            case 'auth_error':\n              this.emit('authError', data.data);\n              break;\n            case 'channel_joined':\n              this.emit('channelJoined', data.data);\n              break;\n            case 'message_create':\n              this.emit('messageCreate', data.data);\n              break;\n            case 'pong':\n              this.lastHeartbeat = Date.now();\n              break;\n            case 'error':\n              this.emit('error', data.data);\n              break;\n            default:\n              console.log('Unknown WebSocket message type:', data.type);\n          }\n        } catch (error) {\n          console.error('Error parsing WebSocket message:', error);\n        }\n      };\n      this.ws.onclose = event => {\n        console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n        this.isConnected = false;\n        this.stopHeartbeat();\n        this.emit('disconnected', {\n          code: event.code,\n          reason: event.reason\n        });\n\n        // Attempt to reconnect\n        if (this.reconnectAttempts < this.maxReconnectAttempts) {\n          this.scheduleReconnect();\n        }\n      };\n      this.ws.onerror = error => {\n        console.error('❌ WebSocket error:', error);\n        this.emit('error', error);\n      };\n    } catch (error) {\n      console.error('Failed to connect to WebSocket:', error);\n      this.emit('error', error);\n    }\n  }\n\n  /**\n   * Disconnect from WebSocket\n   */\n  disconnect() {\n    if (this.ws) {\n      this.ws.close(1000, 'Client disconnect');\n      this.ws = null;\n    }\n    this.isConnected = false;\n    this.stopHeartbeat();\n  }\n\n  /**\n   * Send authentication message\n   * @param {string} token - Authentication token\n   */\n  authenticate(token) {\n    this.send({\n      type: 'authenticate',\n      data: {\n        token: token\n      }\n    });\n  }\n\n  /**\n   * Send message to WebSocket\n   * @param {Object} data - Message data\n   */\n  send(data) {\n    if (this.isConnected && this.ws) {\n      try {\n        this.ws.send(JSON.stringify(data));\n      } catch (error) {\n        console.error('Error sending WebSocket message:', error);\n      }\n    } else {\n      // Queue message for later\n      this.messageQueue.push(data);\n    }\n  }\n\n  /**\n   * Handle dispatch messages (events)\n   * @param {Object} data - Message data\n   */\n  handleDispatch(data) {\n    const {\n      t: eventType,\n      d: eventData\n    } = data;\n    switch (eventType) {\n      case 'MESSAGE_CREATE':\n        this.emit('messageCreate', eventData);\n        break;\n      case 'MESSAGE_UPDATE':\n        this.emit('messageUpdate', eventData);\n        break;\n      case 'MESSAGE_DELETE':\n        this.emit('messageDelete', eventData);\n        break;\n      case 'CHANNEL_CREATE':\n        this.emit('channelCreate', eventData);\n        break;\n      case 'CHANNEL_UPDATE':\n        this.emit('channelUpdate', eventData);\n        break;\n      case 'CHANNEL_DELETE':\n        this.emit('channelDelete', eventData);\n        break;\n      case 'GUILD_CREATE':\n        this.emit('guildCreate', eventData);\n        break;\n      case 'GUILD_UPDATE':\n        this.emit('guildUpdate', eventData);\n        break;\n      case 'GUILD_DELETE':\n        this.emit('guildDelete', eventData);\n        break;\n      case 'READY':\n        this.emit('ready', eventData);\n        break;\n      default:\n        this.emit('event', {\n          type: eventType,\n          data: eventData\n        });\n    }\n  }\n\n  /**\n   * Handle heartbeat\n   * @param {Object} data - Message data\n   */\n  handleHeartbeat(data) {\n    // Send heartbeat response\n    this.send({\n      op: 1,\n      d: this.lastHeartbeat\n    });\n  }\n\n  /**\n   * Handle hello message\n   * @param {Object} data - Message data\n   */\n  handleHello(data) {\n    const {\n      heartbeat_interval\n    } = data.d;\n    this.heartbeatInterval = heartbeat_interval;\n    console.log('💓 Heartbeat interval:', heartbeat_interval);\n  }\n\n  /**\n   * Handle heartbeat acknowledgment\n   * @param {Object} data - Message data\n   */\n  handleHeartbeatAck(data) {\n    this.lastHeartbeat = Date.now();\n  }\n\n  /**\n   * Handle invalid session\n   * @param {Object} data - Message data\n   */\n  handleInvalidSession(data) {\n    console.error('Invalid WebSocket session');\n    this.emit('invalidSession', data);\n  }\n\n  /**\n   * Start heartbeat timer\n   */\n  startHeartbeat() {\n    if (this.heartbeatInterval) {\n      this.heartbeatTimer = setInterval(() => {\n        this.send({\n          op: 1,\n          d: this.lastHeartbeat\n        });\n      }, this.heartbeatInterval);\n    }\n  }\n\n  /**\n   * Stop heartbeat timer\n   */\n  stopHeartbeat() {\n    if (this.heartbeatTimer) {\n      clearInterval(this.heartbeatTimer);\n      this.heartbeatTimer = null;\n    }\n  }\n\n  /**\n   * Schedule reconnection attempt\n   */\n  scheduleReconnect() {\n    this.reconnectAttempts++;\n    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n    console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n    setTimeout(() => {\n      if (!this.isConnected) {\n        this.connect();\n      }\n    }, delay);\n  }\n\n  /**\n   * Flush queued messages\n   */\n  flushMessageQueue() {\n    while (this.messageQueue.length > 0) {\n      const message = this.messageQueue.shift();\n      this.send(message);\n    }\n  }\n\n  /**\n   * Register event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  on(event, handler) {\n    if (!this.eventHandlers.has(event)) {\n      this.eventHandlers.set(event, []);\n    }\n    this.eventHandlers.get(event).push(handler);\n  }\n\n  /**\n   * Remove event handler\n   * @param {string} event - Event name\n   * @param {Function} handler - Event handler\n   */\n  off(event, handler) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      const index = handlers.indexOf(handler);\n      if (index > -1) {\n        handlers.splice(index, 1);\n      }\n    }\n  }\n\n  /**\n   * Emit event to handlers\n   * @param {string} event - Event name\n   * @param {*} data - Event data\n   */\n  emit(event, data) {\n    const handlers = this.eventHandlers.get(event);\n    if (handlers) {\n      handlers.forEach(handler => {\n        try {\n          handler(data);\n        } catch (error) {\n          console.error(`Error in WebSocket event handler for ${event}:`, error);\n        }\n      });\n    }\n  }\n\n  /**\n   * Get connection status\n   * @returns {boolean} - Connection status\n   */\n  isConnectedToServer() {\n    return this.isConnected;\n  }\n\n  /**\n   * Get connection statistics\n   * @returns {Object} - Connection stats\n   */\n  getStats() {\n    return {\n      isConnected: this.isConnected,\n      reconnectAttempts: this.reconnectAttempts,\n      lastHeartbeat: this.lastHeartbeat,\n      queuedMessages: this.messageQueue.length\n    };\n  }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\nexport default websocketService;", "map": {"version": 3, "names": ["WebSocketService", "constructor", "ws", "isConnected", "reconnectAttempts", "maxReconnectAttempts", "reconnectDelay", "eventHandlers", "Map", "messageQueue", "heartbeatInterval", "lastHeartbeat", "connect", "url", "token", "console", "log", "WebSocket", "onopen", "authenticate", "flushMessageQueue", "emit", "onmessage", "event", "data", "JSON", "parse", "type", "Date", "now", "error", "onclose", "code", "reason", "stopHeartbeat", "scheduleReconnect", "onerror", "disconnect", "close", "send", "stringify", "push", "handleDispatch", "t", "eventType", "d", "eventData", "handleHeartbeat", "op", "handleHello", "heartbeat_interval", "handleHeartbeatAck", "handleInvalidSession", "startHeartbeat", "heartbeatTimer", "setInterval", "clearInterval", "delay", "Math", "pow", "setTimeout", "length", "message", "shift", "on", "handler", "has", "set", "get", "off", "handlers", "index", "indexOf", "splice", "for<PERSON>ach", "isConnectedToServer", "getStats", "queuedMessages", "websocketService"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/src/services/websocket.js"], "sourcesContent": ["/**\n * WebSocket service for real-time chat functionality\n */\n\nclass WebSocketService {\n    constructor() {\n        this.ws = null;\n        this.isConnected = false;\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.reconnectDelay = 1000;\n        this.eventHandlers = new Map();\n        this.messageQueue = [];\n        this.heartbeatInterval = null;\n        this.lastHeartbeat = null;\n    }\n\n    /**\n     * Connect to WebSocket server\n     * @param {string} url - WebSocket URL\n     * @param {string} token - Authentication token\n     */\n    connect(url = 'ws://localhost:3003/chat', token = null) {\n        try {\n            console.log('🔌 Connecting to WebSocket:', url);\n            \n            this.ws = new WebSocket(url);\n            \n            this.ws.onopen = () => {\n                console.log('✅ WebSocket connected');\n                this.isConnected = true;\n                this.reconnectAttempts = 0;\n\n                // Send authentication if token provided\n                if (token) {\n                    this.authenticate(token);\n                }\n\n                // Send queued messages\n                this.flushMessageQueue();\n\n                this.emit('connected');\n            };\n\n            this.ws.onmessage = (event) => {\n                try {\n                    const data = JSON.parse(event.data);\n                    console.log('📨 WebSocket message:', data);\n\n                    // Handle different message types for chat server\n                    switch (data.type) {\n                        case 'hello':\n                            this.emit('connected');\n                            break;\n                        case 'authenticated':\n                            this.emit('authenticated', data.data);\n                            break;\n                        case 'auth_error':\n                            this.emit('authError', data.data);\n                            break;\n                        case 'channel_joined':\n                            this.emit('channelJoined', data.data);\n                            break;\n                        case 'message_create':\n                            this.emit('messageCreate', data.data);\n                            break;\n                        case 'pong':\n                            this.lastHeartbeat = Date.now();\n                            break;\n                        case 'error':\n                            this.emit('error', data.data);\n                            break;\n                        default:\n                            console.log('Unknown WebSocket message type:', data.type);\n                    }\n                } catch (error) {\n                    console.error('Error parsing WebSocket message:', error);\n                }\n            };\n\n            this.ws.onclose = (event) => {\n                console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n                this.isConnected = false;\n                this.stopHeartbeat();\n                \n                this.emit('disconnected', { code: event.code, reason: event.reason });\n                \n                // Attempt to reconnect\n                if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                    this.scheduleReconnect();\n                }\n            };\n\n            this.ws.onerror = (error) => {\n                console.error('❌ WebSocket error:', error);\n                this.emit('error', error);\n            };\n\n        } catch (error) {\n            console.error('Failed to connect to WebSocket:', error);\n            this.emit('error', error);\n        }\n    }\n\n    /**\n     * Disconnect from WebSocket\n     */\n    disconnect() {\n        if (this.ws) {\n            this.ws.close(1000, 'Client disconnect');\n            this.ws = null;\n        }\n        this.isConnected = false;\n        this.stopHeartbeat();\n    }\n\n    /**\n     * Send authentication message\n     * @param {string} token - Authentication token\n     */\n    authenticate(token) {\n        this.send({\n            type: 'authenticate',\n            data: {\n                token: token\n            }\n        });\n    }\n\n    /**\n     * Send message to WebSocket\n     * @param {Object} data - Message data\n     */\n    send(data) {\n        if (this.isConnected && this.ws) {\n            try {\n                this.ws.send(JSON.stringify(data));\n            } catch (error) {\n                console.error('Error sending WebSocket message:', error);\n            }\n        } else {\n            // Queue message for later\n            this.messageQueue.push(data);\n        }\n    }\n\n    /**\n     * Handle dispatch messages (events)\n     * @param {Object} data - Message data\n     */\n    handleDispatch(data) {\n        const { t: eventType, d: eventData } = data;\n        \n        switch (eventType) {\n            case 'MESSAGE_CREATE':\n                this.emit('messageCreate', eventData);\n                break;\n            case 'MESSAGE_UPDATE':\n                this.emit('messageUpdate', eventData);\n                break;\n            case 'MESSAGE_DELETE':\n                this.emit('messageDelete', eventData);\n                break;\n            case 'CHANNEL_CREATE':\n                this.emit('channelCreate', eventData);\n                break;\n            case 'CHANNEL_UPDATE':\n                this.emit('channelUpdate', eventData);\n                break;\n            case 'CHANNEL_DELETE':\n                this.emit('channelDelete', eventData);\n                break;\n            case 'GUILD_CREATE':\n                this.emit('guildCreate', eventData);\n                break;\n            case 'GUILD_UPDATE':\n                this.emit('guildUpdate', eventData);\n                break;\n            case 'GUILD_DELETE':\n                this.emit('guildDelete', eventData);\n                break;\n            case 'READY':\n                this.emit('ready', eventData);\n                break;\n            default:\n                this.emit('event', { type: eventType, data: eventData });\n        }\n    }\n\n    /**\n     * Handle heartbeat\n     * @param {Object} data - Message data\n     */\n    handleHeartbeat(data) {\n        // Send heartbeat response\n        this.send({\n            op: 1,\n            d: this.lastHeartbeat\n        });\n    }\n\n    /**\n     * Handle hello message\n     * @param {Object} data - Message data\n     */\n    handleHello(data) {\n        const { heartbeat_interval } = data.d;\n        this.heartbeatInterval = heartbeat_interval;\n        console.log('💓 Heartbeat interval:', heartbeat_interval);\n    }\n\n    /**\n     * Handle heartbeat acknowledgment\n     * @param {Object} data - Message data\n     */\n    handleHeartbeatAck(data) {\n        this.lastHeartbeat = Date.now();\n    }\n\n    /**\n     * Handle invalid session\n     * @param {Object} data - Message data\n     */\n    handleInvalidSession(data) {\n        console.error('Invalid WebSocket session');\n        this.emit('invalidSession', data);\n    }\n\n    /**\n     * Start heartbeat timer\n     */\n    startHeartbeat() {\n        if (this.heartbeatInterval) {\n            this.heartbeatTimer = setInterval(() => {\n                this.send({\n                    op: 1,\n                    d: this.lastHeartbeat\n                });\n            }, this.heartbeatInterval);\n        }\n    }\n\n    /**\n     * Stop heartbeat timer\n     */\n    stopHeartbeat() {\n        if (this.heartbeatTimer) {\n            clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = null;\n        }\n    }\n\n    /**\n     * Schedule reconnection attempt\n     */\n    scheduleReconnect() {\n        this.reconnectAttempts++;\n        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);\n        \n        console.log(`🔄 Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);\n        \n        setTimeout(() => {\n            if (!this.isConnected) {\n                this.connect();\n            }\n        }, delay);\n    }\n\n    /**\n     * Flush queued messages\n     */\n    flushMessageQueue() {\n        while (this.messageQueue.length > 0) {\n            const message = this.messageQueue.shift();\n            this.send(message);\n        }\n    }\n\n    /**\n     * Register event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    on(event, handler) {\n        if (!this.eventHandlers.has(event)) {\n            this.eventHandlers.set(event, []);\n        }\n        this.eventHandlers.get(event).push(handler);\n    }\n\n    /**\n     * Remove event handler\n     * @param {string} event - Event name\n     * @param {Function} handler - Event handler\n     */\n    off(event, handler) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            const index = handlers.indexOf(handler);\n            if (index > -1) {\n                handlers.splice(index, 1);\n            }\n        }\n    }\n\n    /**\n     * Emit event to handlers\n     * @param {string} event - Event name\n     * @param {*} data - Event data\n     */\n    emit(event, data) {\n        const handlers = this.eventHandlers.get(event);\n        if (handlers) {\n            handlers.forEach(handler => {\n                try {\n                    handler(data);\n                } catch (error) {\n                    console.error(`Error in WebSocket event handler for ${event}:`, error);\n                }\n            });\n        }\n    }\n\n    /**\n     * Get connection status\n     * @returns {boolean} - Connection status\n     */\n    isConnectedToServer() {\n        return this.isConnected;\n    }\n\n    /**\n     * Get connection statistics\n     * @returns {Object} - Connection stats\n     */\n    getStats() {\n        return {\n            isConnected: this.isConnected,\n            reconnectAttempts: this.reconnectAttempts,\n            lastHeartbeat: this.lastHeartbeat,\n            queuedMessages: this.messageQueue.length\n        };\n    }\n}\n\n// Create singleton instance\nconst websocketService = new WebSocketService();\n\nexport default websocketService;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,gBAAgB,CAAC;EACnBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,EAAE,GAAG,IAAI;IACd,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAC7B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;;EAEA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAACC,GAAG,GAAG,0BAA0B,EAAEC,KAAK,GAAG,IAAI,EAAE;IACpD,IAAI;MACAC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEH,GAAG,CAAC;MAE/C,IAAI,CAACX,EAAE,GAAG,IAAIe,SAAS,CAACJ,GAAG,CAAC;MAE5B,IAAI,CAACX,EAAE,CAACgB,MAAM,GAAG,MAAM;QACnBH,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC,IAAI,CAACb,WAAW,GAAG,IAAI;QACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;;QAE1B;QACA,IAAIU,KAAK,EAAE;UACP,IAAI,CAACK,YAAY,CAACL,KAAK,CAAC;QAC5B;;QAEA;QACA,IAAI,CAACM,iBAAiB,CAAC,CAAC;QAExB,IAAI,CAACC,IAAI,CAAC,WAAW,CAAC;MAC1B,CAAC;MAED,IAAI,CAACnB,EAAE,CAACoB,SAAS,GAAIC,KAAK,IAAK;QAC3B,IAAI;UACA,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACC,IAAI,CAAC;UACnCT,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEQ,IAAI,CAAC;;UAE1C;UACA,QAAQA,IAAI,CAACG,IAAI;YACb,KAAK,OAAO;cACR,IAAI,CAACN,IAAI,CAAC,WAAW,CAAC;cACtB;YACJ,KAAK,eAAe;cAChB,IAAI,CAACA,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,YAAY;cACb,IAAI,CAACH,IAAI,CAAC,WAAW,EAAEG,IAAI,CAACA,IAAI,CAAC;cACjC;YACJ,KAAK,gBAAgB;cACjB,IAAI,CAACH,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,gBAAgB;cACjB,IAAI,CAACH,IAAI,CAAC,eAAe,EAAEG,IAAI,CAACA,IAAI,CAAC;cACrC;YACJ,KAAK,MAAM;cACP,IAAI,CAACb,aAAa,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC;cAC/B;YACJ,KAAK,OAAO;cACR,IAAI,CAACR,IAAI,CAAC,OAAO,EAAEG,IAAI,CAACA,IAAI,CAAC;cAC7B;YACJ;cACIT,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,IAAI,CAACG,IAAI,CAAC;UACjE;QACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;UACZf,OAAO,CAACe,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC5D;MACJ,CAAC;MAED,IAAI,CAAC5B,EAAE,CAAC6B,OAAO,GAAIR,KAAK,IAAK;QACzBR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,KAAK,CAACS,IAAI,EAAET,KAAK,CAACU,MAAM,CAAC;QACnE,IAAI,CAAC9B,WAAW,GAAG,KAAK;QACxB,IAAI,CAAC+B,aAAa,CAAC,CAAC;QAEpB,IAAI,CAACb,IAAI,CAAC,cAAc,EAAE;UAAEW,IAAI,EAAET,KAAK,CAACS,IAAI;UAAEC,MAAM,EAAEV,KAAK,CAACU;QAAO,CAAC,CAAC;;QAErE;QACA,IAAI,IAAI,CAAC7B,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,EAAE;UACpD,IAAI,CAAC8B,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MAED,IAAI,CAACjC,EAAE,CAACkC,OAAO,GAAIN,KAAK,IAAK;QACzBf,OAAO,CAACe,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,IAAI,CAACT,IAAI,CAAC,OAAO,EAAES,KAAK,CAAC;MAC7B,CAAC;IAEL,CAAC,CAAC,OAAOA,KAAK,EAAE;MACZf,OAAO,CAACe,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,IAAI,CAACT,IAAI,CAAC,OAAO,EAAES,KAAK,CAAC;IAC7B;EACJ;;EAEA;AACJ;AACA;EACIO,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACnC,EAAE,EAAE;MACT,IAAI,CAACA,EAAE,CAACoC,KAAK,CAAC,IAAI,EAAE,mBAAmB,CAAC;MACxC,IAAI,CAACpC,EAAE,GAAG,IAAI;IAClB;IACA,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC+B,aAAa,CAAC,CAAC;EACxB;;EAEA;AACJ;AACA;AACA;EACIf,YAAYA,CAACL,KAAK,EAAE;IAChB,IAAI,CAACyB,IAAI,CAAC;MACNZ,IAAI,EAAE,cAAc;MACpBH,IAAI,EAAE;QACFV,KAAK,EAAEA;MACX;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIyB,IAAIA,CAACf,IAAI,EAAE;IACP,IAAI,IAAI,CAACrB,WAAW,IAAI,IAAI,CAACD,EAAE,EAAE;MAC7B,IAAI;QACA,IAAI,CAACA,EAAE,CAACqC,IAAI,CAACd,IAAI,CAACe,SAAS,CAAChB,IAAI,CAAC,CAAC;MACtC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACZf,OAAO,CAACe,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC5D;IACJ,CAAC,MAAM;MACH;MACA,IAAI,CAACrB,YAAY,CAACgC,IAAI,CAACjB,IAAI,CAAC;IAChC;EACJ;;EAEA;AACJ;AACA;AACA;EACIkB,cAAcA,CAAClB,IAAI,EAAE;IACjB,MAAM;MAAEmB,CAAC,EAAEC,SAAS;MAAEC,CAAC,EAAEC;IAAU,CAAC,GAAGtB,IAAI;IAE3C,QAAQoB,SAAS;MACb,KAAK,gBAAgB;QACjB,IAAI,CAACvB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAACzB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAACzB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAACzB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAACzB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,gBAAgB;QACjB,IAAI,CAACzB,IAAI,CAAC,eAAe,EAAEyB,SAAS,CAAC;QACrC;MACJ,KAAK,cAAc;QACf,IAAI,CAACzB,IAAI,CAAC,aAAa,EAAEyB,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAACzB,IAAI,CAAC,aAAa,EAAEyB,SAAS,CAAC;QACnC;MACJ,KAAK,cAAc;QACf,IAAI,CAACzB,IAAI,CAAC,aAAa,EAAEyB,SAAS,CAAC;QACnC;MACJ,KAAK,OAAO;QACR,IAAI,CAACzB,IAAI,CAAC,OAAO,EAAEyB,SAAS,CAAC;QAC7B;MACJ;QACI,IAAI,CAACzB,IAAI,CAAC,OAAO,EAAE;UAAEM,IAAI,EAAEiB,SAAS;UAAEpB,IAAI,EAAEsB;QAAU,CAAC,CAAC;IAChE;EACJ;;EAEA;AACJ;AACA;AACA;EACIC,eAAeA,CAACvB,IAAI,EAAE;IAClB;IACA,IAAI,CAACe,IAAI,CAAC;MACNS,EAAE,EAAE,CAAC;MACLH,CAAC,EAAE,IAAI,CAAClC;IACZ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;EACIsC,WAAWA,CAACzB,IAAI,EAAE;IACd,MAAM;MAAE0B;IAAmB,CAAC,GAAG1B,IAAI,CAACqB,CAAC;IACrC,IAAI,CAACnC,iBAAiB,GAAGwC,kBAAkB;IAC3CnC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEkC,kBAAkB,CAAC;EAC7D;;EAEA;AACJ;AACA;AACA;EACIC,kBAAkBA,CAAC3B,IAAI,EAAE;IACrB,IAAI,CAACb,aAAa,GAAGiB,IAAI,CAACC,GAAG,CAAC,CAAC;EACnC;;EAEA;AACJ;AACA;AACA;EACIuB,oBAAoBA,CAAC5B,IAAI,EAAE;IACvBT,OAAO,CAACe,KAAK,CAAC,2BAA2B,CAAC;IAC1C,IAAI,CAACT,IAAI,CAAC,gBAAgB,EAAEG,IAAI,CAAC;EACrC;;EAEA;AACJ;AACA;EACI6B,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC3C,iBAAiB,EAAE;MACxB,IAAI,CAAC4C,cAAc,GAAGC,WAAW,CAAC,MAAM;QACpC,IAAI,CAAChB,IAAI,CAAC;UACNS,EAAE,EAAE,CAAC;UACLH,CAAC,EAAE,IAAI,CAAClC;QACZ,CAAC,CAAC;MACN,CAAC,EAAE,IAAI,CAACD,iBAAiB,CAAC;IAC9B;EACJ;;EAEA;AACJ;AACA;EACIwB,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACoB,cAAc,EAAE;MACrBE,aAAa,CAAC,IAAI,CAACF,cAAc,CAAC;MAClC,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;;EAEA;AACJ;AACA;EACInB,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC/B,iBAAiB,EAAE;IACxB,MAAMqD,KAAK,GAAG,IAAI,CAACnD,cAAc,GAAGoD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACvD,iBAAiB,GAAG,CAAC,CAAC;IAE3EW,OAAO,CAACC,GAAG,CAAC,sBAAsByC,KAAK,eAAe,IAAI,CAACrD,iBAAiB,IAAI,IAAI,CAACC,oBAAoB,GAAG,CAAC;IAE7GuD,UAAU,CAAC,MAAM;MACb,IAAI,CAAC,IAAI,CAACzD,WAAW,EAAE;QACnB,IAAI,CAACS,OAAO,CAAC,CAAC;MAClB;IACJ,CAAC,EAAE6C,KAAK,CAAC;EACb;;EAEA;AACJ;AACA;EACIrC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACX,YAAY,CAACoD,MAAM,GAAG,CAAC,EAAE;MACjC,MAAMC,OAAO,GAAG,IAAI,CAACrD,YAAY,CAACsD,KAAK,CAAC,CAAC;MACzC,IAAI,CAACxB,IAAI,CAACuB,OAAO,CAAC;IACtB;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIE,EAAEA,CAACzC,KAAK,EAAE0C,OAAO,EAAE;IACf,IAAI,CAAC,IAAI,CAAC1D,aAAa,CAAC2D,GAAG,CAAC3C,KAAK,CAAC,EAAE;MAChC,IAAI,CAAChB,aAAa,CAAC4D,GAAG,CAAC5C,KAAK,EAAE,EAAE,CAAC;IACrC;IACA,IAAI,CAAChB,aAAa,CAAC6D,GAAG,CAAC7C,KAAK,CAAC,CAACkB,IAAI,CAACwB,OAAO,CAAC;EAC/C;;EAEA;AACJ;AACA;AACA;AACA;EACII,GAAGA,CAAC9C,KAAK,EAAE0C,OAAO,EAAE;IAChB,MAAMK,QAAQ,GAAG,IAAI,CAAC/D,aAAa,CAAC6D,GAAG,CAAC7C,KAAK,CAAC;IAC9C,IAAI+C,QAAQ,EAAE;MACV,MAAMC,KAAK,GAAGD,QAAQ,CAACE,OAAO,CAACP,OAAO,CAAC;MACvC,IAAIM,KAAK,GAAG,CAAC,CAAC,EAAE;QACZD,QAAQ,CAACG,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC7B;IACJ;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACIlD,IAAIA,CAACE,KAAK,EAAEC,IAAI,EAAE;IACd,MAAM8C,QAAQ,GAAG,IAAI,CAAC/D,aAAa,CAAC6D,GAAG,CAAC7C,KAAK,CAAC;IAC9C,IAAI+C,QAAQ,EAAE;MACVA,QAAQ,CAACI,OAAO,CAACT,OAAO,IAAI;QACxB,IAAI;UACAA,OAAO,CAACzC,IAAI,CAAC;QACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;UACZf,OAAO,CAACe,KAAK,CAAC,wCAAwCP,KAAK,GAAG,EAAEO,KAAK,CAAC;QAC1E;MACJ,CAAC,CAAC;IACN;EACJ;;EAEA;AACJ;AACA;AACA;EACI6C,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACxE,WAAW;EAC3B;;EAEA;AACJ;AACA;AACA;EACIyE,QAAQA,CAAA,EAAG;IACP,OAAO;MACHzE,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCO,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCkE,cAAc,EAAE,IAAI,CAACpE,YAAY,CAACoD;IACtC,CAAC;EACL;AACJ;;AAEA;AACA,MAAMiB,gBAAgB,GAAG,IAAI9E,gBAAgB,CAAC,CAAC;AAE/C,eAAe8E,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}