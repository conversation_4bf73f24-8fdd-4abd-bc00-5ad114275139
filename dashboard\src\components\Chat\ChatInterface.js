import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  TextField,
  IconButton,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Button,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Send as SendIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import websocketService from '../../services/websocket';
import toast from 'react-hot-toast';

const ChatInterface = ({ serverId, channelId, serverName, channelName, onBotInvite, onCreateBot, onShowBots }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  // const [onlineUsers, setOnlineUsers] = useState([]);
  const [bots, setBots] = useState([]);
  const [showBotMenu, setShowBotMenu] = useState(false);
  const [botMenuAnchor, setBotMenuAnchor] = useState(null);
  const [showInviteBotDialog, setShowInviteBotDialog] = useState(false);
  const [availableBots, setAvailableBots] = useState([]);
  
  const messagesEndRef = useRef(null);
  const wsRef = useRef(null);

  // Connect to chat when component mounts
  useEffect(() => {
    connectToChat();

    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
      }
    };
  }, []); // Only run once on mount

  // Load data when server/channel changes
  useEffect(() => {
    if (serverId && channelId) {
      loadMessages();
      loadChannelBots();

      // Join channel if already connected
      if (wsRef.current && wsRef.current.isConnectedToServer()) {
        console.log('Joining channel:', serverId, channelId);
        wsRef.current.joinChannel(serverId, channelId);
      }
    }
  }, [serverId, channelId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const connectToChat = () => {
    // Only connect if not already connected
    if (wsRef.current && wsRef.current.isConnectedToServer()) {
      console.log('Already connected to chat, joining channel...');
      wsRef.current.joinChannel(serverId, channelId);
      return;
    }

    if (wsRef.current) {
      wsRef.current.disconnect();
    }

    wsRef.current = websocketService;

    wsRef.current.on('connected', () => {
      console.log('WebSocket connected, authenticating...');
    });

    wsRef.current.on('authenticated', (userData) => {
      console.log('Authenticated as:', userData);
      setIsConnected(true);
      toast.success('Connected to chat');

      // Join the current channel
      if (serverId && channelId) {
        wsRef.current.joinChannel(serverId, channelId);
      }
    });

    wsRef.current.on('authError', (error) => {
      console.error('Authentication error:', error);
      setIsConnected(false);
      toast.error('Authentication failed');
    });

    wsRef.current.on('channelJoined', (data) => {
      console.log('Joined channel:', data);
    });

    wsRef.current.on('disconnected', () => {
      setIsConnected(false);
      toast.error('Disconnected from chat');
    });

    wsRef.current.on('messageCreate', (message) => {
      console.log('New message:', message);
      setMessages(prev => {
        // Avoid duplicate messages
        const exists = prev.some(m => m.id === message.id);
        if (exists) {
          console.log('Message already exists, skipping...');
          return prev;
        }
        return [...prev, message];
      });
    });

    wsRef.current.on('error', (error) => {
      console.error('WebSocket error:', error);
      toast.error(error.message || 'WebSocket error');
    });

    // Connect to WebSocket with proper URL and token
    const token = localStorage.getItem('token');
    wsRef.current.connect('ws://localhost:3003/chat', token);
  };

  const loadMessages = async () => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/messages`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setMessages(data.messages || []);
      }
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const loadChannelBots = async () => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setBots(data.bots || []);
      }
    } catch (error) {
      console.error('Error loading channel bots:', error);
    }
  };

  const loadAvailableBots = async () => {
    try {
      const response = await fetch('/api/applications', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setAvailableBots(data.applications.filter(app => app.status === 'active'));
      }
    } catch (error) {
      console.error('Error loading available bots:', error);
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !isConnected) return;

    try {
      // Send via WebSocket only
      if (wsRef.current && wsRef.current.isConnectedToServer()) {
        console.log('Sending message via WebSocket:', newMessage.trim());
        wsRef.current.sendChatMessage(serverId, channelId, newMessage.trim());
        setNewMessage('');
      } else {
        toast.error('Not connected to chat server');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const inviteBot = async (botId) => {
    try {
      const response = await fetch(`/api/servers/${serverId}/channels/${channelId}/bots`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ bot_id: botId })
      });

      if (response.ok) {
        toast.success('Bot invited successfully');
        loadChannelBots();
        setShowInviteBotDialog(false);
        
        // Trigger bot invite callback for code editor integration
        if (onBotInvite) {
          const botData = availableBots.find(bot => bot.id === botId);
          onBotInvite(botData);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to invite bot');
      }
    } catch (error) {
      console.error('Error inviting bot:', error);
      toast.error('Failed to invite bot');
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getMessageTime = (timestamp) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  };

  const isBot = (author) => {
    return author.bot || bots.some(bot => bot.id === author.id);
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Chat Header - Discord Style */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          bgcolor: '#36393f',
          color: 'white'
        }}
      >
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h6" sx={{ color: '#dcddde', fontWeight: 600 }}>
            {serverName}
          </Typography>
          <Typography variant="body2" sx={{ color: '#72767d' }}>
            # {channelName}
          </Typography>
          <Chip
            label={isConnected ? '🟢 Kết nối' : '🔴 Mất kết nối'}
            color={isConnected ? 'success' : 'error'}
            size="small"
            sx={{ bgcolor: isConnected ? '#43b581' : '#f04747', color: 'white' }}
          />
        </Box>

        <Box display="flex" gap={1}>
          <Button
            startIcon={<AddIcon />}
            onClick={onCreateBot}
            size="small"
            variant="outlined"
            sx={{ color: '#dcddde', borderColor: '#72767d' }}
          >
            Create Bot
          </Button>

          <Button
            startIcon={<AddIcon />}
            onClick={() => {
              loadAvailableBots();
              setShowInviteBotDialog(true);
            }}
            size="small"
            variant="outlined"
            sx={{ color: '#dcddde', borderColor: '#72767d' }}
          >
            Invite Bot
          </Button>

          <Button
            onClick={onShowBots}
            size="small"
            variant="contained"
            sx={{ bgcolor: '#5865f2' }}
          >
            My Bots
          </Button>
        </Box>
      </Box>

      {/* Messages Area - Discord Style */}
      <Box sx={{
        flex: 1,
        overflow: 'auto',
        p: 2,
        bgcolor: '#36393f',
        '&::-webkit-scrollbar': {
          width: '8px',
        },
        '&::-webkit-scrollbar-track': {
          background: '#2f3136',
        },
        '&::-webkit-scrollbar-thumb': {
          background: '#202225',
          borderRadius: '4px',
        },
      }}>
        {messages.length === 0 ? (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            height="100%"
            sx={{ color: '#72767d' }}
          >
            <Typography variant="h6" gutterBottom>
              Chào mừng đến với #general-chat!
            </Typography>
            <Typography variant="body2">
              Đây là kênh chat chung. Hãy bắt đầu cuộc trò chuyện!
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {messages.map((message, index) => (
              <ListItem
                key={message.id || index}
                alignItems="flex-start"
                sx={{
                  py: 1,
                  px: 2,
                  '&:hover': {
                    bgcolor: 'rgba(79, 84, 92, 0.16)',
                  },
                  borderRadius: 1,
                  mb: 0.5
                }}
              >
                <ListItemAvatar>
                  <Avatar
                    sx={{
                      bgcolor: isBot(message.author) ? '#5865f2' : '#43b581',
                      width: 40,
                      height: 40
                    }}
                  >
                    {isBot(message.author) ? <BotIcon /> : <PersonIcon />}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                      <Typography
                        variant="subtitle2"
                        fontWeight="600"
                        sx={{
                          color: isBot(message.author) ? '#5865f2' : '#dcddde',
                          fontSize: '16px'
                        }}
                      >
                        {message.author.username}
                      </Typography>
                      {isBot(message.author) && (
                        <Chip
                          label="BOT"
                          size="small"
                          sx={{
                            bgcolor: '#5865f2',
                            color: 'white',
                            fontSize: '10px',
                            height: '16px',
                            fontWeight: 'bold'
                          }}
                        />
                      )}
                      <Typography
                        variant="caption"
                        sx={{
                          color: '#72767d',
                          fontSize: '12px',
                          ml: 1
                        }}
                      >
                        {getMessageTime(message.created_at)}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#dcddde',
                        whiteSpace: 'pre-wrap',
                        fontSize: '16px',
                        lineHeight: 1.375
                      }}
                    >
                      {message.content}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}
        <div ref={messagesEndRef} />
      </Box>

      {/* Message Input - Discord Style */}
      <Box sx={{
        p: 2,
        bgcolor: '#36393f',
        borderTop: '1px solid #40444b'
      }}>
        <Box
          display="flex"
          gap={1}
          sx={{
            bgcolor: '#40444b',
            borderRadius: '8px',
            p: 1,
            alignItems: 'flex-end'
          }}
        >
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder={isConnected ? "Nhắn tin tại #general-chat" : "Đang kết nối..."}
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            disabled={!isConnected}
            variant="standard"
            InputProps={{
              disableUnderline: true,
              sx: {
                color: '#dcddde',
                fontSize: '16px',
                '& input::placeholder': {
                  color: '#72767d',
                  opacity: 1
                },
                '& textarea::placeholder': {
                  color: '#72767d',
                  opacity: 1
                }
              }
            }}
            sx={{
              '& .MuiInputBase-root': {
                bgcolor: 'transparent',
              }
            }}
          />
          <IconButton
            onClick={sendMessage}
            disabled={!newMessage.trim() || !isConnected}
            sx={{
              color: isConnected && newMessage.trim() ? '#5865f2' : '#72767d',
              '&:hover': {
                bgcolor: 'rgba(88, 101, 242, 0.1)',
              }
            }}
          >
            <SendIcon />
          </IconButton>
        </Box>
        {!isConnected && (
          <Typography
            variant="caption"
            sx={{
              color: '#f04747',
              mt: 1,
              display: 'block'
            }}
          >
            ⚠️ Mất kết nối với server. Đang thử kết nối lại...
          </Typography>
        )}
      </Box>

      {/* Bot Menu */}
      <Menu
        anchorEl={botMenuAnchor}
        open={showBotMenu}
        onClose={() => setShowBotMenu(false)}
      >
        <MenuItem onClick={() => setShowInviteBotDialog(true)}>
          <AddIcon sx={{ mr: 1 }} />
          Invite Bot
        </MenuItem>
        <MenuItem>
          <SettingsIcon sx={{ mr: 1 }} />
          Channel Settings
        </MenuItem>
      </Menu>

      {/* Invite Bot Dialog */}
      <Dialog 
        open={showInviteBotDialog} 
        onClose={() => setShowInviteBotDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Invite Bot to Channel</DialogTitle>
        <DialogContent>
          <List>
            {availableBots.map((bot) => (
              <ListItem key={bot.id}>
                <ListItemAvatar>
                  <Avatar>
                    <BotIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={bot.name}
                  secondary={bot.description || 'No description'}
                />
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => inviteBot(bot.id)}
                  disabled={bots.some(b => b.id === bot.id)}
                >
                  {bots.some(b => b.id === bot.id) ? 'Added' : 'Invite'}
                </Button>
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowInviteBotDialog(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChatInterface;
