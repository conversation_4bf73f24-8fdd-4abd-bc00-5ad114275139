{"ast": null, "code": "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["every", "values", "test", "TypeError", "index", "value"], "sources": ["C:/Users/<USER>/Desktop/bot_fake_discord/dashboard/node_modules/d3-array/src/every.js"], "sourcesContent": ["export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC1C,IAAI,OAAOA,IAAI,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAC7E,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,KAAK,MAAMC,KAAK,IAAIJ,MAAM,EAAE;IAC1B,IAAI,CAACC,IAAI,CAACG,KAAK,EAAE,EAAED,KAAK,EAAEH,MAAM,CAAC,EAAE;MACjC,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}