import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import { useAuth } from './contexts/AuthContext';

// Components
import Layout from './components/Layout/Layout';
import Login from './components/Auth/Login';
import Register from './components/Auth/Register';
import Dashboard from './components/Dashboard/Dashboard';
import Applications from './components/Applications/Applications';
import ApplicationDetail from './components/Applications/ApplicationDetail';
import CodeEditor from './components/CodeEditor/CodeEditor';
import Chat from './components/Chat/Chat';
import Servers from './components/Servers/Servers';
import ServerDetail from './components/Servers/ServerDetail';
import Runtime from './components/Runtime/Runtime';
import Storage from './components/Storage/Storage';
import Logs from './components/Logs/Logs';
import Profile from './components/Profile/Profile';
import NotFound from './components/Common/NotFound';

// Protected Route component
const ProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return isAuthenticated ? children : <Navigate to="/login" replace />;
};

// Public Route component (redirect if authenticated)
const PublicRoute = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return !isAuthenticated ? children : <Navigate to="/dashboard" replace />;
};

function App() {
  return (
    <div className="App">
      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />

        {/* Protected routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<Dashboard />} />

                  {/* Chat */}
                  <Route path="/chat" element={<Chat />} />

                  {/* Applications */}
                  <Route path="/applications" element={<Applications />} />
                  <Route path="/applications/new" element={<ApplicationDetail />} />
                  <Route path="/applications/:id" element={<ApplicationDetail />} />
                  <Route path="/applications/:id/code" element={<CodeEditor />} />
                  <Route path="/applications/:id/code/:codeId" element={<CodeEditor />} />
                  <Route path="/applications/:id/runtime" element={<Runtime />} />
                  <Route path="/applications/:id/storage" element={<Storage />} />
                  <Route path="/applications/:id/logs" element={<Logs />} />

                  {/* Servers */}
                  <Route path="/servers" element={<Servers />} />
                  <Route path="/servers/:id" element={<ServerDetail />} />
                  
                  {/* Profile */}
                  <Route path="/profile" element={<Profile />} />
                  
                  {/* 404 */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </div>
  );
}

export default App;
