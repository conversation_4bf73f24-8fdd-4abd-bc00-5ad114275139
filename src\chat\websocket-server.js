const WebSocket = require('ws');
const http = require('http');
const jwtService = require('../auth/jwt');
const database = require('../database/connection');

/**
 * WebSocket server for user chat functionality
 */
class ChatWebSocketServer {
    constructor(options = {}) {
        this.port = options.port || 3003;
        this.server = null;
        this.wss = null;
        this.connections = new Map(); // Map of user ID to WebSocket connection
        this.userConnections = new Map(); // Map of connection ID to user info
    }

    /**
     * Start the chat WebSocket server
     */
    async start() {
        try {
            // Create HTTP server for WebSocket upgrade
            this.server = http.createServer();
            
            // Create WebSocket server
            this.wss = new WebSocket.Server({
                server: this.server,
                path: '/chat',
                verifyClient: this.verifyClient.bind(this)
            });

            // Handle WebSocket connections
            this.wss.on('connection', (ws, req) => {
                this.handleConnection(ws, req);
            });

            // Start HTTP server
            this.server.listen(this.port, () => {
                console.log(`Chat WebSocket server running on port ${this.port}`);
                console.log(`Chat URL: ws://localhost:${this.port}/chat`);
            });

            // Handle server errors
            this.server.on('error', (error) => {
                console.error('Chat WebSocket server error:', error);
            });

        } catch (error) {
            console.error('Failed to start chat WebSocket server:', error);
            throw error;
        }
    }

    /**
     * Stop the chat WebSocket server
     */
    async stop() {
        if (this.wss) {
            this.wss.close();
        }
        if (this.server) {
            this.server.close();
        }
        console.log('Chat WebSocket server stopped');
    }

    /**
     * Verify client connection and authenticate
     */
    verifyClient(info) {
        const { req } = info;
        console.log(`Chat WebSocket connection attempt from ${req.socket.remoteAddress}`);
        return true; // We'll authenticate after connection
    }

    /**
     * Handle new WebSocket connection
     */
    async handleConnection(ws, req) {
        const connectionId = this.generateConnectionId();
        console.log(`New chat connection: ${connectionId}`);

        // Set up connection handlers
        ws.on('message', (data) => this.handleMessage(ws, data, connectionId));
        ws.on('close', () => this.handleClose(connectionId));
        ws.on('error', (error) => this.handleError(connectionId, error));

        // Send hello message
        this.sendMessage(ws, {
            type: 'hello',
            data: {
                connectionId,
                message: 'Connected to chat server'
            }
        });
    }

    /**
     * Handle incoming messages
     */
    async handleMessage(ws, data, connectionId) {
        try {
            const message = JSON.parse(data.toString());
            console.log(`Chat message from ${connectionId}:`, message);

            switch (message.type) {
                case 'authenticate':
                    await this.handleAuthenticate(ws, message.data, connectionId);
                    break;
                case 'join_channel':
                    await this.handleJoinChannel(ws, message.data, connectionId);
                    break;
                case 'send_message':
                    await this.handleSendMessage(ws, message.data, connectionId);
                    break;
                case 'ping':
                    this.sendMessage(ws, { type: 'pong', data: { timestamp: Date.now() } });
                    break;
                default:
                    console.log(`Unknown message type: ${message.type}`);
            }
        } catch (error) {
            console.error(`Error handling message from ${connectionId}:`, error);
            this.sendMessage(ws, {
                type: 'error',
                data: { message: 'Invalid message format' }
            });
        }
    }

    /**
     * Handle user authentication
     */
    async handleAuthenticate(ws, data, connectionId) {
        try {
            const { token } = data;
            
            if (!token) {
                this.sendMessage(ws, {
                    type: 'auth_error',
                    data: { message: 'Token required' }
                });
                return;
            }

            // Verify JWT token
            const decoded = jwtService.verifyToken(token);
            
            // Get user from database
            const user = await database.get(
                'SELECT id, username, email FROM users WHERE id = ?',
                [decoded.userId]
            );

            if (!user) {
                this.sendMessage(ws, {
                    type: 'auth_error',
                    data: { message: 'User not found' }
                });
                return;
            }

            // Store user connection
            this.userConnections.set(connectionId, {
                ws,
                user,
                channels: new Set(),
                authenticated: true
            });

            this.connections.set(user.id, connectionId);

            console.log(`User authenticated: ${user.username} (${connectionId})`);

            this.sendMessage(ws, {
                type: 'authenticated',
                data: {
                    user: {
                        id: user.id,
                        username: user.username
                    }
                }
            });

        } catch (error) {
            console.error('Authentication error:', error);
            this.sendMessage(ws, {
                type: 'auth_error',
                data: { message: 'Authentication failed' }
            });
        }
    }

    /**
     * Handle joining a channel
     */
    async handleJoinChannel(ws, data, connectionId) {
        const connection = this.userConnections.get(connectionId);
        
        if (!connection || !connection.authenticated) {
            this.sendMessage(ws, {
                type: 'error',
                data: { message: 'Not authenticated' }
            });
            return;
        }

        const { serverId, channelId } = data;
        
        // Add channel to user's joined channels
        connection.channels.add(`${serverId}:${channelId}`);
        
        console.log(`User ${connection.user.username} joined channel ${channelId} in server ${serverId}`);
        
        this.sendMessage(ws, {
            type: 'channel_joined',
            data: { serverId, channelId }
        });
    }

    /**
     * Handle sending a message
     */
    async handleSendMessage(ws, data, connectionId) {
        const connection = this.userConnections.get(connectionId);
        
        if (!connection || !connection.authenticated) {
            this.sendMessage(ws, {
                type: 'error',
                data: { message: 'Not authenticated' }
            });
            return;
        }

        const { serverId, channelId, content } = data;
        const channelKey = `${serverId}:${channelId}`;
        
        if (!connection.channels.has(channelKey)) {
            this.sendMessage(ws, {
                type: 'error',
                data: { message: 'Not joined to this channel' }
            });
            return;
        }

        // Create message data
        const messageData = {
            type: 'message_create',
            data: {
                id: this.generateMessageId(),
                content,
                author: {
                    id: connection.user.id,
                    username: connection.user.username,
                    bot: false
                },
                channel_id: channelId,
                server_id: serverId,
                created_at: new Date().toISOString()
            }
        };

        // Broadcast to all users in the channel (including sender)
        this.broadcastToChannel(serverId, channelId, messageData);

        console.log(`Message sent by ${connection.user.username} in ${channelKey}: ${content}`);
    }

    /**
     * Broadcast message to all users in a channel
     */
    broadcastToChannel(serverId, channelId, message) {
        const channelKey = `${serverId}:${channelId}`;
        
        for (const [connectionId, connection] of this.userConnections) {
            if (connection.authenticated && connection.channels.has(channelKey)) {
                this.sendMessage(connection.ws, message);
            }
        }
    }

    /**
     * Handle connection close
     */
    handleClose(connectionId) {
        console.log(`Chat connection closed: ${connectionId}`);
        
        const connection = this.userConnections.get(connectionId);
        if (connection && connection.user) {
            this.connections.delete(connection.user.id);
        }
        
        this.userConnections.delete(connectionId);
    }

    /**
     * Handle connection error
     */
    handleError(connectionId, error) {
        console.error(`Chat connection error ${connectionId}:`, error);
    }

    /**
     * Send message to WebSocket
     */
    sendMessage(ws, message) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }

    /**
     * Generate unique connection ID
     */
    generateConnectionId() {
        return `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Generate unique message ID
     */
    generateMessageId() {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

module.exports = ChatWebSocketServer;
